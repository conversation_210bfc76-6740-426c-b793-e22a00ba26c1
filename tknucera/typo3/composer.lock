{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "bc5b4363d40b3391c6de13f307617765", "packages": [{"name": "and<PERSON>undsehr/aus-driver-amazon-s3", "version": "dev-mogic-master", "source": {"type": "git", "url": "https://github.com/mogic-le/aus_driver_amazon_s3.git", "reference": "58afa897552ab8dd129a4f3e3b92a12d26e12e3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mogic-le/aus_driver_amazon_s3/zipball/58afa897552ab8dd129a4f3e3b92a12d26e12e3e", "reference": "58afa897552ab8dd129a4f3e3b92a12d26e12e3e", "shasum": ""}, "require": {"aws/aws-sdk-php": "^3.288", "php": ">=7.4", "typo3/cms-core": "^11.5.6 || ^12.4.5"}, "replace": {"typo3-ter/aus-driver-amazon-s3": "self.version", "typo3-ter/aus_driver_amazon_s3": "self.version"}, "require-dev": {"phpspec/prophecy": "dev-master", "phpspec/prophecy-phpunit": "dev-master", "pluswerk/grumphp-config": "^5.0", "saschaegerer/phpstan-typo3": "^1.8.9", "ssch/typo3-rector": "^1.3.5", "typo3/testing-framework": "^7.0"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "aus_driver_amazon_s3", "cms-package-dir": "{$vendor-dir}/typo3/cms", "web-dir": ".Build/Web"}, "pluswerk/grumphp-config": {"auto-setting": true}, "grumphp": {"config-default-path": "vendor/pluswerk/grumphp-config/grumphp.yml"}}, "autoload": {"psr-4": {"AUS\\AusDriverAmazonS3\\": "Classes/"}}, "autoload-dev": {"psr-4": {"AUS\\AusDriverAmazonS3\\Tests\\": "Tests/"}}, "scripts": {"setupTest": ["mkdir -p .Build/Web/typo3conf/ && ln -sfn ../../../../ .Build/Web/typo3conf/ext"], "test": ["@setupTest", "./Build/Scripts/runTests.sh -p '8.2' -s composerInstall && ./Build/Scripts/runTests.sh -p '8.2' -s composerValidate && ./Build/Scripts/runTests.sh -p '8.2' -s unit && ./Build/Scripts/runTests.sh -p '8.2' -s lint"]}, "license": ["LGPL-3.0-or-later"], "description": "Provides a FAL driver for the Amazon Web Service AWS S3.", "support": {"source": "https://github.com/andersundsehr/aus_driver_amazon_s3.git", "issues": "https://github.com/andersundsehr/aus_driver_amazon_s3/issues", "docs": "https://docs.typo3.org/p/andersundsehr/aus-driver-amazon-s3/master/en-us/"}, "time": "2024-06-17T14:21:11+00:00"}, {"name": "aws/aws-crt-php", "version": "v1.2.6", "source": {"type": "git", "url": "https://github.com/awslabs/aws-crt-php.git", "reference": "a63485b65b6b3367039306496d49737cf1995408"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/awslabs/aws-crt-php/zipball/a63485b65b6b3367039306496d49737cf1995408", "reference": "a63485b65b6b3367039306496d49737cf1995408", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"phpunit/phpunit": "^4.8.35||^5.6.3||^9.5", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"ext-awscrt": "Make sure you install awscrt native extension to use any of the functionality."}, "type": "library", "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "AWS SDK Common Runtime Team", "email": "<EMAIL>"}], "description": "AWS Common Runtime for PHP", "homepage": "https://github.com/awslabs/aws-crt-php", "keywords": ["amazon", "aws", "crt", "sdk"], "support": {"issues": "https://github.com/awslabs/aws-crt-php/issues", "source": "https://github.com/awslabs/aws-crt-php/tree/v1.2.6"}, "time": "2024-06-13T17:21:28+00:00"}, {"name": "aws/aws-sdk-php", "version": "3.314.8", "source": {"type": "git", "url": "https://github.com/aws/aws-sdk-php.git", "reference": "cec444ca2e86dade32886d586ac55838779e2ae2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aws/aws-sdk-php/zipball/cec444ca2e86dade32886d586ac55838779e2ae2", "reference": "cec444ca2e86dade32886d586ac55838779e2ae2", "shasum": ""}, "require": {"aws/aws-crt-php": "^1.2.3", "ext-json": "*", "ext-pcre": "*", "ext-simplexml": "*", "guzzlehttp/guzzle": "^6.5.8 || ^7.4.5", "guzzlehttp/promises": "^1.4.0 || ^2.0", "guzzlehttp/psr7": "^1.9.1 || ^2.4.5", "mtdowling/jmespath.php": "^2.6", "php": ">=7.2.5", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"andrewsville/php-token-reflection": "^1.4", "aws/aws-php-sns-message-validator": "~1.0", "behat/behat": "~3.0", "composer/composer": "^1.10.22", "dms/phpunit-arraysubset-asserts": "^0.4.0", "doctrine/cache": "~1.4", "ext-dom": "*", "ext-openssl": "*", "ext-pcntl": "*", "ext-sockets": "*", "nette/neon": "^2.3", "paragonie/random_compat": ">= 2", "phpunit/phpunit": "^5.6.3 || ^8.5 || ^9.5", "psr/cache": "^1.0", "psr/simple-cache": "^1.0", "sebastian/comparator": "^1.2.3 || ^4.0", "yoast/phpunit-polyfills": "^1.0"}, "suggest": {"aws/aws-php-sns-message-validator": "To validate incoming SNS notifications", "doctrine/cache": "To use the DoctrineCacheAdapter", "ext-curl": "To send requests using cURL", "ext-openssl": "Allows working with CloudFront private distributions and verifying received SNS messages", "ext-sockets": "To use client-side monitoring"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"files": ["src/functions.php"], "psr-4": {"Aws\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "Amazon Web Services", "homepage": "http://aws.amazon.com"}], "description": "AWS SDK for PHP - Use Amazon Web Services in your PHP project", "homepage": "http://aws.amazon.com/sdkforphp", "keywords": ["amazon", "aws", "cloud", "dynamodb", "ec2", "glacier", "s3", "sdk"], "support": {"forum": "https://forums.aws.amazon.com/forum.jspa?forumID=80", "issues": "https://github.com/aws/aws-sdk-php/issues", "source": "https://github.com/aws/aws-sdk-php/tree/3.314.8"}, "time": "2024-06-25T18:13:28+00:00"}, {"name": "b13/container", "version": "2.3.6", "source": {"type": "git", "url": "https://github.com/b13/container.git", "reference": "abaab9486ccc2a145080d2c604510aec3421914a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/b13/container/zipball/abaab9486ccc2a145080d2c604510aec3421914a", "reference": "abaab9486ccc2a145080d2c604510aec3421914a", "shasum": ""}, "require": {"doctrine/dbal": "~2.13.1 || ~3.4", "typo3/cms-backend": "^11.5 || ^12.4 || ^10.4"}, "replace": {"typo3-ter/container": "self.version"}, "require-dev": {"b13/container-example": "dev-master", "codeception/codeception": "^5.0 || ^4.1", "codeception/module-asserts": "^3.0 || ^1.0", "codeception/module-db": "^3.0 || ^1.0", "codeception/module-webdriver": "^3.1 || ^1.0", "friendsofphp/php-cs-fixer": "~3.9.0", "ichhabrecht/content-defender": "^3.2", "phpstan/phpstan": "^1.4.8", "typo3/cms-fluid-styled-content": "^11.5 || ^12.4", "typo3/cms-info": "^11.5 || ^12.4", "typo3/cms-install": "^11.5 || ^12.4", "typo3/cms-workspaces": "^11.5 || ^12.4", "typo3/coding-standards": "^0.5.0", "typo3/testing-framework": "^7"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"cms-package-dir": "{$vendor-dir}/typo3/cms", "web-dir": ".Build/Web", "app-dir": ".Build", "extension-key": "container"}}, "autoload": {"psr-4": {"B13\\Container\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Create Custom Container Content Elements for TYPO3", "homepage": "https://b13.com", "support": {"issues": "https://github.com/b13/container/issues", "source": "https://github.com/b13/container/tree/2.3.6"}, "time": "2024-03-28T17:28:19+00:00"}, {"name": "b13/masi", "version": "2.0.3", "source": {"type": "git", "url": "https://github.com/b13/masi.git", "reference": "a920e2a5938788c98b6d699a7595e074b69da71e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/b13/masi/zipball/a920e2a5938788c98b6d699a7595e074b69da71e", "reference": "a920e2a5938788c98b6d699a7595e074b69da71e", "shasum": ""}, "require": {"php": "^7.4 || ~8.0", "typo3/cms-core": "^10.4 || ^11.5 || ^12.0"}, "replace": {"typo3-ter/masi": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "masi"}}, "autoload": {"psr-4": {"B13\\Masi\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/bmack/", "role": "Developer"}], "description": "TYPO3 Extend URL generation to optionally include storage folders.", "homepage": "https://github.com/b13/masi", "keywords": ["slug", "typo3", "url"], "support": {"issues": "https://github.com/b13/masi/issues", "source": "https://github.com/b13/masi/tree/2.0.3"}, "time": "2023-12-06T08:42:50+00:00"}, {"name": "bacon/bacon-qr-code", "version": "2.0.8", "source": {"type": "git", "url": "https://github.com/Bacon/BaconQrCode.git", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Bacon/BaconQrCode/zipball/8674e51bb65af933a5ffaf1c308a660387c35c22", "reference": "8674e51bb65af933a5ffaf1c308a660387c35c22", "shasum": ""}, "require": {"dasprid/enum": "^1.0.3", "ext-iconv": "*", "php": "^7.1 || ^8.0"}, "require-dev": {"phly/keep-a-changelog": "^2.1", "phpunit/phpunit": "^7 | ^8 | ^9", "spatie/phpunit-snapshot-assertions": "^4.2.9", "squizlabs/php_codesniffer": "^3.4"}, "suggest": {"ext-imagick": "to generate QR code images"}, "type": "library", "autoload": {"psr-4": {"BaconQrCode\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "BaconQrCode is a QR code generator for PHP.", "homepage": "https://github.com/Bacon/BaconQrCode", "support": {"issues": "https://github.com/Bacon/BaconQrCode/issues", "source": "https://github.com/Bacon/BaconQrCode/tree/2.0.8"}, "time": "2022-12-07T17:46:57+00:00"}, {"name": "christian-riesen/base32", "version": "1.6.0", "source": {"type": "git", "url": "https://github.com/ChristianRiesen/base32.git", "reference": "2e82dab3baa008e24a505649b0d583c31d31e894"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ChristianRiesen/base32/zipball/2e82dab3baa008e24a505649b0d583c31d31e894", "reference": "2e82dab3baa008e24a505649b0d583c31d31e894", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^2.17", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^8.5.13 || ^9.5"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Base32\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://christianriesen.com", "role": "Developer"}], "description": "Base32 encoder/decoder according to RFC 4648", "homepage": "https://github.com/ChristianRiesen/base32", "keywords": ["base32", "decode", "encode", "rfc4648"], "support": {"issues": "https://github.com/ChristianRiesen/base32/issues", "source": "https://github.com/ChristianRiesen/base32/tree/1.6.0"}, "time": "2021-02-26T10:19:33+00:00"}, {"name": "cweagans/composer-patches", "version": "1.7.3", "source": {"type": "git", "url": "https://github.com/cweagans/composer-patches.git", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweagans/composer-patches/zipball/e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "reference": "e190d4466fe2b103a55467dfa83fc2fecfcaf2db", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.0"}, "require-dev": {"composer/composer": "~1.0 || ~2.0", "phpunit/phpunit": "~4.6"}, "type": "composer-plugin", "extra": {"class": "cweagans\\Composer\\Patches"}, "autoload": {"psr-4": {"cweagans\\Composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Provides a way to patch Composer packages.", "support": {"issues": "https://github.com/cweagans/composer-patches/issues", "source": "https://github.com/cweagans/composer-patches/tree/1.7.3"}, "time": "2022-12-20T22:53:13+00:00"}, {"name": "dasprid/enum", "version": "1.0.5", "source": {"type": "git", "url": "https://github.com/DASPRiD/Enum.git", "reference": "6faf451159fb8ba4126b925ed2d78acfce0dc016"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/DASPRiD/Enum/zipball/6faf451159fb8ba4126b925ed2d78acfce0dc016", "reference": "6faf451159fb8ba4126b925ed2d78acfce0dc016", "shasum": ""}, "require": {"php": ">=7.1 <9.0"}, "require-dev": {"phpunit/phpunit": "^7 | ^8 | ^9", "squizlabs/php_codesniffer": "*"}, "type": "library", "autoload": {"psr-4": {"DASPRiD\\Enum\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-2-<PERSON><PERSON>"], "authors": [{"name": "<PERSON> 'DASPRiD'", "email": "<EMAIL>", "homepage": "https://dasprids.de/", "role": "Developer"}], "description": "PHP 7.1 enum implementation", "keywords": ["enum", "map"], "support": {"issues": "https://github.com/DASPRiD/Enum/issues", "source": "https://github.com/DASPRiD/Enum/tree/1.0.5"}, "time": "2023-08-25T16:18:39+00:00"}, {"name": "doctrine/annotations", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/annotations.git", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/annotations/zipball/e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "reference": "e157ef3f3124bbf6fe7ce0ffd109e8a8ef284e7f", "shasum": ""}, "require": {"doctrine/lexer": "^2 || ^3", "ext-tokenizer": "*", "php": "^7.2 || ^8.0", "psr/cache": "^1 || ^2 || ^3"}, "require-dev": {"doctrine/cache": "^2.0", "doctrine/coding-standard": "^10", "phpstan/phpstan": "^1.8.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "symfony/cache": "^5.4 || ^6", "vimeo/psalm": "^4.10"}, "suggest": {"php": "PHP 8.0 or higher comes with attributes, a native replacement for annotations"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Annotations\\": "lib/Doctrine/Common/Annotations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "Docblock Annotations Parser", "homepage": "https://www.doctrine-project.org/projects/annotations.html", "keywords": ["annotations", "doc<PERSON>", "parser"], "support": {"issues": "https://github.com/doctrine/annotations/issues", "source": "https://github.com/doctrine/annotations/tree/2.0.1"}, "time": "2023-02-02T22:02:53+00:00"}, {"name": "doctrine/cache", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/doctrine/cache.git", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/cache/zipball/1ca8f21980e770095a31456042471a57bc4c68fb", "reference": "1ca8f21980e770095a31456042471a57bc4c68fb", "shasum": ""}, "require": {"php": "~7.1 || ^8.0"}, "conflict": {"doctrine/common": ">2.2,<2.4"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/coding-standard": "^9", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psr/cache": "^1.0 || ^2.0 || ^3.0", "symfony/cache": "^4.4 || ^5.4 || ^6", "symfony/var-exporter": "^4.4 || ^5.4 || ^6"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Cache\\": "lib/Doctrine/Common/Cache"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Cache library is a popular cache implementation that supports many different drivers such as redis, memcache, apc, mongodb and others.", "homepage": "https://www.doctrine-project.org/projects/cache.html", "keywords": ["abstraction", "apcu", "cache", "caching", "couchdb", "memcached", "php", "redis", "xcache"], "support": {"issues": "https://github.com/doctrine/cache/issues", "source": "https://github.com/doctrine/cache/tree/2.2.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fcache", "type": "tidelift"}], "time": "2022-05-20T20:07:39+00:00"}, {"name": "doctrine/dbal", "version": "3.8.6", "source": {"type": "git", "url": "https://github.com/doctrine/dbal.git", "reference": "b7411825cf7efb7e51f9791dea19d86e43b399a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/dbal/zipball/b7411825cf7efb7e51f9791dea19d86e43b399a1", "reference": "b7411825cf7efb7e51f9791dea19d86e43b399a1", "shasum": ""}, "require": {"composer-runtime-api": "^2", "doctrine/cache": "^1.11|^2.0", "doctrine/deprecations": "^0.5.3|^1", "doctrine/event-manager": "^1|^2", "php": "^7.4 || ^8.0", "psr/cache": "^1|^2|^3", "psr/log": "^1|^2|^3"}, "require-dev": {"doctrine/coding-standard": "12.0.0", "fig/log-test": "^1", "jetbrains/phpstorm-stubs": "2023.1", "phpstan/phpstan": "1.11.5", "phpstan/phpstan-strict-rules": "^1.6", "phpunit/phpunit": "9.6.19", "psalm/plugin-phpunit": "0.18.4", "slevomat/coding-standard": "8.13.1", "squizlabs/php_codesniffer": "3.10.1", "symfony/cache": "^5.4|^6.0|^7.0", "symfony/console": "^4.4|^5.4|^6.0|^7.0", "vimeo/psalm": "4.30.0"}, "suggest": {"symfony/console": "For helpful console commands such as SQL execution and import of files."}, "bin": ["bin/doctrine-dbal"], "type": "library", "autoload": {"psr-4": {"Doctrine\\DBAL\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Powerful PHP database abstraction layer (DBAL) with many features for database schema introspection and management.", "homepage": "https://www.doctrine-project.org/projects/dbal.html", "keywords": ["abstraction", "database", "db2", "dbal", "ma<PERSON>b", "mssql", "mysql", "oci8", "oracle", "pdo", "pgsql", "postgresql", "queryobject", "sasql", "sql", "sqlite", "sqlserver", "sqlsrv"], "support": {"issues": "https://github.com/doctrine/dbal/issues", "source": "https://github.com/doctrine/dbal/tree/3.8.6"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fdbal", "type": "tidelift"}], "time": "2024-06-19T10:38:17+00:00"}, {"name": "doctrine/deprecations", "version": "1.1.3", "source": {"type": "git", "url": "https://github.com/doctrine/deprecations.git", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/deprecations/zipball/dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "reference": "dfbaa3c2d2e9a9df1118213f3b8b0c597bb99fab", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"doctrine/coding-standard": "^9", "phpstan/phpstan": "1.4.10 || 1.10.15", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^7.5 || ^8.5 || ^9.5", "psalm/plugin-phpunit": "0.18.4", "psr/log": "^1 || ^2 || ^3", "vimeo/psalm": "4.30.0 || 5.12.0"}, "suggest": {"psr/log": "Allows logging deprecations via PSR-3 logger implementation"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Deprecations\\": "lib/Doctrine/Deprecations"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "A small layer on top of trigger_error(E_USER_DEPRECATED) or PSR-3 logging with options to disable all deprecations or selectively for packages.", "homepage": "https://www.doctrine-project.org/", "support": {"issues": "https://github.com/doctrine/deprecations/issues", "source": "https://github.com/doctrine/deprecations/tree/1.1.3"}, "time": "2024-01-30T19:34:25+00:00"}, {"name": "doctrine/event-manager", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/event-manager.git", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/event-manager/zipball/b680156fa328f1dfd874fd48c7026c41570b9c6e", "reference": "b680156fa328f1dfd874fd48c7026c41570b9c6e", "shasum": ""}, "require": {"php": "^8.1"}, "conflict": {"doctrine/common": "<2.9"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.8.8", "phpunit/phpunit": "^10.5", "vimeo/psalm": "^5.24"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "description": "The Doctrine Event Manager is a simple PHP event system that was built to be used with the various Doctrine projects.", "homepage": "https://www.doctrine-project.org/projects/event-manager.html", "keywords": ["event", "event dispatcher", "event manager", "event system", "events"], "support": {"issues": "https://github.com/doctrine/event-manager/issues", "source": "https://github.com/doctrine/event-manager/tree/2.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Fevent-manager", "type": "tidelift"}], "time": "2024-05-22T20:47:39+00:00"}, {"name": "doctrine/instantiator", "version": "2.0.0", "source": {"type": "git", "url": "https://github.com/doctrine/instantiator.git", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/instantiator/zipball/c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "reference": "c6222283fa3f4ac679f8b9ced9a4e23f163e80d0", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^11", "ext-pdo": "*", "ext-phar": "*", "phpbench/phpbench": "^1.2", "phpstan/phpstan": "^1.9.4", "phpstan/phpstan-phpunit": "^1.3", "phpunit/phpunit": "^9.5.27", "vimeo/psalm": "^5.4"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Instantiator\\": "src/Doctrine/Instantiator/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://ocramius.github.io/"}], "description": "A small, lightweight utility to instantiate objects in PHP without invoking their constructors", "homepage": "https://www.doctrine-project.org/projects/instantiator.html", "keywords": ["constructor", "instantiate"], "support": {"issues": "https://github.com/doctrine/instantiator/issues", "source": "https://github.com/doctrine/instantiator/tree/2.0.0"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Finstantiator", "type": "tidelift"}], "time": "2022-12-30T00:23:10+00:00"}, {"name": "doctrine/lexer", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/doctrine/lexer.git", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/doctrine/lexer/zipball/31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "reference": "31ad66abc0fc9e1a1f2d9bc6a42668d2fbbcd6dd", "shasum": ""}, "require": {"php": "^8.1"}, "require-dev": {"doctrine/coding-standard": "^12", "phpstan/phpstan": "^1.10", "phpunit/phpunit": "^10.5", "psalm/plugin-phpunit": "^0.18.3", "vimeo/psalm": "^5.21"}, "type": "library", "autoload": {"psr-4": {"Doctrine\\Common\\Lexer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "guil<PERSON><PERSON><EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "schmitt<PERSON><EMAIL>"}], "description": "PHP Doctrine Lexer parser library that can be used in Top-Down, Recursive Descent Parsers.", "homepage": "https://www.doctrine-project.org/projects/lexer.html", "keywords": ["annotations", "doc<PERSON>", "lexer", "parser", "php"], "support": {"issues": "https://github.com/doctrine/lexer/issues", "source": "https://github.com/doctrine/lexer/tree/3.0.1"}, "funding": [{"url": "https://www.doctrine-project.org/sponsorship.html", "type": "custom"}, {"url": "https://www.patreon.com/phpdoctrine", "type": "patreon"}, {"url": "https://tidelift.com/funding/github/packagist/doctrine%2Flexer", "type": "tidelift"}], "time": "2024-02-05T11:56:58+00:00"}, {"name": "egulias/email-validator", "version": "4.0.2", "source": {"type": "git", "url": "https://github.com/egulias/EmailValidator.git", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/egulias/EmailValidator/zipball/ebaaf5be6c0286928352e054f2d5125608e5405e", "reference": "ebaaf5be6c0286928352e054f2d5125608e5405e", "shasum": ""}, "require": {"doctrine/lexer": "^2.0 || ^3.0", "php": ">=8.1", "symfony/polyfill-intl-idn": "^1.26"}, "require-dev": {"phpunit/phpunit": "^10.2", "vimeo/psalm": "^5.12"}, "suggest": {"ext-intl": "PHP Internationalization Libraries are required to use the SpoofChecking validation"}, "type": "library", "extra": {"branch-alias": {"dev-master": "4.0.x-dev"}}, "autoload": {"psr-4": {"Egulias\\EmailValidator\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}], "description": "A library for validating emails against several RFCs", "homepage": "https://github.com/egulias/EmailValidator", "keywords": ["email", "emailvalidation", "emailvalidator", "validation", "validator"], "support": {"issues": "https://github.com/egulias/EmailValidator/issues", "source": "https://github.com/egulias/EmailValidator/tree/4.0.2"}, "funding": [{"url": "https://github.com/egulias", "type": "github"}], "time": "2023-10-06T06:47:41+00:00"}, {"name": "enshrined/svg-sanitize", "version": "0.18.0", "source": {"type": "git", "url": "https://github.com/darylldoyle/svg-sanitizer.git", "reference": "6a2c069dab3843ca4d887ff09c972fc7033888d0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/darylldoyle/svg-sanitizer/zipball/6a2c069dab3843ca4d887ff09c972fc7033888d0", "reference": "6a2c069dab3843ca4d887ff09c972fc7033888d0", "shasum": ""}, "require": {"ext-dom": "*", "ext-libxml": "*", "php": "^5.6 || ^7.0 || ^8.0"}, "require-dev": {"phpunit/phpunit": "^5.7 || ^6.5 || ^8.5"}, "type": "library", "autoload": {"psr-4": {"enshrined\\svgSanitize\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An SVG sanitizer for PHP", "support": {"issues": "https://github.com/darylldoyle/svg-sanitizer/issues", "source": "https://github.com/darylldoyle/svg-sanitizer/tree/0.18.0"}, "time": "2024-02-22T17:51:05+00:00"}, {"name": "firebase/php-jwt", "version": "v6.10.1", "source": {"type": "git", "url": "https://github.com/firebase/php-jwt.git", "reference": "500501c2ce893c824c801da135d02661199f60c5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/firebase/php-jwt/zipball/500501c2ce893c824c801da135d02661199f60c5", "reference": "500501c2ce893c824c801da135d02661199f60c5", "shasum": ""}, "require": {"php": "^8.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.4", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5", "psr/cache": "^2.0||^3.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0"}, "suggest": {"ext-sodium": "Support EdDSA (Ed25519) signatures", "paragonie/sodium_compat": "Support EdDSA (Ed25519) signatures when libsodium is not present"}, "type": "library", "autoload": {"psr-4": {"Firebase\\JWT\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "A simple library to encode and decode JSON Web Tokens (JWT) in PHP. Should conform to the current spec.", "homepage": "https://github.com/firebase/php-jwt", "keywords": ["jwt", "php"], "support": {"issues": "https://github.com/firebase/php-jwt/issues", "source": "https://github.com/firebase/php-jwt/tree/v6.10.1"}, "time": "2024-05-18T18:05:11+00:00"}, {"name": "fluidtypo3/flux", "version": "10.0.10", "source": {"type": "git", "url": "https://github.com/FluidTYPO3/flux.git", "reference": "f22ebddd4347aea16109ad74970f42cdbd6c96d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FluidTYPO3/flux/zipball/f22ebddd4347aea16109ad74970f42cdbd6c96d6", "reference": "f22ebddd4347aea16109ad74970f42cdbd6c96d6", "shasum": ""}, "require": {"ext-dom": "*", "ext-json": "*", "ext-pdo": "*", "php": "^7.4.0 || ^8", "typo3/cms-backend": "^10 || ^11 || ^12 || dev-master", "typo3/cms-core": "^10 || ^11 || ^12 || dev-master", "typo3/cms-fluid": "^10 || ^11 || ^12 || dev-master", "typo3/cms-frontend": "^10 || ^11 || ^12 || dev-master", "typo3/cms-recordlist": "^10 || ^11 || ^12 || dev-master"}, "replace": {"typo3-ter/flux": "self.version"}, "require-dev": {"mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpspec/prophecy": "*", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^5.7 || ^9.5", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"fluidtypo3/vhs": "ViewHelper library for Fluid templates."}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "flux"}}, "autoload": {"psr-4": {"FluidTYPO3\\Flux\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0+"], "description": "The flux package from FluidTYPO3", "homepage": "http://fluidtypo3.org", "support": {"issues": "https://github.com/FluidTYPO3/flux/issues", "source": "https://github.com/FluidTYPO3/flux/tree/10.0.10"}, "time": "2024-05-07T11:37:40+00:00"}, {"name": "fluidtypo3/vhs", "version": "7.0.3", "source": {"type": "git", "url": "https://github.com/FluidTYPO3/vhs.git", "reference": "e0df945780fea29c8d2828ac96736b9d24588619"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/FluidTYPO3/vhs/zipball/e0df945780fea29c8d2828ac96736b9d24588619", "reference": "e0df945780fea29c8d2828ac96736b9d24588619", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^7.4.0 || ^8", "typo3/cms-backend": "^10 || ^11 || ^12 || dev-master", "typo3/cms-core": "^10 || ^11 || ^12 || dev-master", "typo3/cms-extbase": "^10 || ^11 || ^12 || dev-master", "typo3/cms-fluid": "^10 || ^11 || ^12 || dev-master", "typo3/cms-frontend": "^10 || ^11 || ^12 || dev-master"}, "replace": {"typo3-ter/vhs": "self.version"}, "require-dev": {"fluidtypo3/development": "^5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpstan/phpstan": "^1.8", "phpunit/phpunit": "^5.7 || ^9.5", "squizlabs/php_codesniffer": "^3.7"}, "suggest": {"ext-json": "Enable use of v:format.json.encode and v:format.json.decode", "ext-tidy": "Allows to make use of the tidy ViewHelper v:format.tidy", "ext-zlib": "Enable use of v:format.placeholder.lipsum with default lipsum text"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "vhs"}}, "autoload": {"psr-4": {"FluidTYPO3\\Vhs\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "This is a collection of ViewHelpers for performing rendering tasks that are not natively provided by TYPO3's Fluid templating engine.", "homepage": "https://fluidtypo3.org", "keywords": ["TYPO3 CMS", "fedext", "fluid", "templating", "utility", "viewhelper"], "support": {"chat": "https://typo3.slack.com/archives/C79562JES", "docs": "https://viewhelpers.fluidtypo3.org/fluidtypo3/vhs/", "issues": "https://github.com/FluidTYPO3/vhs/issues", "source": "https://github.com/FluidTYPO3/vhs"}, "time": "2024-04-23T12:00:30+00:00"}, {"name": "fullstackfreelancer/ce-timeline", "version": "4.0.1", "source": {"type": "git", "url": "https://github.com/fullstackfreelancer/ce_timeline.git", "reference": "b32eab825d78460ca616a78db4462a5f5bbbf417"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/fullstackfreelancer/ce_timeline/zipball/b32eab825d78460ca616a78db4462a5f5bbbf417", "reference": "b32eab825d78460ca616a78db4462a5f5bbbf417", "shasum": ""}, "require": {"typo3/cms-core": "^11.8 || ^12.0"}, "replace": {"simonkoehler/ce-timeline": "self.version", "typo3-ter/ce-timeline": "self.version"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "ce_timeline"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-3.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://simonkoehler.com", "role": "Developer"}], "description": "Enables a new content element of type Timeline", "homepage": "https://simonkoehler.com", "keywords": ["addon", "chronologic", "chronologisch", "css", "extension", "frontend", "history", "plugin", "timeline", "typo3", "zeitleiste"], "support": {"docs": "https://simonkoehler.com/contact", "email": "<EMAIL>", "issues": "https://github.com/fullstackfreelancer/ce_timeline/issues", "source": "https://github.com/fullstackfreelancer/ce_timeline/tree/v4.0.1"}, "funding": [{"url": "https://paypal.me/fullstackfreelancer", "type": "paypal"}], "time": "2023-11-14T12:39:10+00:00"}, {"name": "guzzlehttp/guzzle", "version": "7.8.1", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/41042bc7ab002487b876a0683fc8dce04ddce104", "reference": "41042bc7ab002487b876a0683fc8dce04ddce104", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5.3 || ^2.0.1", "guzzlehttp/psr7": "^1.9.1 || ^2.5.1", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "ext-curl": "*", "php-http/client-integration-tests": "dev-master#2c025848417c1135031fdf9c728ee53d0a7ceaee as 3.0.999", "php-http/message-factory": "^1.1", "phpunit/phpunit": "^8.5.36 || ^9.6.15", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/7.8.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2023-12-03T20:35:24+00:00"}, {"name": "guzzlehttp/promises", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/bbff78d96034045e58e13dedd6ad91b5d1253223", "reference": "bbff78d96034045e58e13dedd6ad91b5d1253223", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/2.0.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2023-12-03T20:19:20+00:00"}, {"name": "guzzlehttp/psr7", "version": "2.6.2", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/45b30f99ac27b5ca93cb4831afe16285f57b8221", "reference": "45b30f99ac27b5ca93cb4831afe16285f57b8221", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.2", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.36 || ^9.6.15"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.6.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2023-12-03T20:05:35+00:00"}, {"name": "helhum/config-loader", "version": "v0.12.5", "source": {"type": "git", "url": "https://github.com/helhum/config-loader.git", "reference": "f761ab3fcf888b9d17d679d94b14aca24fe6bfab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/helhum/config-loader/zipball/f761ab3fcf888b9d17d679d94b14aca24fe6bfab", "reference": "f761ab3fcf888b9d17d679d94b14aca24fe6bfab", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"php-parallel-lint/php-parallel-lint": "^1.2", "phpunit/phpunit": "^8.5", "symfony/yaml": "^2.8 || ^3.3 || ^4.0 || ^5.0"}, "suggest": {"ext-yaml": "For improved performance when parsing yaml files you should use the PECL YAML Parser php extension", "symfony/yaml": "To be able to parse yaml files, you will need symfony/yaml"}, "type": "library", "extra": {"branch-alias": {"dev-main": "1.0.x-dev"}}, "autoload": {"psr-4": {"Helhum\\ConfigLoader\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "Generic config loader with context and environment support.", "support": {"issues": "https://github.com/helhum/config-loader/issues", "source": "https://github.com/helhum/config-loader/tree/v0.12.5"}, "funding": [{"url": "https://www.paypal.me/helhum/19.99", "type": "custom"}, {"url": "https://github.com/helhum", "type": "github"}], "time": "2022-02-21T15:32:39+00:00"}, {"name": "helhum/php-error-reporting", "version": "v1.0.1", "source": {"type": "git", "url": "https://github.com/helhum/php-error-reporting.git", "reference": "c9f1a0b6fedf7e641bce23b65eeeea946a6f7eb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/helhum/php-error-reporting/zipball/c9f1a0b6fedf7e641bce23b65eeeea946a6f7eb9", "reference": "c9f1a0b6fedf7e641bce23b65eeeea946a6f7eb9", "shasum": ""}, "require": {"php": ">=7.4.1"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "phpstan/phpstan": "^1.0", "phpstan/phpstan-deprecation-rules": "^1.0", "phpstan/phpstan-doctrine": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.0"}, "type": "library", "autoload": {"psr-4": {"ErrorReporting\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Create ErrorException with clean trace from PHP error handler errors, inspired by Sentry PHP SDK", "homepage": "https://helhum.io", "keywords": ["error reporting", "sentry"], "support": {"issues": "https://github.com/helhum/php-error-reporting", "source": "https://github.com/helhum/php-error-reporting/tree/v1.0.1"}, "time": "2023-01-12T14:49:10+00:00"}, {"name": "helhum/typo3-console", "version": "v8.1.1", "source": {"type": "git", "url": "https://github.com/TYPO3-Console/TYPO3-Console.git", "reference": "7916d393fe18b441d0e339fde6c9247fd49a5a58"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-Console/TYPO3-Console/zipball/7916d393fe18b441d0e339fde6c9247fd49a5a58", "reference": "7916d393fe18b441d0e339fde6c9247fd49a5a58", "shasum": ""}, "require": {"composer-runtime-api": "^2.1", "helhum/config-loader": ">=0.9 <0.13", "helhum/php-error-reporting": "^1.0", "php": ">=8.1", "symfony/console": "^5.4 || ^6.4 || ^7.0", "symfony/process": "^5.4 || ^6.4 || ^7.0", "typo3/cms-backend": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-composer-installers": "^4.0@rc || >=5.0", "typo3/cms-core": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-extbase": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-extensionmanager": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-fluid": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-frontend": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-install": "^11.5.26 || ^12.4 || dev-main"}, "replace": {"typo3-ter/typo3-console": "self.version"}, "require-dev": {"dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.2", "php-parallel-lint/php-parallel-lint": "^1.2", "phpspec/prophecy": "^1.15", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^9.5.25", "symfony/expression-language": "^5.4 || ^6.4 || ^7.0", "symfony/filesystem": "^5.4 || ^6.4 || ^7.0", "typo3-console/create-reference-command": "^1.0", "typo3-console/sql-command": "^1.0", "typo3/cms-filemetadata": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-recordlist": "^11.5.26 || ^12.4 || dev-main", "typo3/cms-reports": "^11.5.26 || ^12.4 || dev-main"}, "type": "typo3-cms-extension", "extra": {"branch-alias": {"dev-main": "8.1.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "Helhum\\Typo3Console\\ServiceProvider", "protected": true, "partOfMinimalUsableSystem": true}, "extension-key": "typo3_console", "ignore-as-root": false}}, "autoload": {"psr-4": {"Helhum\\Typo3Console\\": ["Classes/Console/", "Classes/Compatibility/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://helhum.io", "role": "Developer"}], "description": "A reliable and powerful command line interface for TYPO3 CMS", "homepage": "https://insight.helhum.io/post/104528981610/about-the-beauty-and-power-of-typo3-console", "keywords": ["cli", "command", "commandline", "console", "typo3"], "support": {"docs": "https://docs.typo3.org/p/helhum/typo3-console/main/en-us/", "issues": "https://github.com/TYPO3-Console/TYPO3-Console/issues", "source": "https://github.com/TYPO3-Console/TYPO3-Console"}, "funding": [{"url": "https://www.paypal.me/helhum/19.99", "type": "custom"}, {"url": "https://github.com/helhum", "type": "github"}], "time": "2024-02-06T17:48:27+00:00"}, {"name": "ichhabrecht/content-defender", "version": "3.4.3", "source": {"type": "git", "url": "https://github.com/IchHabRecht/content_defender.git", "reference": "50b072c8477d779aac00ebc10d6b52d8edfd9099"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/IchHabRecht/content_defender/zipball/50b072c8477d779aac00ebc10d6b52d8edfd9099", "reference": "50b072c8477d779aac00ebc10d6b52d8edfd9099", "shasum": ""}, "require": {"php": "^7.4 || ^8.0", "typo3/cms-backend": "^10.4 || ^11.5 || ^12.4", "typo3/cms-core": "^10.4 || ^11.5 || ^12.4"}, "conflict": {"psr/container": "<1.1", "typo3/testing-framework": ">=7.0.0 <7.0.4"}, "replace": {"typo3-ter/content-defender": "self.version"}, "require-dev": {"doctrine/dbal": "^2.11 || ^3.8", "php-parallel-lint/php-parallel-lint": "^1.4", "phpspec/prophecy": "^1.12.1", "phpspec/prophecy-phpunit": "^2.0", "phpunit/phpunit": "^8.4 || ^9.0", "typo3/cms-fluid-styled-content": "^10.4 || ^11.5 || ^12.4", "typo3/cms-indexed-search": "^10.4 || ^11.5 || ^12.4", "typo3/cms-workspaces": "^10.4 || ^11.5 || ^12.4", "typo3/minimal": "^10.4 || ^11.5 || ^12.0", "typo3/testing-framework": "^6.16 || ^7.0.2"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "content_defender", "cms-package-dir": "{$vendor-dir}/typo3/cms", "app-dir": ".Build", "web-dir": ".Build/public"}}, "autoload": {"psr-4": {"IchHabRecht\\ContentDefender\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "Define allowed or denied content element types in your backend layouts", "homepage": "https://github.com/IchHabRecht/content_defender", "keywords": ["TYPO3 CMS", "backend", "content", "restrict"], "support": {"issues": "https://github.com/IchHabRecht/content_defender/issues", "source": "https://github.com/IchHabRecht/content_defender/tree/3.4.3"}, "time": "2024-04-29T07:35:37+00:00"}, {"name": "in2code/powermail", "version": "12.3.5", "source": {"type": "git", "url": "https://github.com/in2code-de/powermail.git", "reference": "24853e38528dd565cd4834b74cfa677308e8abaa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/in2code-de/powermail/zipball/24853e38528dd565cd4834b74cfa677308e8abaa", "reference": "24853e38528dd565cd4834b74cfa677308e8abaa", "shasum": ""}, "require": {"ext-curl": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-json": "*", "php": "^8.1", "typo3/cms-core": "^12.4"}, "replace": {"typo3-ter/powermail": "self.version"}, "require-dev": {"behat/behat": "^3.8", "behat/mink-selenium2-driver": "^1.5", "drevops/behat-screenshot": "^1.1", "friends-of-behat/mink-browserkit-driver": "^1.5", "friends-of-behat/mink-extension": "^2.5", "friendsofphp/php-cs-fixer": "^3.10", "helhum/typo3-console": "^8.0.x-dev", "helmich/typo3-typoscript-lint": "^3.1", "mikey179/vfsstream": "^1.6", "phpmd/phpmd": "^2.8", "se/selenium-server-standalone": "^3.141", "squizlabs/php_codesniffer": "^3.5", "symfony/config": "^6.2", "typo3/cms-adminpanel": "^12.4", "typo3/cms-belog": "^12.4", "typo3/cms-beuser": "^12.4", "typo3/cms-dashboard": "^12.4", "typo3/cms-extbase": "^12.4", "typo3/cms-extensionmanager": "^12.4", "typo3/cms-felogin": "^12.4", "typo3/cms-filelist": "^12.4", "typo3/cms-fluid-styled-content": "^12.4", "typo3/cms-frontend": "^12.4", "typo3/cms-info": "^12.4", "typo3/cms-install": "^12.4", "typo3/cms-lowlevel": "^12.4", "typo3/cms-recordlist": "^12.4", "typo3/cms-rte-ckeditor": "^12.4", "typo3/cms-scheduler": "^12.4", "typo3/cms-setup": "^12.4", "typo3/cms-t3editor": "^12.4", "typo3/cms-tstemplate": "^12.4", "typo3/testing-framework": "^8.0"}, "suggest": {"jambagecom/base-excel": "^v1.29.0", "sjbr/static-info-tables": "*"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"cms-package-dir": "{$vendor-dir}/typo3/cms", "web-dir": ".build/public", "extension-key": "powermail", "ignore-as-root": false}}, "autoload": {"psr-4": {"In2code\\Powermail\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Update Manager"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Product owner, Technical owner"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.in2code.de", "role": "Developer"}], "description": "Powermail is a well-known, editor-friendly, powerful and easy to use mailform extension for TYPO3 with a lots of features", "homepage": "https://github.com/einpraegsam/powermail", "keywords": ["form", "mailform", "spamprevention", "typo3"], "support": {"issues": "https://github.com/in2code-de/powermail/issues", "source": "https://github.com/in2code-de/powermail/tree/12.3.5"}, "time": "2024-06-07T14:55:24+00:00"}, {"name": "jean85/pretty-package-versions", "version": "2.0.6", "source": {"type": "git", "url": "https://github.com/Jean85/pretty-package-versions.git", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Jean85/pretty-package-versions/zipball/f9fdd29ad8e6d024f52678b570e5593759b550b4", "reference": "f9fdd29ad8e6d024f52678b570e5593759b550b4", "shasum": ""}, "require": {"composer-runtime-api": "^2.0.0", "php": "^7.1|^8.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.2", "jean85/composer-provided-replaced-stub-package": "^1.0", "phpstan/phpstan": "^1.4", "phpunit/phpunit": "^7.5|^8.5|^9.4", "vimeo/psalm": "^4.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.x-dev"}}, "autoload": {"psr-4": {"Jean85\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A library to get pretty versions strings of installed dependencies", "keywords": ["composer", "package", "release", "versions"], "support": {"issues": "https://github.com/Jean85/pretty-package-versions/issues", "source": "https://github.com/Jean85/pretty-package-versions/tree/2.0.6"}, "time": "2024-03-08T09:58:59+00:00"}, {"name": "jigal/t3adminer", "version": "12.0.1", "source": {"type": "git", "url": "**************:jigal/t3adminer.git", "reference": "8e76d49a483117ecdb22fa21d4dcf0fef0911b4c"}, "dist": {"type": "zip", "url": "https://gitlab.com/api/v4/projects/jigal%2Ft3adminer/repository/archive.zip?sha=8e76d49a483117ecdb22fa21d4dcf0fef0911b4c", "reference": "8e76d49a483117ecdb22fa21d4dcf0fef0911b4c", "shasum": ""}, "require": {"ext-phar": "*", "php": "~7.4.0 || ~8.0.0 || ~8.1.0 || ~8.2.0", "typo3/cms-core": "^11.5.0 || ^12.4.0"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "t3adminer"}}, "autoload": {"psr-4": {"Jigal\\T3adminer\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.typo3coder.nl", "role": "Developer"}], "description": "Adminer as BE module to work with the database", "homepage": "https://forge.typo3.org/projects/extension-t3adminer", "keywords": ["adminer", "database", "dev", "ma<PERSON>b", "mysql", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "postgresq", "sqlite", "typo3"], "support": {"issues": "https://forge.typo3.org/projects/extension-t3adminer/issues"}, "time": "2023-09-03T20:40:40+00:00"}, {"name": "kamermans/guzzle-oauth2-subscriber", "version": "v1.1.0", "source": {"type": "git", "url": "https://github.com/kamermans/guzzle-oauth2-subscriber.git", "reference": "16f2fb28fa6803c519c6339ff6270cdd7a365abf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/kamermans/guzzle-oauth2-subscriber/zipball/16f2fb28fa6803c519c6339ff6270cdd7a365abf", "reference": "16f2fb28fa6803c519c6339ff6270cdd7a365abf", "shasum": ""}, "require": {"php": ">=7.1.0"}, "suggest": {"guzzlehttp/guzzle": "Guzzle ~4.0|~5.0|~6.0|~7.0"}, "type": "library", "autoload": {"psr-4": {"kamermans\\OAuth2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "steve<PERSON><PERSON><PERSON>@gmail.com"}], "description": "OAuth 2.0 client for Guzzle 4, 5, 6 and 7+", "keywords": ["Guzzle", "o<PERSON>h"], "support": {"issues": "https://github.com/kamermans/guzzle-oauth2-subscriber/issues", "source": "https://github.com/kamermans/guzzle-oauth2-subscriber/tree/v1.1.0"}, "time": "2024-05-02T18:45:14+00:00"}, {"name": "lolli42/finediff", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/lolli42/FineDiff.git", "reference": "784ade1515ba9f56d943a6a551c96073f9858b85"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/lolli42/FineDiff/zipball/784ade1515ba9f56d943a6a551c96073f9858b85", "reference": "784ade1515ba9f56d943a6a551c96073f9858b85", "shasum": ""}, "require": {"ext-mbstring": "*", "php": ">=8.2"}, "replace": {"cogpowered/finediff": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.49.0", "phpstan/phpstan": "^1.10.57", "phpunit/phpunit": "^10.5.10 || ^11.0.2"}, "type": "library", "autoload": {"psr-4": {"cogpowered\\FineDiff\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "maintainer"}], "description": "PHP implementation of a Fine granularity Diff engine", "homepage": "https://github.com/lolli42/FineDiff", "keywords": ["diff", "finediff", "opcode", "string", "text"], "support": {"issues": "https://github.com/lolli42/FineDiff/issues", "source": "https://github.com/lolli42/FineDiff/tree/1.1.0"}, "time": "2024-02-06T15:34:02+00:00"}, {"name": "maennchen/zipstream-php", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/maennchen/ZipStream-PHP.git", "reference": "b8174494eda667f7d13876b4a7bfef0f62a7c0d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maennchen/ZipStream-PHP/zipball/b8174494eda667f7d13876b4a7bfef0f62a7c0d1", "reference": "b8174494eda667f7d13876b4a7bfef0f62a7c0d1", "shasum": ""}, "require": {"ext-mbstring": "*", "ext-zlib": "*", "php-64bit": "^8.1"}, "require-dev": {"ext-zip": "*", "friendsofphp/php-cs-fixer": "^3.16", "guzzlehttp/guzzle": "^7.5", "mikey179/vfsstream": "^1.6", "php-coveralls/php-coveralls": "^2.5", "phpunit/phpunit": "^10.0", "vimeo/psalm": "^5.0"}, "suggest": {"guzzlehttp/psr7": "^2.4", "psr/http-message": "^2.0"}, "type": "library", "autoload": {"psr-4": {"ZipStream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Jonatan Männchen", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "ZipStream is a library for dynamically streaming dynamic zip files from PHP without writing to the disk at all on the server.", "keywords": ["stream", "zip"], "support": {"issues": "https://github.com/maennchen/ZipStream-PHP/issues", "source": "https://github.com/maennchen/ZipStream-PHP/tree/3.1.0"}, "funding": [{"url": "https://github.com/maennchen", "type": "github"}, {"url": "https://opencollective.com/zipstream", "type": "open_collective"}], "time": "2023-06-21T14:59:35+00:00"}, {"name": "markbaker/complex", "version": "3.0.2", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPComplex.git", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPComplex/zipball/95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "reference": "95c56caa1cf5c766ad6d65b6344b807c1e8405b9", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Complex\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with complex numbers", "homepage": "https://github.com/MarkBaker/PHPComplex", "keywords": ["complex", "mathematics"], "support": {"issues": "https://github.com/MarkBaker/PHPComplex/issues", "source": "https://github.com/MarkBaker/PHPComplex/tree/3.0.2"}, "time": "2022-12-06T16:21:08+00:00"}, {"name": "markbaker/matrix", "version": "3.0.1", "source": {"type": "git", "url": "https://github.com/MarkBaker/PHPMatrix.git", "reference": "728434227fe21be27ff6d86621a1b13107a2562c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/MarkBaker/PHPMatrix/zipball/728434227fe21be27ff6d86621a1b13107a2562c", "reference": "728434227fe21be27ff6d86621a1b13107a2562c", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-master", "phpcompatibility/php-compatibility": "^9.3", "phpdocumentor/phpdocumentor": "2.*", "phploc/phploc": "^4.0", "phpmd/phpmd": "2.*", "phpunit/phpunit": "^7.0 || ^8.0 || ^9.0", "sebastian/phpcpd": "^4.0", "squizlabs/php_codesniffer": "^3.7"}, "type": "library", "autoload": {"psr-4": {"Matrix\\": "classes/src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "PHP Class for working with matrices", "homepage": "https://github.com/MarkBaker/PHPMatrix", "keywords": ["mathematics", "matrix", "vector"], "support": {"issues": "https://github.com/MarkBaker/PHPMatrix/issues", "source": "https://github.com/MarkBaker/PHPMatrix/tree/3.0.1"}, "time": "2022-12-02T22:17:43+00:00"}, {"name": "masterminds/html5", "version": "2.9.0", "source": {"type": "git", "url": "https://github.com/Masterminds/html5-php.git", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/Masterminds/html5-php/zipball/f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "reference": "f5ac2c0b0a2eefca70b2ce32a5809992227e75a6", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^4.8.35 || ^5.7.21 || ^6 || ^7 || ^8 || ^9"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"psr-4": {"Masterminds\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An HTML5 parser and serializer.", "homepage": "http://masterminds.github.io/html5-php", "keywords": ["HTML5", "dom", "html", "parser", "querypath", "serializer", "xml"], "support": {"issues": "https://github.com/Masterminds/html5-php/issues", "source": "https://github.com/Masterminds/html5-php/tree/2.9.0"}, "time": "2024-03-31T07:05:07+00:00"}, {"name": "mogic/t3x-scheduler-status", "version": "v0.3.0", "source": {"type": "git", "url": "https://github.com/mogic-le/t3x-scheduler-status.git", "reference": "883bf897c1aa83458e347c62b37e539c47016292"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mogic-le/t3x-scheduler-status/zipball/883bf897c1aa83458e347c62b37e539c47016292", "reference": "883bf897c1aa83458e347c62b37e539c47016292", "shasum": ""}, "require": {"typo3/cms-core": "^11.5 || ^12.4", "typo3/cms-scheduler": "^11.5 || ^12.4"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "scheduler_status"}}, "autoload": {"psr-4": {"Mogic\\SchedulerStatus\\": "Classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "role": "Developer"}, {"name": "<PERSON>", "email": "<EMAIL>", "role": "Developer"}], "description": "API to monitor scheduler task execution status", "support": {"issues": "https://github.com/mogic-le/t3x-scheduler-status/issues", "source": "https://github.com/mogic-le/t3x-scheduler-status/tree/v0.3.0"}, "time": "2024-05-06T06:58:13+00:00"}, {"name": "mogic/tknucera", "version": "dev-main", "dist": {"type": "path", "url": "packages/tknucera", "reference": "5d5d15b396697372824dcc4d1a61f49db4957f33"}, "require": {"fluidtypo3/flux": "^10.0", "mogic/t3x-scheduler-status": "^0.3", "typo3/cms-core": "^12.4"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "tknucera"}}, "autoload": {"psr-4": {"Mogic\\tknucera\\": "Classes/"}}, "license": ["GPL-2.0-or-later"], "description": "Templates for tknucera", "transport-options": {"relative": true}}, {"name": "mtdowling/jmespath.php", "version": "2.7.0", "source": {"type": "git", "url": "https://github.com/jmespath/jmespath.php.git", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jmespath/jmespath.php/zipball/bbb69a935c2cbb0c03d7f481a238027430f6440b", "reference": "bbb69a935c2cbb0c03d7f481a238027430f6440b", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "symfony/polyfill-mbstring": "^1.17"}, "require-dev": {"composer/xdebug-handler": "^3.0.3", "phpunit/phpunit": "^8.5.33"}, "bin": ["bin/jp.php"], "type": "library", "extra": {"branch-alias": {"dev-master": "2.7-dev"}}, "autoload": {"files": ["src/JmesPath.php"], "psr-4": {"JmesPath\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Declaratively specify how to extract elements from a JSON document", "keywords": ["json", "jsonpath"], "support": {"issues": "https://github.com/jmespath/jmespath.php/issues", "source": "https://github.com/jmespath/jmespath.php/tree/2.7.0"}, "time": "2023-08-25T10:54:48+00:00"}, {"name": "netresearch/jsonmapper", "version": "v4.4.1", "source": {"type": "git", "url": "https://github.com/cweiske/jsonmapper.git", "reference": "132c75c7dd83e45353ebb9c6c9f591952995bbf0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cweiske/jsonmapper/zipball/132c75c7dd83e45353ebb9c6c9f591952995bbf0", "reference": "132c75c7dd83e45353ebb9c6c9f591952995bbf0", "shasum": ""}, "require": {"ext-json": "*", "ext-pcre": "*", "ext-reflection": "*", "ext-spl": "*", "php": ">=7.1"}, "require-dev": {"phpunit/phpunit": "~7.5 || ~8.0 || ~9.0 || ~10.0", "squizlabs/php_codesniffer": "~3.5"}, "type": "library", "autoload": {"psr-0": {"JsonMapper": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["OSL-3.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://github.com/cweiske/jsonmapper/", "role": "Developer"}], "description": "Map nested JSON structures onto PHP classes", "support": {"email": "<EMAIL>", "issues": "https://github.com/cweiske/jsonmapper/issues", "source": "https://github.com/cweiske/jsonmapper/tree/v4.4.1"}, "time": "2024-01-31T06:18:54+00:00"}, {"name": "networkteam/sentry-client", "version": "5.1.1", "source": {"type": "git", "url": "https://github.com/networkteam/sentry_client.git", "reference": "5c2b768f5f9a98eb19f02531b684aa0a7757714c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/networkteam/sentry_client/zipball/5c2b768f5f9a98eb19f02531b684aa0a7757714c", "reference": "5c2b768f5f9a98eb19f02531b684aa0a7757714c", "shasum": ""}, "require": {"php": "^8.0", "sentry/sentry": "^4.6", "typo3/cms-backend": "^11.0 || ^12.0", "typo3/cms-core": "^11.0 || ^12.0", "typo3/cms-frontend": "^11.0 || ^12.0"}, "require-dev": {"saschaegerer/phpstan-typo3": "^1.1"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"extension-key": "sentry_client", "cms-package-dir": "{$vendor-dir}/typo3/cms", "app-dir": ".Build", "web-dir": ".Build/Web"}}, "autoload": {"psr-4": {"Networkteam\\SentryClient\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A Sentry client for TYPO3. It forwards errors and exceptions to Sentry - https://sentry.io/", "homepage": "https://networkteam.com", "keywords": ["extension", "monitoring", "sentry", "typo3"], "support": {"issues": "https://github.com/networkteam/sentry_client", "source": "https://github.com/networkteam/sentry_client/tree/5.1.1"}, "time": "2024-05-13T20:46:20+00:00"}, {"name": "nikic/php-parser", "version": "v4.19.1", "source": {"type": "git", "url": "https://github.com/nikic/PHP-Parser.git", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nikic/PHP-Parser/zipball/4e1b88d21c69391150ace211e9eaf05810858d0b", "reference": "4e1b88d21c69391150ace211e9eaf05810858d0b", "shasum": ""}, "require": {"ext-tokenizer": "*", "php": ">=7.1"}, "require-dev": {"ircmaxell/php-yacc": "^0.0.7", "phpunit/phpunit": "^6.5 || ^7.0 || ^8.0 || ^9.0"}, "bin": ["bin/php-parse"], "type": "library", "extra": {"branch-alias": {"dev-master": "4.9-dev"}}, "autoload": {"psr-4": {"PhpParser\\": "lib/Php<PERSON><PERSON>er"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON><PERSON>"}], "description": "A PHP parser written in PHP", "keywords": ["parser", "php"], "support": {"issues": "https://github.com/nikic/PHP-Parser/issues", "source": "https://github.com/nikic/PHP-Parser/tree/v4.19.1"}, "time": "2024-03-17T08:10:35+00:00"}, {"name": "php-http/guzzle7-adapter", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-http/guzzle7-adapter.git", "reference": "fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/guzzle7-adapter/zipball/fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01", "reference": "fb075a71dbfa4847cf0c2938c4e5a9c478ef8b01", "shasum": ""}, "require": {"guzzlehttp/guzzle": "^7.0", "php": "^7.2 | ^8.0", "php-http/httplug": "^2.0", "psr/http-client": "^1.0"}, "provide": {"php-http/async-client-implementation": "1.0", "php-http/client-implementation": "1.0", "psr/http-client-implementation": "1.0"}, "require-dev": {"php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.0|^9.3"}, "type": "library", "extra": {"branch-alias": {"dev-master": "0.2.x-dev"}}, "autoload": {"psr-4": {"Http\\Adapter\\Guzzle7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Guzzle 7 HTTP Adapter", "homepage": "http://httplug.io", "keywords": ["Guzzle", "http"], "support": {"issues": "https://github.com/php-http/guzzle7-adapter/issues", "source": "https://github.com/php-http/guzzle7-adapter/tree/1.0.0"}, "time": "2021-03-09T07:35:15+00:00"}, {"name": "php-http/httplug", "version": "2.4.0", "source": {"type": "git", "url": "https://github.com/php-http/httplug.git", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/httplug/zipball/625ad742c360c8ac580fcc647a1541d29e257f67", "reference": "625ad742c360c8ac580fcc647a1541d29e257f67", "shasum": ""}, "require": {"php": "^7.1 || ^8.0", "php-http/promise": "^1.1", "psr/http-client": "^1.0", "psr/http-message": "^1.0 || ^2.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.1 || ^5.0 || ^6.0", "phpspec/phpspec": "^5.1 || ^6.0 || ^7.0"}, "type": "library", "autoload": {"psr-4": {"Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Eric <PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "HTTPlug, the HTTP client abstraction for PHP", "homepage": "http://httplug.io", "keywords": ["client", "http"], "support": {"issues": "https://github.com/php-http/httplug/issues", "source": "https://github.com/php-http/httplug/tree/2.4.0"}, "time": "2023-04-14T15:10:03+00:00"}, {"name": "php-http/promise", "version": "1.3.1", "source": {"type": "git", "url": "https://github.com/php-http/promise.git", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-http/promise/zipball/fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "reference": "fc85b1fba37c169a69a07ef0d5a8075770cc1f83", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"friends-of-phpspec/phpspec-code-coverage": "^4.3.2 || ^6.3", "phpspec/phpspec": "^5.1.2 || ^6.2 || ^7.4"}, "type": "library", "autoload": {"psr-4": {"Http\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Promise used for asynchronous HTTP requests", "homepage": "http://httplug.io", "keywords": ["promise"], "support": {"issues": "https://github.com/php-http/promise/issues", "source": "https://github.com/php-http/promise/tree/1.3.1"}, "time": "2024-03-15T13:55:21+00:00"}, {"name": "phpdocumentor/reflection-common", "version": "2.2.0", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionCommon.git", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionCommon/zipball/1d01c49d4ed62f25aa84a747ad35d5a16924662b", "reference": "1d01c49d4ed62f25aa84a747ad35d5a16924662b", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-2.x": "2.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Common reflection classes used by phpdocumentor to reflect the code structure", "homepage": "http://www.phpdoc.org", "keywords": ["FQSEN", "phpDocumentor", "phpdoc", "reflection", "static analysis"], "support": {"issues": "https://github.com/phpDocumentor/ReflectionCommon/issues", "source": "https://github.com/phpDocumentor/ReflectionCommon/tree/2.x"}, "time": "2020-06-27T09:03:43+00:00"}, {"name": "phpdocumentor/reflection-docblock", "version": "5.4.1", "source": {"type": "git", "url": "https://github.com/phpDocumentor/ReflectionDocBlock.git", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/ReflectionDocBlock/zipball/9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "reference": "9d07b3f7fdcf5efec5d1609cba3c19c5ea2bdc9c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.1", "ext-filter": "*", "php": "^7.4 || ^8.0", "phpdocumentor/reflection-common": "^2.2", "phpdocumentor/type-resolver": "^1.7", "phpstan/phpdoc-parser": "^1.7", "webmozart/assert": "^1.9.1"}, "require-dev": {"mockery/mockery": "~1.3.5", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-mockery": "^1.1", "phpstan/phpstan-webmozart-assert": "^1.2", "phpunit/phpunit": "^9.5", "vimeo/psalm": "^5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "5.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "With this component, a library can provide support for annotations via DocBlocks or otherwise retrieve information that is embedded in a DocBlock.", "support": {"issues": "https://github.com/phpDocumentor/ReflectionDocBlock/issues", "source": "https://github.com/phpDocumentor/ReflectionDocBlock/tree/5.4.1"}, "time": "2024-05-21T05:55:05+00:00"}, {"name": "phpdocumentor/type-resolver", "version": "1.8.2", "source": {"type": "git", "url": "https://github.com/phpDocumentor/TypeResolver.git", "reference": "153ae662783729388a584b4361f2545e4d841e3c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpDocumentor/TypeResolver/zipball/153ae662783729388a584b4361f2545e4d841e3c", "reference": "153ae662783729388a584b4361f2545e4d841e3c", "shasum": ""}, "require": {"doctrine/deprecations": "^1.0", "php": "^7.3 || ^8.0", "phpdocumentor/reflection-common": "^2.0", "phpstan/phpdoc-parser": "^1.13"}, "require-dev": {"ext-tokenizer": "*", "phpbench/phpbench": "^1.2", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.8", "phpstan/phpstan-phpunit": "^1.1", "phpunit/phpunit": "^9.5", "rector/rector": "^0.13.9", "vimeo/psalm": "^4.25"}, "type": "library", "extra": {"branch-alias": {"dev-1.x": "1.x-dev"}}, "autoload": {"psr-4": {"phpDocumentor\\Reflection\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A PSR-5 based resolver of Class names, Types and Structural Element Names", "support": {"issues": "https://github.com/phpDocumentor/TypeResolver/issues", "source": "https://github.com/phpDocumentor/TypeResolver/tree/1.8.2"}, "time": "2024-02-23T11:10:43+00:00"}, {"name": "phpoffice/phpspreadsheet", "version": "2.1.0", "source": {"type": "git", "url": "https://github.com/PHPOffice/PhpSpreadsheet.git", "reference": "dbed77bd3a0f68f96c0dd68ad4499d5674fecc3e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPOffice/PhpSpreadsheet/zipball/dbed77bd3a0f68f96c0dd68ad4499d5674fecc3e", "reference": "dbed77bd3a0f68f96c0dd68ad4499d5674fecc3e", "shasum": ""}, "require": {"ext-ctype": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-iconv": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-simplexml": "*", "ext-xml": "*", "ext-xmlreader": "*", "ext-xmlwriter": "*", "ext-zip": "*", "ext-zlib": "*", "maennchen/zipstream-php": "^2.1 || ^3.0", "markbaker/complex": "^3.0", "markbaker/matrix": "^3.0", "php": "^8.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/simple-cache": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"dealerdirect/phpcodesniffer-composer-installer": "dev-main", "dompdf/dompdf": "^2.0", "friendsofphp/php-cs-fixer": "^3.2", "mitoteam/jpgraph": "^10.3", "mpdf/mpdf": "^8.1.1", "phpcompatibility/php-compatibility": "^9.3", "phpstan/phpstan": "^1.1", "phpstan/phpstan-phpunit": "^1.0", "phpunit/phpunit": "^9.6", "squizlabs/php_codesniffer": "^3.7", "tecnickcom/tcpdf": "^6.5"}, "suggest": {"dompdf/dompdf": "Option for rendering PDF with PDF Writer", "ext-intl": "PHP Internationalization Functions", "mitoteam/jpgraph": "Option for rendering charts, or including charts with PDF or HTML Writers", "mpdf/mpdf": "Option for rendering PDF with PDF Writer", "tecnickcom/tcpdf": "Option for rendering PDF with PDF Writer"}, "type": "library", "autoload": {"psr-4": {"PhpOffice\\PhpSpreadsheet\\": "src/PhpSpreadsheet"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "homepage": "https://blog.maartenballiauw.be"}, {"name": "<PERSON>", "homepage": "https://markbakeruk.net"}, {"name": "<PERSON><PERSON><PERSON>", "homepage": "https://rootslabs.net"}, {"name": "<PERSON>"}, {"name": "<PERSON><PERSON>"}], "description": "PHPSpreadsheet - Read, Create and Write Spreadsheet documents in PHP - Spreadsheet engine", "homepage": "https://github.com/PHPOffice/PhpSpreadsheet", "keywords": ["OpenXML", "excel", "gnumeric", "ods", "php", "spreadsheet", "xls", "xlsx"], "support": {"issues": "https://github.com/PHPOffice/PhpSpreadsheet/issues", "source": "https://github.com/PHPOffice/PhpSpreadsheet/tree/2.1.0"}, "time": "2024-05-11T04:17:56+00:00"}, {"name": "phpstan/phpdoc-parser", "version": "1.29.1", "source": {"type": "git", "url": "https://github.com/phpstan/phpdoc-parser.git", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/phpstan/phpdoc-parser/zipball/fcaefacf2d5c417e928405b71b400d4ce10daaf4", "reference": "fcaefacf2d5c417e928405b71b400d4ce10daaf4", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"doctrine/annotations": "^2.0", "nikic/php-parser": "^4.15", "php-parallel-lint/php-parallel-lint": "^1.2", "phpstan/extension-installer": "^1.0", "phpstan/phpstan": "^1.5", "phpstan/phpstan-phpunit": "^1.1", "phpstan/phpstan-strict-rules": "^1.0", "phpunit/phpunit": "^9.5", "symfony/process": "^5.2"}, "type": "library", "autoload": {"psr-4": {"PHPStan\\PhpDocParser\\": ["src/"]}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "description": "PHPDoc parser with support for nullable, intersection and generic types", "support": {"issues": "https://github.com/phpstan/phpdoc-parser/issues", "source": "https://github.com/phpstan/phpdoc-parser/tree/1.29.1"}, "time": "2024-05-31T08:52:43+00:00"}, {"name": "psr/cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "reference": "aa5030cfa5405eccfdcb1083ce040c2cb8d253bf", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/3.0.0"}, "time": "2021-02-03T23:26:27+00:00"}, {"name": "psr/clock", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/clock.git", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/clock/zipball/e41a24703d4560fd0acb709162f73b8adfc3aa0d", "reference": "e41a24703d4560fd0acb709162f73b8adfc3aa0d", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Psr\\Clock\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for reading the clock.", "homepage": "https://github.com/php-fig/clock", "keywords": ["clock", "now", "psr", "psr-20", "time"], "support": {"issues": "https://github.com/php-fig/clock/issues", "source": "https://github.com/php-fig/clock/tree/1.0.0"}, "time": "2022-11-25T14:36:26+00:00"}, {"name": "psr/container", "version": "2.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/container.git", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/container/zipball/c71ecc56dfe541dbd90c5360474fbc405f8d5963", "reference": "c71ecc56dfe541dbd90c5360474fbc405f8d5963", "shasum": ""}, "require": {"php": ">=7.4.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Container\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common Container Interface (PHP FIG PSR-11)", "homepage": "https://github.com/php-fig/container", "keywords": ["PSR-11", "container", "container-interface", "container-interop", "psr"], "support": {"issues": "https://github.com/php-fig/container/issues", "source": "https://github.com/php-fig/container/tree/2.0.2"}, "time": "2021-11-05T16:47:00+00:00"}, {"name": "psr/event-dispatcher", "version": "1.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/event-dispatcher.git", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/event-dispatcher/zipball/dbefd12671e8a14ec7f180cab83036ed26714bb0", "reference": "dbefd12671e8a14ec7f180cab83036ed26714bb0", "shasum": ""}, "require": {"php": ">=7.2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\EventDispatcher\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Standard interfaces for event handling.", "keywords": ["events", "psr", "psr-14"], "support": {"issues": "https://github.com/php-fig/event-dispatcher/issues", "source": "https://github.com/php-fig/event-dispatcher/tree/1.0.0"}, "time": "2019-01-08T18:20:26+00:00"}, {"name": "psr/http-client", "version": "1.0.3", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/bb5906edc1c324c9a05aa0873d40117941e5fa90", "reference": "bb5906edc1c324c9a05aa0873d40117941e5fa90", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client"}, "time": "2023-09-23T14:17:50+00:00"}, {"name": "psr/http-factory", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "reference": "2b4765fddfe3b508ac62f829e852b1501d3f6e8a", "shasum": ""}, "require": {"php": ">=7.1", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "PSR-17: Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory"}, "time": "2024-04-15T12:06:14+00:00"}, {"name": "psr/http-message", "version": "2.0", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/402d35bcb92c70c026d1a6a9883f06b2ead23d71", "reference": "402d35bcb92c70c026d1a6a9883f06b2ead23d71", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/2.0"}, "time": "2023-04-04T09:54:51+00:00"}, {"name": "psr/http-server-handler", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-handler.git", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-handler/zipball/84c4fb66179be4caaf8e97bd239203245302e7d4", "reference": "84c4fb66179be4caaf8e97bd239203245302e7d4", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side request handler", "keywords": ["handler", "http", "http-interop", "psr", "psr-15", "psr-7", "request", "response", "server"], "support": {"source": "https://github.com/php-fig/http-server-handler/tree/1.0.2"}, "time": "2023-04-10T20:06:20+00:00"}, {"name": "psr/http-server-middleware", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/php-fig/http-server-middleware.git", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-server-middleware/zipball/c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "reference": "c1481f747daaa6a0782775cd6a8c26a1bf4a3829", "shasum": ""}, "require": {"php": ">=7.0", "psr/http-message": "^1.0 || ^2.0", "psr/http-server-handler": "^1.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Server\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP server-side middleware", "keywords": ["http", "http-interop", "middleware", "psr", "psr-15", "psr-7", "request", "response"], "support": {"issues": "https://github.com/php-fig/http-server-middleware/issues", "source": "https://github.com/php-fig/http-server-middleware/tree/1.0.2"}, "time": "2023-04-11T06:14:47+00:00"}, {"name": "psr/link", "version": "2.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/link.git", "reference": "84b159194ecfd7eaa472280213976e96415433f7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/link/zipball/84b159194ecfd7eaa472280213976e96415433f7", "reference": "84b159194ecfd7eaa472280213976e96415433f7", "shasum": ""}, "require": {"php": ">=8.0.0"}, "suggest": {"fig/link-util": "Provides some useful PSR-13 utilities"}, "type": "library", "extra": {"branch-alias": {"dev-master": "2.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Link\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interfaces for HTTP links", "homepage": "https://github.com/php-fig/link", "keywords": ["http", "http-link", "link", "psr", "psr-13", "rest"], "support": {"source": "https://github.com/php-fig/link/tree/2.0.1"}, "time": "2021-03-11T23:00:27+00:00"}, {"name": "psr/log", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/fe5ea303b0887d5caefd3d431c3e61ad47037001", "reference": "fe5ea303b0887d5caefd3d431c3e61ad47037001", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/3.0.0"}, "time": "2021-07-14T16:46:02+00:00"}, {"name": "psr/simple-cache", "version": "3.0.0", "source": {"type": "git", "url": "https://github.com/php-fig/simple-cache.git", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/simple-cache/zipball/764e0b3939f5ca87cb904f570ef9be2d78a07865", "reference": "764e0b3939f5ca87cb904f570ef9be2d78a07865", "shasum": ""}, "require": {"php": ">=8.0.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\SimpleCache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for simple caching", "keywords": ["cache", "caching", "psr", "psr-16", "simple-cache"], "support": {"source": "https://github.com/php-fig/simple-cache/tree/3.0.0"}, "time": "2021-10-29T13:26:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "sentry/sentry", "version": "4.8.0", "source": {"type": "git", "url": "https://github.com/getsentry/sentry-php.git", "reference": "3cf5778ff425a23f2d22ed41b423691d36f47163"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/getsentry/sentry-php/zipball/3cf5778ff425a23f2d22ed41b423691d36f47163", "reference": "3cf5778ff425a23f2d22ed41b423691d36f47163", "shasum": ""}, "require": {"ext-curl": "*", "ext-json": "*", "ext-mbstring": "*", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "jean85/pretty-package-versions": "^1.5|^2.0.4", "php": "^7.2|^8.0", "psr/log": "^1.0|^2.0|^3.0", "symfony/options-resolver": "^4.4.30|^5.0.11|^6.0|^7.0"}, "conflict": {"raven/raven": "*"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.4", "guzzlehttp/promises": "^1.0|^2.0", "guzzlehttp/psr7": "^1.8.4|^2.1.1", "monolog/monolog": "^1.6|^2.0|^3.0", "phpbench/phpbench": "^1.0", "phpstan/phpstan": "^1.3", "phpunit/phpunit": "^8.5.14|^9.4", "symfony/phpunit-bridge": "^5.2|^6.0|^7.0", "vimeo/psalm": "^4.17"}, "suggest": {"monolog/monolog": "Allow sending log messages to Sentry by using the included Monolog handler."}, "type": "library", "autoload": {"files": ["src/functions.php"], "psr-4": {"Sentry\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Sentry", "email": "<EMAIL>"}], "description": "PHP SDK for Sentry (http://sentry.io)", "homepage": "http://sentry.io", "keywords": ["crash-reporting", "crash-reports", "error-handler", "error-monitoring", "log", "logging", "profiling", "sentry", "tracing"], "support": {"issues": "https://github.com/getsentry/sentry-php/issues", "source": "https://github.com/getsentry/sentry-php/tree/4.8.0"}, "funding": [{"url": "https://sentry.io/", "type": "custom"}, {"url": "https://sentry.io/pricing/", "type": "custom"}], "time": "2024-06-05T13:18:43+00:00"}, {"name": "sitegeist/fluid-components", "version": "3.6.0", "source": {"type": "git", "url": "https://github.com/sitegeist/fluid-components.git", "reference": "14329e6f591dc29ce1aa3df1b30c7c5b1774ad16"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sitegeist/fluid-components/zipball/14329e6f591dc29ce1aa3df1b30c7c5b1774ad16", "reference": "14329e6f591dc29ce1aa3df1b30c7c5b1774ad16", "shasum": ""}, "require": {"typo3/cms-core": "^12.2 || ^11.5", "typo3fluid/fluid": "^2"}, "require-dev": {"editorconfig-checker/editorconfig-checker": "^10.0", "phpspec/prophecy-phpunit": "^2.0", "squizlabs/php_codesniffer": "^3.0", "typo3/testing-framework": "^6.0 || ^7.0"}, "type": "typo3-cms-extension", "extra": {"typo3/cms": {"Package": {"serviceProvider": "SMS\\FluidComponents\\ServiceProvider"}, "cms-package-dir": "{$vendor-dir}/typo3/cms", "app-dir": ".Build", "web-dir": ".Build/Web", "extension-key": "fluid_components"}}, "autoload": {"psr-4": {"SMS\\FluidComponents\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Encapsulated frontend components with Fluid's ViewHelper syntax", "homepage": "https://github.com/sitegeist/fluid-components", "keywords": ["components", "fluid", "html", "template", "typo3", "typo3-extension", "typo3-fluid"], "support": {"issues": "https://github.com/sitegeist/fluid-components/issues", "source": "https://github.com/sitegeist/fluid-components/tree/3.6.0"}, "time": "2023-06-08T08:15:12+00:00"}, {"name": "ssch/typo3-encore", "version": "v5.0.6", "source": {"type": "git", "url": "https://github.com/sabbelasichon/typo3_encore.git", "reference": "e20e7200c487e7275a5bbfa0efc3eb347e23e063"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sabbelasichon/typo3_encore/zipball/e20e7200c487e7275a5bbfa0efc3eb347e23e063", "reference": "e20e7200c487e7275a5bbfa0efc3eb347e23e063", "shasum": ""}, "require": {"ext-dom": "*", "php": ">=7.4", "symfony/asset": "^5.0 || ^6", "symfony/web-link": "^5.4 || ^6", "typo3/cms-core": "^10.4.2 || ^11.5 || ^12.4", "typo3/cms-tstemplate": "^10.4.2 || ^11.5 || ^12.4", "webmozart/assert": "^1.10"}, "replace": {"typo3-ter/typo3-encore": "self.version"}, "require-dev": {"jangregor/phpstan-prophecy": "^1.0", "php-parallel-lint/php-parallel-lint": "^1.3", "phpspec/prophecy-phpunit": "^2.0", "phpstan/extension-installer": "^1.1", "phpstan/phpstan": "^1.0", "phpstan/phpstan-phpunit": "^1.0", "phpstan/phpstan-strict-rules": "^1.4.4", "phpstan/phpstan-webmozart-assert": "^1.2.2", "rector/rector": "^0.15.2", "saschaegerer/phpstan-typo3": "^1.8.0", "sbuerk/typo3-cmscomposerinstallers-testingframework-bridge": "^0.1.1", "symplify/easy-coding-standard": "^11.0", "typo3/cms-rte-ckeditor": "^10.4 || ^11.5 || ^12.4", "typo3/minimal": "^10.4 || ^11.5 || ^12.4", "typo3/testing-framework": "^6.2 || dev-main"}, "type": "typo3-cms-extension", "extra": {"branch-alias": {"dev-master": "4.x-dev"}, "typo3/cms": {"extension-key": "typo3_encore", "web-dir": ".Build", "app-dir": ".Build"}}, "autoload": {"psr-4": {"Ssch\\Typo3Encore\\": "Classes"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://www.schreibersebastian.de", "role": "Developer"}], "description": "Use Webpack Encore in TYPO3 Context", "homepage": "http://www.schreibersebastian.de", "keywords": ["encore", "webpack"], "support": {"issues": "https://github.com/sabbelasichon/typo3_encore/issues", "source": "https://github.com/sabbelasichon/typo3_encore/tree/v5.0.6"}, "funding": [{"url": "https://paypal.me/schreiberten", "type": "custom"}, {"url": "https://github.com/sabbelasichon", "type": "github"}], "time": "2023-05-15T14:04:16+00:00"}, {"name": "symfony/asset", "version": "v6.4.8", "source": {"type": "git", "url": "https://github.com/symfony/asset.git", "reference": "c668aa320e26b7379540368832b9d1dd43d32603"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/asset/zipball/c668aa320e26b7379540368832b9d1dd43d32603", "reference": "c668aa320e26b7379540368832b9d1dd43d32603", "shasum": ""}, "require": {"php": ">=8.1"}, "conflict": {"symfony/http-foundation": "<5.4"}, "require-dev": {"symfony/http-client": "^5.4|^6.0|^7.0", "symfony/http-foundation": "^5.4|^6.0|^7.0", "symfony/http-kernel": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Asset\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages URL generation and versioning of web assets such as CSS stylesheets, JavaScript files and image files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/asset/tree/v6.4.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:49:08+00:00"}, {"name": "symfony/cache", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/cache.git", "reference": "760294dc7158372699dccd077965c16c328f8719"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache/zipball/760294dc7158372699dccd077965c16c328f8719", "reference": "760294dc7158372699dccd077965c16c328f8719", "shasum": ""}, "require": {"php": ">=8.2", "psr/cache": "^2.0|^3.0", "psr/log": "^1.1|^2|^3", "symfony/cache-contracts": "^2.5|^3", "symfony/deprecation-contracts": "^2.5|^3.0", "symfony/service-contracts": "^2.5|^3", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/dependency-injection": "<6.4", "symfony/http-kernel": "<6.4", "symfony/var-dumper": "<6.4"}, "provide": {"psr/cache-implementation": "2.0|3.0", "psr/simple-cache-implementation": "1.0|2.0|3.0", "symfony/cache-implementation": "1.1|2.0|3.0"}, "require-dev": {"cache/integration-tests": "dev-master", "doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "psr/simple-cache": "^1.0|^2.0|^3.0", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/filesystem": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Cache\\": ""}, "classmap": ["Traits/ValueWrapper.php"], "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides extended PSR-6, PSR-16 (and tags) implementations", "homepage": "https://symfony.com", "keywords": ["caching", "psr6"], "support": {"source": "https://github.com/symfony/cache/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/cache-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/cache-contracts.git", "reference": "df6a1a44c890faded49a5fca33c2d5c5fd3c2197"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/cache-contracts/zipball/df6a1a44c890faded49a5fca33c2d5c5fd3c2197", "reference": "df6a1a44c890faded49a5fca33c2d5c5fd3c2197", "shasum": ""}, "require": {"php": ">=8.1", "psr/cache": "^3.0"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Cache\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to caching", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/cache-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/clock", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/clock.git", "reference": "3dfc8b084853586de51dd1441c6242c76a28cbe7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/clock/zipball/3dfc8b084853586de51dd1441c6242c76a28cbe7", "reference": "3dfc8b084853586de51dd1441c6242c76a28cbe7", "shasum": ""}, "require": {"php": ">=8.2", "psr/clock": "^1.0", "symfony/polyfill-php83": "^1.28"}, "provide": {"psr/clock-implementation": "1.0"}, "type": "library", "autoload": {"files": ["Resources/now.php"], "psr-4": {"Symfony\\Component\\Clock\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Decouples applications from the system clock", "homepage": "https://symfony.com", "keywords": ["clock", "psr20", "time"], "support": {"source": "https://github.com/symfony/clock/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/config", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/config.git", "reference": "2210fc99fa42a259eb6c89d1f724ce0c4d62d5d2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/config/zipball/2210fc99fa42a259eb6c89d1f724ce0c4d62d5d2", "reference": "2210fc99fa42a259eb6c89d1f724ce0c4d62d5d2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3", "symfony/filesystem": "^7.1", "symfony/polyfill-ctype": "~1.8"}, "conflict": {"symfony/finder": "<6.4", "symfony/service-contracts": "<2.5"}, "require-dev": {"symfony/event-dispatcher": "^6.4|^7.0", "symfony/finder": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Config\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps you find, load, combine, autofill and validate configuration values of any kind", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/config/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/console", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/console.git", "reference": "9b008f2d7b21c74ef4d0c3de6077a642bc55ece3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/console/zipball/9b008f2d7b21c74ef4d0c3de6077a642bc55ece3", "reference": "9b008f2d7b21c74ef4d0c3de6077a642bc55ece3", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.0", "symfony/service-contracts": "^2.5|^3", "symfony/string": "^6.4|^7.0"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/dotenv": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/lock": "<6.4", "symfony/process": "<6.4"}, "provide": {"psr/log-implementation": "1.0|2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/lock": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/stopwatch": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Console\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Eases the creation of beautiful and testable command line interfaces", "homepage": "https://symfony.com", "keywords": ["cli", "command-line", "console", "terminal"], "support": {"source": "https://github.com/symfony/console/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/dependency-injection", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/dependency-injection.git", "reference": "77c636dfd86c0b60c5d184b2fd2ddf8dd11c309c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/dependency-injection/zipball/77c636dfd86c0b60c5d184b2fd2ddf8dd11c309c", "reference": "77c636dfd86c0b60c5d184b2fd2ddf8dd11c309c", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^3.5", "symfony/var-exporter": "^6.4|^7.0"}, "conflict": {"ext-psr": "<1.1|>=2", "symfony/config": "<6.4", "symfony/finder": "<6.4", "symfony/yaml": "<6.4"}, "provide": {"psr/container-implementation": "1.1|2.0", "symfony/service-implementation": "1.1|2.0|3.0"}, "require-dev": {"symfony/config": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\DependencyInjection\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows you to standardize and centralize the way objects are constructed in your application", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/dependency-injection/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/deprecation-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "reference": "0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1", "shasum": ""}, "require": {"php": ">=8.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/doctrine-messenger", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/doctrine-messenger.git", "reference": "d4195a8ede84526328abe12683b4479fc2589461"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/doctrine-messenger/zipball/d4195a8ede84526328abe12683b4479fc2589461", "reference": "d4195a8ede84526328abe12683b4479fc2589461", "shasum": ""}, "require": {"doctrine/dbal": "^3.6|^4", "php": ">=8.2", "symfony/messenger": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"doctrine/persistence": "<1.3"}, "require-dev": {"doctrine/persistence": "^1.3|^2|^3", "symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "symfony-messenger-bridge", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\Bridge\\Doctrine\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony Doctrine Messenger Bridge", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/doctrine-messenger/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/event-dispatcher", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher.git", "reference": "9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher/zipball/9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7", "reference": "9fa7f7a21beb22a39a8f3f28618b29e50d7a55a7", "shasum": ""}, "require": {"php": ">=8.2", "symfony/event-dispatcher-contracts": "^2.5|^3"}, "conflict": {"symfony/dependency-injection": "<6.4", "symfony/service-contracts": "<2.5"}, "provide": {"psr/event-dispatcher-implementation": "1.0", "symfony/event-dispatcher-implementation": "2.0|3.0"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/error-handler": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\EventDispatcher\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides tools that allow your application components to communicate with each other by dispatching events and listening to them", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/event-dispatcher/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/event-dispatcher-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/event-dispatcher-contracts.git", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/event-dispatcher-contracts/zipball/8f93aec25d41b72493c6ddff14e916177c9efc50", "reference": "8f93aec25d41b72493c6ddff14e916177c9efc50", "shasum": ""}, "require": {"php": ">=8.1", "psr/event-dispatcher": "^1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\EventDispatcher\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to dispatching event", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/event-dispatcher-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/expression-language", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/expression-language.git", "reference": "463cb95f80c14136175f4e03f7f6199b01c6b8b4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/expression-language/zipball/463cb95f80c14136175f4e03f7f6199b01c6b8b4", "reference": "463cb95f80c14136175f4e03f7f6199b01c6b8b4", "shasum": ""}, "require": {"php": ">=8.2", "symfony/cache": "^6.4|^7.0", "symfony/deprecation-contracts": "^2.5|^3", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\ExpressionLanguage\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an engine that can compile and evaluate expressions", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/expression-language/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/filesystem", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/filesystem.git", "reference": "802e87002f919296c9f606457d9fa327a0b3d6b2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/filesystem/zipball/802e87002f919296c9f606457d9fa327a0b3d6b2", "reference": "802e87002f919296c9f606457d9fa327a0b3d6b2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-mbstring": "~1.8"}, "require-dev": {"symfony/process": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Filesystem\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides basic utilities for the filesystem", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/filesystem/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/finder", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/finder.git", "reference": "fbb0ba67688b780efbc886c1a0a0948dcf7205d6"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/finder/zipball/fbb0ba67688b780efbc886c1a0a0948dcf7205d6", "reference": "fbb0ba67688b780efbc886c1a0a0948dcf7205d6", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/filesystem": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Finder\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Finds files and directories via an intuitive fluent interface", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/finder/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/http-foundation", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/http-foundation.git", "reference": "74d171d5b6a1d9e4bfee09a41937c17a7536acfa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/http-foundation/zipball/74d171d5b6a1d9e4bfee09a41937c17a7536acfa", "reference": "74d171d5b6a1d9e4bfee09a41937c17a7536acfa", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-mbstring": "~1.1", "symfony/polyfill-php83": "^1.27"}, "conflict": {"doctrine/dbal": "<3.6", "symfony/cache": "<6.4"}, "require-dev": {"doctrine/dbal": "^3.6|^4", "predis/predis": "^1.1|^2.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\HttpFoundation\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Defines an object-oriented layer for the HTTP specification", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/http-foundation/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/mailer", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/mailer.git", "reference": "2eaad2e167cae930f25a3d731fec8b2ded5e751e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mailer/zipball/2eaad2e167cae930f25a3d731fec8b2ded5e751e", "reference": "2eaad2e167cae930f25a3d731fec8b2ded5e751e", "shasum": ""}, "require": {"egulias/email-validator": "^2.1.10|^3|^4", "php": ">=8.2", "psr/event-dispatcher": "^1", "psr/log": "^1|^2|^3", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/mime": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3"}, "conflict": {"symfony/http-client-contracts": "<2.5", "symfony/http-kernel": "<6.4", "symfony/messenger": "<6.4", "symfony/mime": "<6.4", "symfony/twig-bridge": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/messenger": "^6.4|^7.0", "symfony/twig-bridge": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mailer\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps sending emails", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/mailer/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/messenger", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/messenger.git", "reference": "70df0484323979f555df70bbb14d85c75f186010"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/messenger/zipball/70df0484323979f555df70bbb14d85c75f186010", "reference": "70df0484323979f555df70bbb14d85c75f186010", "shasum": ""}, "require": {"php": ">=8.2", "psr/log": "^1|^2|^3", "symfony/clock": "^6.4|^7.0"}, "conflict": {"symfony/console": "<6.4", "symfony/event-dispatcher": "<6.4", "symfony/event-dispatcher-contracts": "<2.5", "symfony/framework-bundle": "<6.4", "symfony/http-kernel": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/console": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/event-dispatcher": "^6.4|^7.0", "symfony/http-kernel": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/rate-limiter": "^6.4|^7.0", "symfony/routing": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/service-contracts": "^2.5|^3", "symfony/stopwatch": "^6.4|^7.0", "symfony/validator": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Messenger\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Helps applications send and receive messages to/from other applications or via message queues", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/messenger/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/mime", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/mime.git", "reference": "21027eaacc1a8a20f5e616c25c3580f5dd3a15df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/mime/zipball/21027eaacc1a8a20f5e616c25c3580f5dd3a15df", "reference": "21027eaacc1a8a20f5e616c25c3580f5dd3a15df", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-intl-idn": "^1.10", "symfony/polyfill-mbstring": "^1.0"}, "conflict": {"egulias/email-validator": "~3.0.0", "phpdocumentor/reflection-docblock": "<3.2.2", "phpdocumentor/type-resolver": "<1.4.0", "symfony/mailer": "<6.4", "symfony/serializer": "<6.4.3|>7.0,<7.0.3"}, "require-dev": {"egulias/email-validator": "^2.1.10|^3.1|^4", "league/html-to-markdown": "^5.0", "phpdocumentor/reflection-docblock": "^3.0|^4.0|^5.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/process": "^6.4|^7.0", "symfony/property-access": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0", "symfony/serializer": "^6.4.3|^7.0.3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Mime\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows manipulating MIME messages", "homepage": "https://symfony.com", "keywords": ["mime", "mime-type"], "support": {"source": "https://github.com/symfony/mime/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-04T06:40:14+00:00"}, {"name": "symfony/options-resolver", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/options-resolver.git", "reference": "47aa818121ed3950acd2b58d1d37d08a94f9bf55"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/options-resolver/zipball/47aa818121ed3950acd2b58d1d37d08a94f9bf55", "reference": "47aa818121ed3950acd2b58d1d37d08a94f9bf55", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\OptionsResolver\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an improved replacement for the array_replace PHP function", "homepage": "https://symfony.com", "keywords": ["config", "configuration", "options"], "support": {"source": "https://github.com/symfony/options-resolver/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/polyfill-ctype", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-ctype.git", "reference": "0424dff1c58f028c451efff2045f5d92410bd540"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-ctype/zipball/0424dff1c58f028c451efff2045f5d92410bd540", "reference": "0424dff1c58f028c451efff2045f5d92410bd540", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-ctype": "*"}, "suggest": {"ext-ctype": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Ctype\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for ctype functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "ctype", "polyfill", "portable"], "support": {"source": "https://github.com/symfony/polyfill-ctype/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-grapheme", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-grapheme.git", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-grapheme/zipball/64647a7c30b2283f5d49b874d84a18fc22054b7a", "reference": "64647a7c30b2283f5d49b874d84a18fc22054b7a", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Grapheme\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's grapheme_* functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "grapheme", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-grapheme/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-idn", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-idn.git", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-idn/zipball/a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "reference": "a6e83bdeb3c84391d1dfe16f42e40727ce524a5c", "shasum": ""}, "require": {"php": ">=7.1", "symfony/polyfill-intl-normalizer": "^1.10", "symfony/polyfill-php72": "^1.10"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Idn\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's idn_to_ascii and idn_to_utf8 functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "idn", "intl", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-idn/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-intl-normalizer", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-intl-normalizer.git", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-intl-normalizer/zipball/a95281b0be0d9ab48050ebd988b967875cdb9fdb", "reference": "a95281b0be0d9ab48050ebd988b967875cdb9fdb", "shasum": ""}, "require": {"php": ">=7.1"}, "suggest": {"ext-intl": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Intl\\Normalizer\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for intl's Normalizer class and related functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "intl", "normalizer", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-intl-normalizer/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-mbstring", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-mbstring.git", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-mbstring/zipball/fd22ab50000ef01661e2a31d850ebaa297f8e03c", "reference": "fd22ab50000ef01661e2a31d850ebaa297f8e03c", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-mbstring": "*"}, "suggest": {"ext-mbstring": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Mbstring\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for the Mbstring extension", "homepage": "https://symfony.com", "keywords": ["compatibility", "mbstring", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-mbstring/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php72", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php72.git", "reference": "10112722600777e02d2745716b70c5db4ca70442"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php72/zipball/10112722600777e02d2745716b70c5db4ca70442", "reference": "10112722600777e02d2745716b70c5db4ca70442", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php72\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php72/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/polyfill-php83", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php83.git", "reference": "dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php83/zipball/dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9", "reference": "dbdcdf1a4dcc2743591f1079d0c35ab1e2dcbbc9", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php83\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.3+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php83/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:35:24+00:00"}, {"name": "symfony/polyfill-uuid", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-uuid.git", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-uuid/zipball/2ba1f33797470debcda07fe9dce20a0003df18e9", "reference": "2ba1f33797470debcda07fe9dce20a0003df18e9", "shasum": ""}, "require": {"php": ">=7.1"}, "provide": {"ext-uuid": "*"}, "suggest": {"ext-uuid": "For best performance"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Uuid\\": ""}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill for uuid functions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "uuid"], "support": {"source": "https://github.com/symfony/polyfill-uuid/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/process", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/process.git", "reference": "febf90124323a093c7ee06fdb30e765ca3c20028"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/process/zipball/febf90124323a093c7ee06fdb30e765ca3c20028", "reference": "febf90124323a093c7ee06fdb30e765ca3c20028", "shasum": ""}, "require": {"php": ">=8.2"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Process\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Executes commands in sub-processes", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/process/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/property-access", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/property-access.git", "reference": "74e39e6a6276b8e384f34c6ddbc10a6c9a60193a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-access/zipball/74e39e6a6276b8e384f34c6ddbc10a6c9a60193a", "reference": "74e39e6a6276b8e384f34c6ddbc10a6c9a60193a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/property-info": "^6.4|^7.0"}, "require-dev": {"symfony/cache": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyAccess\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides functions to read and write from/to an object or array using a simple string notation", "homepage": "https://symfony.com", "keywords": ["access", "array", "extraction", "index", "injection", "object", "property", "property-path", "reflection"], "support": {"source": "https://github.com/symfony/property-access/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/property-info", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/property-info.git", "reference": "0f80f818c6728f15de30a4f89866d68e4912ae84"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/property-info/zipball/0f80f818c6728f15de30a4f89866d68e4912ae84", "reference": "0f80f818c6728f15de30a4f89866d68e4912ae84", "shasum": ""}, "require": {"php": ">=8.2", "symfony/string": "^6.4|^7.0", "symfony/type-info": "^7.1"}, "conflict": {"phpdocumentor/reflection-docblock": "<5.2", "phpdocumentor/type-resolver": "<1.5.1", "symfony/dependency-injection": "<6.4", "symfony/serializer": "<6.4"}, "require-dev": {"phpdocumentor/reflection-docblock": "^5.2", "phpstan/phpdoc-parser": "^1.0", "symfony/cache": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\PropertyInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts information about PHP class' properties using metadata of popular sources", "homepage": "https://symfony.com", "keywords": ["doctrine", "phpdoc", "property", "symfony", "type", "validator"], "support": {"source": "https://github.com/symfony/property-info/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/rate-limiter", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/rate-limiter.git", "reference": "f1fbc60e7fed63f1c77bbf8601170cc80fddd95a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/rate-limiter/zipball/f1fbc60e7fed63f1c77bbf8601170cc80fddd95a", "reference": "f1fbc60e7fed63f1c77bbf8601170cc80fddd95a", "shasum": ""}, "require": {"php": ">=8.2", "symfony/options-resolver": "^6.4|^7.0"}, "require-dev": {"psr/cache": "^1.0|^2.0|^3.0", "symfony/lock": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\RateLimiter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a Token Bucket implementation to rate limit input and output in your application", "homepage": "https://symfony.com", "keywords": ["limiter", "rate-limiter"], "support": {"source": "https://github.com/symfony/rate-limiter/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/routing", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/routing.git", "reference": "60c31bab5c45af7f13091b87deb708830f3c96c0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/routing/zipball/60c31bab5c45af7f13091b87deb708830f3c96c0", "reference": "60c31bab5c45af7f13091b87deb708830f3c96c0", "shasum": ""}, "require": {"php": ">=8.2", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"symfony/config": "<6.4", "symfony/dependency-injection": "<6.4", "symfony/yaml": "<6.4"}, "require-dev": {"psr/log": "^1|^2|^3", "symfony/config": "^6.4|^7.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/expression-language": "^6.4|^7.0", "symfony/http-foundation": "^6.4|^7.0", "symfony/yaml": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Routing\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Maps an HTTP request to a set of configuration variables", "homepage": "https://symfony.com", "keywords": ["router", "routing", "uri", "url"], "support": {"source": "https://github.com/symfony/routing/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/service-contracts", "version": "v3.5.0", "source": {"type": "git", "url": "https://github.com/symfony/service-contracts.git", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/service-contracts/zipball/bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "reference": "bd1d9e59a81d8fa4acdcea3f617c581f7475a80f", "shasum": ""}, "require": {"php": ">=8.1", "psr/container": "^1.1|^2.0", "symfony/deprecation-contracts": "^2.5|^3"}, "conflict": {"ext-psr": "<1.1|>=2"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"psr-4": {"Symfony\\Contracts\\Service\\": ""}, "exclude-from-classmap": ["/Test/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Generic abstractions related to writing services", "homepage": "https://symfony.com", "keywords": ["abstractions", "contracts", "decoupling", "interfaces", "interoperability", "standards"], "support": {"source": "https://github.com/symfony/service-contracts/tree/v3.5.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-04-18T09:32:20+00:00"}, {"name": "symfony/string", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/string.git", "reference": "60bc311c74e0af215101235aa6f471bcbc032df2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/string/zipball/60bc311c74e0af215101235aa6f471bcbc032df2", "reference": "60bc311c74e0af215101235aa6f471bcbc032df2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "~1.8", "symfony/polyfill-intl-grapheme": "~1.0", "symfony/polyfill-intl-normalizer": "~1.0", "symfony/polyfill-mbstring": "~1.0"}, "conflict": {"symfony/translation-contracts": "<2.5"}, "require-dev": {"symfony/emoji": "^7.1", "symfony/error-handler": "^6.4|^7.0", "symfony/http-client": "^6.4|^7.0", "symfony/intl": "^6.4|^7.0", "symfony/translation-contracts": "^2.5|^3.0", "symfony/var-exporter": "^6.4|^7.0"}, "type": "library", "autoload": {"files": ["Resources/functions.php"], "psr-4": {"Symfony\\Component\\String\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to strings and deals with bytes, UTF-8 code points and grapheme clusters in a unified way", "homepage": "https://symfony.com", "keywords": ["grapheme", "i18n", "string", "unicode", "utf-8", "utf8"], "support": {"source": "https://github.com/symfony/string/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-04T06:40:14+00:00"}, {"name": "symfony/type-info", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/type-info.git", "reference": "60b28eb733f1453287f1263ed305b96091e0d1dc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/type-info/zipball/60b28eb733f1453287f1263ed305b96091e0d1dc", "reference": "60b28eb733f1453287f1263ed305b96091e0d1dc", "shasum": ""}, "require": {"php": ">=8.2", "psr/container": "^1.1|^2.0"}, "conflict": {"phpstan/phpdoc-parser": "<1.0", "symfony/dependency-injection": "<6.4", "symfony/property-info": "<6.4"}, "require-dev": {"phpstan/phpdoc-parser": "^1.0", "symfony/dependency-injection": "^6.4|^7.0", "symfony/property-info": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\TypeInfo\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Baptiste LEDUC", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Extracts PHP types information.", "homepage": "https://symfony.com", "keywords": ["PHPStan", "phpdoc", "symfony", "type"], "support": {"source": "https://github.com/symfony/type-info/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:59:31+00:00"}, {"name": "symfony/uid", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/uid.git", "reference": "bb59febeecc81528ff672fad5dab7f06db8c8277"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/uid/zipball/bb59febeecc81528ff672fad5dab7f06db8c8277", "reference": "bb59febeecc81528ff672fad5dab7f06db8c8277", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-uuid": "^1.15"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Uid\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides an object-oriented API to generate and represent UIDs", "homepage": "https://symfony.com", "keywords": ["UID", "ulid", "uuid"], "support": {"source": "https://github.com/symfony/uid/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/var-exporter", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/var-exporter.git", "reference": "db82c2b73b88734557cfc30e3270d83fa651b712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/var-exporter/zipball/db82c2b73b88734557cfc30e3270d83fa651b712", "reference": "db82c2b73b88734557cfc30e3270d83fa651b712", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"symfony/property-access": "^6.4|^7.0", "symfony/serializer": "^6.4|^7.0", "symfony/var-dumper": "^6.4|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\VarExporter\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Allows exporting any serializable PHP data structure to plain PHP code", "homepage": "https://symfony.com", "keywords": ["clone", "construct", "export", "hydrate", "instantiate", "lazy-loading", "proxy", "serialize"], "support": {"source": "https://github.com/symfony/var-exporter/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "symfony/web-link", "version": "v6.4.8", "source": {"type": "git", "url": "https://github.com/symfony/web-link.git", "reference": "304c67cefe7128ea3957e9bb1ac6ce08a90a635b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/web-link/zipball/304c67cefe7128ea3957e9bb1ac6ce08a90a635b", "reference": "304c67cefe7128ea3957e9bb1ac6ce08a90a635b", "shasum": ""}, "require": {"php": ">=8.1", "psr/link": "^1.1|^2.0"}, "conflict": {"symfony/http-kernel": "<5.4"}, "provide": {"psr/link-implementation": "1.0|2.0"}, "require-dev": {"symfony/http-kernel": "^5.4|^6.0|^7.0"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\WebLink\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Manages links between resources", "homepage": "https://symfony.com", "keywords": ["dns-prefetch", "http", "http2", "link", "performance", "prefetch", "preload", "prerender", "psr13", "push"], "support": {"source": "https://github.com/symfony/web-link/tree/v6.4.8"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:49:08+00:00"}, {"name": "symfony/yaml", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/yaml.git", "reference": "fa34c77015aa6720469db7003567b9f772492bf2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/yaml/zipball/fa34c77015aa6720469db7003567b9f772492bf2", "reference": "fa34c77015aa6720469db7003567b9f772492bf2", "shasum": ""}, "require": {"php": ">=8.2", "symfony/polyfill-ctype": "^1.8"}, "conflict": {"symfony/console": "<6.4"}, "require-dev": {"symfony/console": "^6.4|^7.0"}, "bin": ["Resources/bin/yaml-lint"], "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Yaml\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Loads and dumps YAML files", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/yaml/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}, {"name": "typo3/class-alias-loader", "version": "v1.1.4", "source": {"type": "git", "url": "https://github.com/TYPO3/class-alias-loader.git", "reference": "f6fc1f5fb04c42195e8e663b43aced4919ef318f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/class-alias-loader/zipball/f6fc1f5fb04c42195e8e663b43aced4919ef318f", "reference": "f6fc1f5fb04c42195e8e663b43aced4919ef318f", "shasum": ""}, "require": {"composer-plugin-api": "^1.0 || ^2.0", "php": ">=5.3.7"}, "replace": {"helhum/class-alias-loader": "*"}, "require-dev": {"composer/composer": "^1.1@dev || ^2.0@dev", "mikey179/vfsstream": "~1.4.0@dev", "phpunit/phpunit": ">4.8 <9"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\ClassAliasLoader\\Plugin", "branch-alias": {"dev-main": "1.1.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\ClassAliasLoader\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Amends the composer class loader to support class aliases to provide backwards compatibility for packages", "homepage": "http://github.com/TYPO3/class-alias-loader", "keywords": ["alias", "autoloader", "classloader", "composer"], "support": {"issues": "https://github.com/TYPO3/class-alias-loader/issues", "source": "https://github.com/TYPO3/class-alias-loader/tree/v1.1.4"}, "time": "2022-08-07T14:48:42+00:00"}, {"name": "typo3/cms-backend", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/backend.git", "reference": "acaa0b87f63a4cdbf00b63c52ca56c1c8fab87a2"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/backend/zipball/acaa0b87f63a4cdbf00b63c52ca56c1c8fab87a2", "reference": "acaa0b87f63a4cdbf00b63c52ca56c1c8fab87a2", "shasum": ""}, "require": {"psr/event-dispatcher": "^1.0", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-about": "self.version", "typo3/cms-context-help": "self.version", "typo3/cms-cshmanual": "self.version", "typo3/cms-func-wizards": "self.version", "typo3/cms-recordlist": "self.version", "typo3/cms-wizard-crpages": "self.version", "typo3/cms-wizard-sortpages": "self.version"}, "suggest": {"typo3/cms-install": "To generate url to install tool in environment toolbar"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Backend\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "backend"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Backend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS backend", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-belog", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/belog.git", "reference": "f051b168c8178af803d2ea26592fdf939bc9560e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/belog/zipball/f051b168c8178af803d2ea26592fdf939bc9560e", "reference": "f051b168c8178af803d2ea26592fdf939bc9560e", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "belog"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Belog\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Log - View logs from the sys_log table in the TYPO3 backend modules System>Log", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-beuser", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/beuser.git", "reference": "ba36598994b6716dfa18ded111f965c9917c9899"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/beuser/zipball/ba36598994b6716dfa18ded111f965c9917c9899", "reference": "ba36598994b6716dfa18ded111f965c9917c9899", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "beuser"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Beuser\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Backend User - TYPO3 backend module System>Backend Users for managing backend users and groups.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-cli", "version": "3.1.1", "source": {"type": "git", "url": "https://github.com/TYPO3/cms-cli.git", "reference": "d8947732ff5a6dc52829e88cb1c761124475c0e8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/cms-cli/zipball/d8947732ff5a6dc52829e88cb1c761124475c0e8", "reference": "d8947732ff5a6dc52829e88cb1c761124475c0e8", "shasum": ""}, "require": {"php": "^7.0 || ^8.0"}, "bin": ["typo3"], "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "description": "TYPO3 command line binary", "homepage": "https://typo3.org", "support": {"issues": "https://github.com/TYPO3/cms-cli/issues", "source": "https://github.com/TYPO3/cms-cli/tree/3.1.1"}, "time": "2023-08-15T10:14:53+00:00"}, {"name": "typo3/cms-composer-installers", "version": "v5.0.0", "source": {"type": "git", "url": "https://github.com/TYPO3/CmsComposerInstallers.git", "reference": "71356484e6ccadf45acdde6489823c7af925b144"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/CmsComposerInstallers/zipball/71356484e6ccadf45acdde6489823c7af925b144", "reference": "71356484e6ccadf45acdde6489823c7af925b144", "shasum": ""}, "require": {"composer-plugin-api": "^2.1.0", "php": "^8.1"}, "replace": {"lw/typo3cms-installers": "*", "netresearch/composer-installers": "*"}, "require-dev": {"composer/composer": "^2.1", "friendsofphp/php-cs-fixer": "^2.18", "overtrue/phplint": "^2.0", "phpunit/phpunit": "^8.5"}, "type": "composer-plugin", "extra": {"class": "TYPO3\\CMS\\Composer\\Installer\\Plugin", "branch-alias": {"dev-main": "5.0.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Composer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 CMS Core Team", "homepage": "https://forge.typo3.org/projects/typo3cms-core", "role": "Developer"}, {"name": "The TYPO3 Community", "homepage": "https://typo3.org/community/", "role": "Contributor"}], "description": "TYPO3 CMS Installers", "homepage": "https://github.com/TYPO3/CmsComposerInstallers", "keywords": ["cms", "core", "extension", "installer", "typo3"], "support": {"general": "https://typo3.org/support/", "issues": "https://github.com/TYPO3/CmsComposerInstallers/issues", "source": "https://github.com/TYPO3/CmsComposerInstallers/tree/v5.0.0"}, "time": "2022-09-30T14:36:05+00:00"}, {"name": "typo3/cms-core", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/core.git", "reference": "2523cad81a903f471a4662665024f99486ff3060"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/core/zipball/2523cad81a903f471a4662665024f99486ff3060", "reference": "2523cad81a903f471a4662665024f99486ff3060", "shasum": ""}, "require": {"bacon/bacon-qr-code": "^2.0.7", "christian-riesen/base32": "^1.6", "composer-runtime-api": "^2.1", "doctrine/annotations": "^1.13.3 || ^2.0", "doctrine/dbal": "^3.8.1", "doctrine/event-manager": "^2.0", "doctrine/lexer": "^2.0 || ^3.0", "egulias/email-validator": "^4.0", "enshrined/svg-sanitize": "^0.18.0", "ext-dom": "*", "ext-intl": "*", "ext-json": "*", "ext-libxml": "*", "ext-mbstring": "*", "ext-pcre": "*", "ext-pdo": "*", "ext-session": "*", "ext-tokenizer": "*", "ext-xml": "*", "firebase/php-jwt": "^6.4.0", "guzzlehttp/guzzle": "^7.7.0", "guzzlehttp/psr7": "^2.5.0", "lolli42/finediff": "^1.0.2", "masterminds/html5": "^2.7.6", "php": "^8.1", "psr/container": "^2.0", "psr/event-dispatcher": "^1.0", "psr/http-client": "^1.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.1 || ^2.0", "psr/http-server-handler": "^1.0", "psr/http-server-middleware": "^1.0", "psr/log": "^2.0 || ^3.0", "symfony/config": "^6.4 || ^7.0", "symfony/console": "^6.4 || ^7.0", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/doctrine-messenger": "^6.4 || ^7.0", "symfony/event-dispatcher-contracts": "^3.1", "symfony/expression-language": "^6.4 || ^7.0", "symfony/filesystem": "^6.4 || ^7.0", "symfony/finder": "^6.4 || ^7.0", "symfony/http-foundation": "^6.4 || ^7.0", "symfony/mailer": "^6.4 || ^7.0", "symfony/messenger": "^6.4 || ^7.0", "symfony/mime": "^6.4 || ^7.0", "symfony/options-resolver": "^6.4 || ^7.0", "symfony/rate-limiter": "^6.4 || ^7.0", "symfony/routing": "^6.4 || ^7.0", "symfony/uid": "^6.4 || ^7.0", "symfony/yaml": "^6.4 || ^7.0", "typo3/class-alias-loader": "^1.1.4", "typo3/cms-cli": "^3.1", "typo3/cms-composer-installers": "^5.0", "typo3/html-sanitizer": "^2.1.4", "typo3fluid/fluid": "^2.9.2"}, "conflict": {"hoa/core": "*", "typo3/cms": "*"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "replace": {"typo3/cms-lang": "self.version", "typo3/cms-saltedpasswords": "self.version", "typo3/cms-sv": "self.version"}, "suggest": {"ext-apcu": "Needed when non-default APCU based cache backends are used", "ext-fileinfo": "Used for proper file type detection in the file abstraction layer", "ext-gd": "GDlib/Freetype is required for building images with text (GIFBUILDER) and can also be used to scale images", "ext-mysqli": "", "ext-openssl": "OpenSSL is required for sending SMTP mails over an encrypted channel endpoint", "ext-zip": "", "ext-zlib": "TYPO3 uses zlib for amongst others output compression and un/packing t3x extension files"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Core\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "core"}}, "autoload": {"files": ["Resources/PHP/GlobalDebugFunctions.php"], "psr-4": {"TYPO3\\CMS\\Core\\": "Classes/"}, "classmap": ["Resources/PHP/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Core", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-dashboard", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/dashboard.git", "reference": "f868cd37557b27209dd4d2336dba25d8bb6ec988"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/dashboard/zipball/f868cd37557b27209dd4d2336dba25d8bb6ec988", "reference": "f868cd37557b27209dd4d2336dba25d8bb6ec988", "shasum": ""}, "require": {"typo3/cms-backend": "12.4.16", "typo3/cms-core": "12.4.16", "typo3/cms-extbase": "12.4.16", "typo3/cms-fluid": "12.4.16", "typo3/cms-frontend": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "dashboard", "Package": {"serviceProvider": "TYPO3\\CMS\\Dashboard\\ServiceProvider", "partOfFactoryDefault": true}}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Dashboard\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Dashboard - TYPO3 backend module used to configure and create backend widgets.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-dashboard/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-extbase", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extbase.git", "reference": "ffa17726c3e4085d90036ef3840afbc8535a6686"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extbase/zipball/ffa17726c3e4085d90036ef3840afbc8535a6686", "reference": "ffa17726c3e4085d90036ef3840afbc8535a6686", "shasum": ""}, "require": {"doctrine/instantiator": "^1.5 || ^2.0", "phpdocumentor/reflection-docblock": "^5.2", "phpdocumentor/type-resolver": "^1.7.1", "symfony/dependency-injection": "^6.4 || ^7.0", "symfony/property-access": "^6.4 || ^7.0", "symfony/property-info": "^6.4 || ^7.0", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "Additional scheduler tasks"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Extbase\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extbase"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extbase\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Extbase - Extension framework to create TYPO3 frontend plugins and TYPO3 backend modules.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-extensionmanager", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/extensionmanager.git", "reference": "3c4f328bd41e8428327848f3e8bbbdee8904d0fa"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/extensionmanager/zipball/3c4f328bd41e8428327848f3e8bbbdee8904d0fa", "reference": "3c4f328bd41e8428327848f3e8bbbdee8904d0fa", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "extensionmanager"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Extensionmanager\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Extension Manager - Backend module (Admin Tools>Extensions) for viewing and managing extensions.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-filelist", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/filelist.git", "reference": "a8e47eb7a8a983a9eebbffac2679084380f96633"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/filelist/zipball/a8e47eb7a8a983a9eebbffac2679084380f96633", "reference": "a8e47eb7a8a983a9eebbffac2679084380f96633", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "filelist"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Filelist\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Filelist - TYPO3 backend module (File>Filelist) used for managing files.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-fluid", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid.git", "reference": "c022b1696a743a13e0d9440c8fc4bc661ab159c3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid/zipball/c022b1696a743a13e0d9440c8fc4bc661ab159c3", "reference": "c022b1696a743a13e0d9440c8fc4bc661ab159c3", "shasum": ""}, "require": {"symfony/dependency-injection": "^6.4 || ^7.0", "typo3/cms-core": "12.4.16", "typo3/cms-extbase": "12.4.16", "typo3fluid/fluid": "^2.9.2"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Fluid\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "fluid"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Fluid\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Fluid Integration - Integration of the Fluid templating engine into TYPO3.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/other/typo3/view-helper-reference/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-fluid-styled-content", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/fluid_styled_content.git", "reference": "1f64bf154db8fdf7aac72a5059bd701bfca5bf8f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/fluid_styled_content/zipball/1f64bf154db8fdf7aac72a5059bd701bfca5bf8f", "reference": "1f64bf154db8fdf7aac72a5059bd701bfca5bf8f", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16", "typo3/cms-fluid": "12.4.16", "typo3/cms-frontend": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "fluid_styled_content"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\FluidStyledContent\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Fluid Styled Content - Fluid templates for TYPO3 content elements.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-fluid-styled-content/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-frontend", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/frontend.git", "reference": "682030d1498821a2eb7d677f2aa06bcf82fb0549"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/frontend/zipball/682030d1498821a2eb7d677f2aa06bcf82fb0549", "reference": "682030d1498821a2eb7d677f2aa06bcf82fb0549", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-adminpanel": "Provides additional information and functionality for backend users in the frontend."}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Frontend\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "frontend"}, "typo3/class-alias-loader": {"class-alias-maps": ["Migrations/Code/ClassAliasMap.php"]}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Frontend\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Frontend", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-impexp", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/impexp.git", "reference": "d7c4877c5cdf16e0d67b3168722dce701b8a5e98"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/impexp/zipball/d7c4877c5cdf16e0d67b3168722dce701b8a5e98", "reference": "d7c4877c5cdf16e0d67b3168722dce701b8a5e98", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "impexp"}, "branch-alias": {"dev-main": "12.4.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Impexp\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Import/Export - Tool for importing and exporting records using XML or the custom T3D format.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-impexp/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-info", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/info.git", "reference": "8b79ab7f5af6d72a88c421ff9f9598cdce3c1cdd"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/info/zipball/8b79ab7f5af6d72a88c421ff9f9598cdce3c1cdd", "reference": "8b79ab7f5af6d72a88c421ff9f9598cdce3c1cdd", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "replace": {"typo3/cms-info-pagetsconfig": "self.version"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "info"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Info\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Info - TYPO3 backend module for displaying information, such as a pagetree overview and localization information.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-install", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/install.git", "reference": "f7dcbad953e1fc7a02e16d402cb87441cf57d9df"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/install/zipball/f7dcbad953e1fc7a02e16d402cb87441cf57d9df", "reference": "f7dcbad953e1fc7a02e16d402cb87441cf57d9df", "shasum": ""}, "require": {"doctrine/dbal": "^3.8.1", "guzzlehttp/promises": "^1.5.2 || ^2.0", "nikic/php-parser": "^4.15.4", "symfony/finder": "^6.4 || ^7.0", "symfony/http-foundation": "^6.4 || ^7.0", "typo3/cms-core": "12.4.16", "typo3/cms-extbase": "12.4.16", "typo3/cms-fluid": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"serviceProvider": "TYPO3\\CMS\\Install\\ServiceProvider", "protected": true, "partOfFactoryDefault": true, "partOfMinimalUsableSystem": true}, "extension-key": "install"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Install\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Install Tool - The Install Tool is used for installation, upgrade, system administration and setup tasks.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-linkvalidator", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/linkvalidator.git", "reference": "25bb21f7ad598d07c947776752b5977e8d5bf43f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/linkvalidator/zipball/25bb21f7ad598d07c947776752b5977e8d5bf43f", "reference": "25bb21f7ad598d07c947776752b5977e8d5bf43f", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16", "typo3/cms-info": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-scheduler": "Regular checks of links"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "linkvalidator"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Linkvalidator\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS LinkValidator - Checks for broken links and displays results in the (Info>LinkValidator) backend module.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-linkvalidator/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-lowlevel", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/lowlevel.git", "reference": "82f3b79fa0ce6fdc68e52cc1ef4c88e02f42c415"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/lowlevel/zipball/82f3b79fa0ce6fdc68e52cc1ef4c88e02f42c415", "reference": "82f3b79fa0ce6fdc68e52cc1ef4c88e02f42c415", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "lowlevel"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Lowlevel\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Lowlevel - Technical analysis of the system. This includes raw database search, checking relations, counting pages and records etc.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-reactions", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/reactions.git", "reference": "247fbb7314f61c67b623fa67c8544dddb3b2836a"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/reactions/zipball/247fbb7314f61c67b623fa67c8544dddb3b2836a", "reference": "247fbb7314f61c67b623fa67c8544dddb3b2836a", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-lowlevel": "To display registered reactions in the configuration module"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "reactions"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Reactions\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Reactions - Handle incoming Webhooks for TYPO3", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-reactions/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-redirects", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/redirects.git", "reference": "53477cd0fab9b413b244a8869eaf4e60be93fdbe"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/redirects/zipball/53477cd0fab9b413b244a8869eaf4e60be93fdbe", "reference": "53477cd0fab9b413b244a8869eaf4e60be93fdbe", "shasum": ""}, "require": {"doctrine/dbal": "^3.8.1", "psr/http-message": "^1.1 || ^2.0", "psr/log": "^2.0 || ^3.0", "symfony/console": "^6.4 || ^7.0", "typo3/cms-backend": "12.4.16", "typo3/cms-core": "12.4.16", "typo3fluid/fluid": "^2.9.2"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-reports": "Get reports of redirects", "typo3/cms-scheduler": "Execute commands to update redirect status"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "redirects", "Package": {"partOfFactoryDefault": true}}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Redirects\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Redirects - Create manual redirects, list existing redirects and automatically createredirects on slug changes.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-redirects/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-rte-ckeditor", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/rte_ckeditor.git", "reference": "06bd24f162dfc0b7eefc76338060cd1c14e23a30"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/rte_ckeditor/zipball/06bd24f162dfc0b7eefc76338060cd1c14e23a30", "reference": "06bd24f162dfc0b7eefc76338060cd1c14e23a30", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-setup": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "rte_ckeditor"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\RteCKEditor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS RTE CKEditor - Integration of CKEditor as a Rich Text Editor for the TYPO3 backend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-rte-ckeditor/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-scheduler", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/scheduler.git", "reference": "c0fdbd81a8ca5b79dc94a0b59ab5b32dca3daf2b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/scheduler/zipball/c0fdbd81a8ca5b79dc94a0b59ab5b32dca3daf2b", "reference": "c0fdbd81a8ca5b79dc94a0b59ab5b32dca3daf2b", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "scheduler"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Scheduler\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Scheduler - Schedule tasks to run once or periodically at a specific time.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-scheduler/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-seo", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/seo.git", "reference": "a531ab32f21775de9bca50fba23669e4b9813e0d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/seo/zipball/a531ab32f21775de9bca50fba23669e4b9813e0d", "reference": "a531ab32f21775de9bca50fba23669e4b9813e0d", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16", "typo3/cms-extbase": "12.4.16", "typo3/cms-frontend": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-dashboard": "TYPO3 users can add widgets that can help to optimise their website for search engines"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "seo", "Package": {"partOfFactoryDefault": true}}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Seo\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS SEO - SEO features including specific fields for SEO purposes, rendering of HTML meta tags and sitemaps.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-seo/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-setup", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/setup.git", "reference": "f98d213628d739d288b2c09fb832114d73998c12"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/setup/zipball/f98d213628d739d288b2c09fb832114d73998c12", "reference": "f98d213628d739d288b2c09fb832114d73998c12", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "setup"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Setup\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Setup - Allows users to edit a limited set of options for their user profile, including preferred language, their name and email address.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-sys-note", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/sys_note.git", "reference": "a2f8b1cabc8886c96964fc3b4d755701abf73fde"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/sys_note/zipball/a2f8b1cabc8886c96964fc3b4d755701abf73fde", "reference": "a2f8b1cabc8886c96964fc3b4d755701abf73fde", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "sys_note"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\SysNote\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS System Notes - Records with messages which can be placed on any page and contain instructions or other information related to a page or section.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-t3editor", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/t3editor.git", "reference": "143086bae9abea1543f7a57261b09fc0c7af0171"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/t3editor/zipball/143086bae9abea1543f7a57261b09fc0c7af0171", "reference": "143086bae9abea1543f7a57261b09fc0c7af0171", "shasum": ""}, "require": {"ext-libxml": "*", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "t3editor"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\T3editor\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS T3Editor - JavaScript-driven editor with syntax highlighting and code completion. Based on CodeMirror.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-t3editor/main/en-us", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-tstemplate", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/tstemplate.git", "reference": "b8e2e4a87c4a17417bb9c0df6e3d5728c0587ed3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/tstemplate/zipball/b8e2e4a87c4a17417bb9c0df6e3d5728c0587ed3", "reference": "b8e2e4a87c4a17417bb9c0df6e3d5728c0587ed3", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "tstemplate"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Tstemplate\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS TypoScript - TYPO3 backend module for the management of TypoScript records for the CMS frontend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-viewpage", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/viewpage.git", "reference": "25e2db06f0fdb3b4548077c9269654458b3140e3"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/viewpage/zipball/25e2db06f0fdb3b4548077c9269654458b3140e3", "reference": "25e2db06f0fdb3b4548077c9269654458b3140e3", "shasum": ""}, "require": {"typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"Package": {"partOfFactoryDefault": true}, "extension-key": "viewpage"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Viewpage\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Viewpage - Use the (Web>View) backend module to view a frontend page inside the TYPO3 backend.", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/cms-webhooks", "version": "v12.4.16", "source": {"type": "git", "url": "https://github.com/TYPO3-CMS/webhooks.git", "reference": "42cd68fe4d7214efb6a35f0370ae628f1b704f2c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3-CMS/webhooks/zipball/42cd68fe4d7214efb6a35f0370ae628f1b704f2c", "reference": "42cd68fe4d7214efb6a35f0370ae628f1b704f2c", "shasum": ""}, "require": {"symfony/uid": "^6.4 || ^7.0", "typo3/cms-core": "12.4.16"}, "conflict": {"typo3/cms": "*"}, "suggest": {"typo3/cms-lowlevel": "To display registered webhooks in the configuration module"}, "type": "typo3-cms-framework", "extra": {"branch-alias": {"dev-main": "12.4.x-dev"}, "typo3/cms": {"extension-key": "webhooks"}}, "autoload": {"psr-4": {"TYPO3\\CMS\\Webhooks\\": "Classes/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["GPL-2.0-or-later"], "authors": [{"name": "TYPO3 Core Team", "email": "<EMAIL>", "role": "Developer"}], "description": "TYPO3 CMS Webhooks - Handle outgoing Webhooks for TYPO3", "homepage": "https://typo3.org", "support": {"chat": "https://typo3.org/help", "docs": "https://docs.typo3.org/c/typo3/cms-webhooks/main/en-us/", "issues": "https://forge.typo3.org", "source": "https://github.com/typo3/typo3"}, "time": "2024-06-11T07:52:39+00:00"}, {"name": "typo3/html-sanitizer", "version": "v2.1.4", "source": {"type": "git", "url": "https://github.com/TYPO3/html-sanitizer.git", "reference": "b8f90717251d968c49dc77f8c1e5912e2fbe0dff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/html-sanitizer/zipball/b8f90717251d968c49dc77f8c1e5912e2fbe0dff", "reference": "b8f90717251d968c49dc77f8c1e5912e2fbe0dff", "shasum": ""}, "require": {"ext-dom": "*", "masterminds/html5": "^2.7.6", "php": "^7.2 || ^8.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"phpunit/phpunit": "^8.5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.x-dev"}}, "autoload": {"psr-4": {"TYPO3\\HtmlSanitizer\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "HTML sanitizer aiming to provide XSS-safe markup based on explicitly allowed tags, attributes and values.", "support": {"issues": "https://github.com/TYPO3/html-sanitizer/issues", "source": "https://github.com/TYPO3/html-sanitizer/tree/v2.1.4"}, "time": "2023-11-14T07:41:08+00:00"}, {"name": "typo3fluid/fluid", "version": "2.11.0", "source": {"type": "git", "url": "https://github.com/TYPO3/Fluid.git", "reference": "392c7d5e494a02131843ec8b2a5ef1d3ca4dcdf5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/TYPO3/Fluid/zipball/392c7d5e494a02131843ec8b2a5ef1d3ca4dcdf5", "reference": "392c7d5e494a02131843ec8b2a5ef1d3ca4dcdf5", "shasum": ""}, "require": {"ext-mbstring": "*", "php": "^8.1"}, "require-dev": {"ext-json": "*", "friendsofphp/php-cs-fixer": "^3.52.1", "phpstan/phpstan": "^1.10.14", "phpstan/phpstan-phpunit": "^1.3.11", "phpunit/phpunit": "^10.2.6"}, "suggest": {"ext-json": "PHP JSON is needed when using JSONVariableProvider: A relatively rare use case"}, "bin": ["bin/fluid"], "type": "library", "autoload": {"psr-4": {"TYPO3Fluid\\Fluid\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["LGPL-3.0-or-later"], "description": "The TYPO3 Fluid template rendering engine", "homepage": "https://github.com/TYPO3/Fluid", "support": {"docs": "https://docs.typo3.org/other/typo3fluid/fluid/main/en-us/", "issues": "https://github.com/TYPO3/Fluid/issues", "source": "https://github.com/TYPO3/Fluid"}, "time": "2024-04-05T13:06:34+00:00"}, {"name": "webmozart/assert", "version": "1.11.0", "source": {"type": "git", "url": "https://github.com/webmozarts/assert.git", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/webmozarts/assert/zipball/11cb2199493b2f8a3b53e7f19068fc6aac760991", "reference": "11cb2199493b2f8a3b53e7f19068fc6aac760991", "shasum": ""}, "require": {"ext-ctype": "*", "php": "^7.2 || ^8.0"}, "conflict": {"phpstan/phpstan": "<0.12.20", "vimeo/psalm": "<4.6.1 || 4.6.2"}, "require-dev": {"phpunit/phpunit": "^8.5.13"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.10-dev"}}, "autoload": {"psr-4": {"Webmozart\\Assert\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "b<PERSON><PERSON><PERSON>@gmail.com"}], "description": "Assertions to validate method input/output with nice error messages.", "keywords": ["assert", "check", "validate"], "support": {"issues": "https://github.com/webmozarts/assert/issues", "source": "https://github.com/webmozarts/assert/tree/1.11.0"}, "time": "2022-06-03T18:03:27+00:00"}], "packages-dev": [{"name": "clue/ndjson-react", "version": "v1.3.0", "source": {"type": "git", "url": "https://github.com/clue/reactphp-ndjson.git", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/clue/reactphp-ndjson/zipball/392dc165fce93b5bb5c637b67e59619223c931b0", "reference": "392dc165fce93b5bb5c637b67e59619223c931b0", "shasum": ""}, "require": {"php": ">=5.3", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35", "react/event-loop": "^1.2"}, "type": "library", "autoload": {"psr-4": {"Clue\\React\\NDJson\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Streaming newline-delimited JSON (NDJSON) parser and encoder for ReactPHP.", "homepage": "https://github.com/clue/reactphp-ndjson", "keywords": ["NDJSON", "json", "jsonlines", "newline", "reactphp", "streaming"], "support": {"issues": "https://github.com/clue/reactphp-ndjson/issues", "source": "https://github.com/clue/reactphp-ndjson/tree/v1.3.0"}, "funding": [{"url": "https://clue.engineering/support", "type": "custom"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-12-23T10:58:28+00:00"}, {"name": "composer/pcre", "version": "3.1.4", "source": {"type": "git", "url": "https://github.com/composer/pcre.git", "reference": "04229f163664973f68f38f6f73d917799168ef24"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/pcre/zipball/04229f163664973f68f38f6f73d917799168ef24", "reference": "04229f163664973f68f38f6f73d917799168ef24", "shasum": ""}, "require": {"php": "^7.4 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.3", "phpstan/phpstan-strict-rules": "^1.1", "symfony/phpunit-bridge": "^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Pcre\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "PCRE wrapping library that offers type-safe preg_* replacements.", "keywords": ["PCRE", "preg", "regex", "regular expression"], "support": {"issues": "https://github.com/composer/pcre/issues", "source": "https://github.com/composer/pcre/tree/3.1.4"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-27T13:40:54+00:00"}, {"name": "composer/semver", "version": "3.4.0", "source": {"type": "git", "url": "https://github.com/composer/semver.git", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/semver/zipball/35e8d0af4486141bc745f23a29cc2091eb624a32", "reference": "35e8d0af4486141bc745f23a29cc2091eb624a32", "shasum": ""}, "require": {"php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^1.4", "symfony/phpunit-bridge": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "3.x-dev"}}, "autoload": {"psr-4": {"Composer\\Semver\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://www.naderman.de"}, {"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://robbast.nl"}], "description": "Semver library that offers utilities, version constraint parsing and validation.", "keywords": ["semantic", "semver", "validation", "versioning"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/semver/issues", "source": "https://github.com/composer/semver/tree/3.4.0"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2023-08-31T09:50:34+00:00"}, {"name": "composer/xdebug-handler", "version": "3.0.5", "source": {"type": "git", "url": "https://github.com/composer/xdebug-handler.git", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/xdebug-handler/zipball/6c1925561632e83d60a44492e0b344cf48ab85ef", "reference": "6c1925561632e83d60a44492e0b344cf48ab85ef", "shasum": ""}, "require": {"composer/pcre": "^1 || ^2 || ^3", "php": "^7.2.5 || ^8.0", "psr/log": "^1 || ^2 || ^3"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpstan/phpstan-strict-rules": "^1.1", "phpunit/phpunit": "^8.5 || ^9.6 || ^10.5"}, "type": "library", "autoload": {"psr-4": {"Composer\\XdebugHandler\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "john-s<PERSON><PERSON><PERSON>@blueyonder.co.uk"}], "description": "Restarts a process without Xdebug.", "keywords": ["Xdebug", "performance"], "support": {"irc": "ircs://irc.libera.chat:6697/composer", "issues": "https://github.com/composer/xdebug-handler/issues", "source": "https://github.com/composer/xdebug-handler/tree/3.0.5"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2024-05-06T16:37:16+00:00"}, {"name": "evenement/evenement", "version": "v3.0.2", "source": {"type": "git", "url": "https://github.com/igorw/evenement.git", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/igorw/evenement/zipball/0a16b0d71ab13284339abb99d9d2bd813640efbc", "reference": "0a16b0d71ab13284339abb99d9d2bd813640efbc", "shasum": ""}, "require": {"php": ">=7.0"}, "require-dev": {"phpunit/phpunit": "^9 || ^6"}, "type": "library", "autoload": {"psr-4": {"Evenement\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "i<PERSON>@wiedler.ch"}], "description": "Événement is a very simple event dispatching library for PHP", "keywords": ["event-dispatcher", "event-emitter"], "support": {"issues": "https://github.com/igorw/evenement/issues", "source": "https://github.com/igorw/evenement/tree/v3.0.2"}, "time": "2023-08-08T05:53:35+00:00"}, {"name": "fidry/cpu-core-counter", "version": "1.1.0", "source": {"type": "git", "url": "https://github.com/theofidry/cpu-core-counter.git", "reference": "f92996c4d5c1a696a6a970e20f7c4216200fcc42"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/theofidry/cpu-core-counter/zipball/f92996c4d5c1a696a6a970e20f7c4216200fcc42", "reference": "f92996c4d5c1a696a6a970e20f7c4216200fcc42", "shasum": ""}, "require": {"php": "^7.2 || ^8.0"}, "require-dev": {"fidry/makefile": "^0.2.0", "fidry/php-cs-fixer-config": "^1.1.2", "phpstan/extension-installer": "^1.2.0", "phpstan/phpstan": "^1.9.2", "phpstan/phpstan-deprecation-rules": "^1.0.0", "phpstan/phpstan-phpunit": "^1.2.2", "phpstan/phpstan-strict-rules": "^1.4.4", "phpunit/phpunit": "^8.5.31 || ^9.5.26", "webmozarts/strict-phpunit": "^7.5"}, "type": "library", "autoload": {"psr-4": {"Fidry\\CpuCoreCounter\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Théo FIDRY", "email": "<EMAIL>"}], "description": "Tiny utility to get the number of CPU cores.", "keywords": ["CPU", "core"], "support": {"issues": "https://github.com/theofidry/cpu-core-counter/issues", "source": "https://github.com/theofidry/cpu-core-counter/tree/1.1.0"}, "funding": [{"url": "https://github.com/theofidry", "type": "github"}], "time": "2024-02-07T09:43:46+00:00"}, {"name": "friendsofphp/php-cs-fixer", "version": "v3.59.3", "source": {"type": "git", "url": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer.git", "reference": "30ba9ecc2b0e5205e578fe29973c15653d9bfd29"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHP-CS-Fixer/PHP-CS-Fixer/zipball/30ba9ecc2b0e5205e578fe29973c15653d9bfd29", "reference": "30ba9ecc2b0e5205e578fe29973c15653d9bfd29", "shasum": ""}, "require": {"clue/ndjson-react": "^1.0", "composer/semver": "^3.4", "composer/xdebug-handler": "^3.0.3", "ext-filter": "*", "ext-json": "*", "ext-tokenizer": "*", "fidry/cpu-core-counter": "^1.0", "php": "^7.4 || ^8.0", "react/child-process": "^0.6.5", "react/event-loop": "^1.0", "react/promise": "^2.0 || ^3.0", "react/socket": "^1.0", "react/stream": "^1.0", "sebastian/diff": "^4.0 || ^5.0 || ^6.0", "symfony/console": "^5.4 || ^6.0 || ^7.0", "symfony/event-dispatcher": "^5.4 || ^6.0 || ^7.0", "symfony/filesystem": "^5.4 || ^6.0 || ^7.0", "symfony/finder": "^5.4 || ^6.0 || ^7.0", "symfony/options-resolver": "^5.4 || ^6.0 || ^7.0", "symfony/polyfill-mbstring": "^1.28", "symfony/polyfill-php80": "^1.28", "symfony/polyfill-php81": "^1.28", "symfony/process": "^5.4 || ^6.0 || ^7.0", "symfony/stopwatch": "^5.4 || ^6.0 || ^7.0"}, "require-dev": {"facile-it/paraunit": "^1.3 || ^2.3", "infection/infection": "^0.29.5", "justinrainbow/json-schema": "^5.2", "keradus/cli-executor": "^2.1", "mikey179/vfsstream": "^1.6.11", "php-coveralls/php-coveralls": "^2.7", "php-cs-fixer/accessible-object": "^1.1", "php-cs-fixer/phpunit-constraint-isidenticalstring": "^1.5", "php-cs-fixer/phpunit-constraint-xmlmatchesxsd": "^1.5", "phpunit/phpunit": "^9.6.19 || ^10.5.21 || ^11.2", "symfony/var-dumper": "^5.4 || ^6.0 || ^7.0", "symfony/yaml": "^5.4 || ^6.0 || ^7.0"}, "suggest": {"ext-dom": "For handling output formats in XML", "ext-mbstring": "For handling non-UTF8 characters."}, "bin": ["php-cs-fixer"], "type": "application", "autoload": {"psr-4": {"PhpCsFixer\\": "src/"}, "exclude-from-classmap": ["src/Fixer/Internal/*"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "A tool to automatically fix PHP code style", "keywords": ["Static code analysis", "fixer", "standards", "static analysis"], "support": {"issues": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/issues", "source": "https://github.com/PHP-CS-Fixer/PHP-CS-Fixer/tree/v3.59.3"}, "funding": [{"url": "https://github.com/keradus", "type": "github"}], "time": "2024-06-16T14:17:03+00:00"}, {"name": "mogic/mogic-phpcs", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/mogic-le/mogic-phpcs.git", "reference": "de4e7c08cc20549087b1a33414d9f7c90beace4e"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/mogic-le/mogic-phpcs/zipball/de4e7c08cc20549087b1a33414d9f7c90beace4e", "reference": "de4e7c08cc20549087b1a33414d9f7c90beace4e", "shasum": ""}, "require-dev": {"squizlabs/php_codesniffer": "^3.6"}, "default-branch": true, "type": "library", "notification-url": "https://packagist.org/downloads/", "license": ["proprietary"], "description": "PHP coding standard used at Mogic GmbH", "support": {"source": "https://github.com/mogic-le/mogic-phpcs/tree/v2.0.1"}, "time": "2024-06-19T08:12:35+00:00"}, {"name": "react/cache", "version": "v1.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/cache.git", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/cache/zipball/d47c472b64aa5608225f47965a484b75c7817d5b", "reference": "d47c472b64aa5608225f47965a484b75c7817d5b", "shasum": ""}, "require": {"php": ">=5.3.0", "react/promise": "^3.0 || ^2.0 || ^1.1"}, "require-dev": {"phpunit/phpunit": "^9.5 || ^5.7 || ^4.8.35"}, "type": "library", "autoload": {"psr-4": {"React\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, Promise-based cache interface for ReactPHP", "keywords": ["cache", "caching", "promise", "reactphp"], "support": {"issues": "https://github.com/reactphp/cache/issues", "source": "https://github.com/reactphp/cache/tree/v1.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2022-11-30T15:59:55+00:00"}, {"name": "react/child-process", "version": "v0.6.5", "source": {"type": "git", "url": "https://github.com/reactphp/child-process.git", "reference": "e71eb1aa55f057c7a4a0d08d06b0b0a484bead43"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/child-process/zipball/e71eb1aa55f057c7a4a0d08d06b0b0a484bead43", "reference": "e71eb1aa55f057c7a4a0d08d06b0b0a484bead43", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/event-loop": "^1.2", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.3 || ^5.7 || ^4.8.35", "react/socket": "^1.8", "sebastian/environment": "^5.0 || ^3.0 || ^2.0 || ^1.0"}, "type": "library", "autoload": {"psr-4": {"React\\ChildProcess\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven library for executing child processes with ReactPHP.", "keywords": ["event-driven", "process", "reactphp"], "support": {"issues": "https://github.com/reactphp/child-process/issues", "source": "https://github.com/reactphp/child-process/tree/v0.6.5"}, "funding": [{"url": "https://github.com/WyriHaximus", "type": "github"}, {"url": "https://github.com/clue", "type": "github"}], "time": "2022-09-16T13:41:56+00:00"}, {"name": "react/dns", "version": "v1.13.0", "source": {"type": "git", "url": "https://github.com/reactphp/dns.git", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/dns/zipball/eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "reference": "eb8ae001b5a455665c89c1df97f6fb682f8fb0f5", "shasum": ""}, "require": {"php": ">=5.3.0", "react/cache": "^1.0 || ^0.6 || ^0.5", "react/event-loop": "^1.2", "react/promise": "^3.2 || ^2.7 || ^1.2.1"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4.3 || ^3 || ^2", "react/promise-timer": "^1.11"}, "type": "library", "autoload": {"psr-4": {"React\\Dns\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async DNS resolver for ReactPHP", "keywords": ["async", "dns", "dns-resolver", "reactphp"], "support": {"issues": "https://github.com/reactphp/dns/issues", "source": "https://github.com/reactphp/dns/tree/v1.13.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-13T14:18:03+00:00"}, {"name": "react/event-loop", "version": "v1.5.0", "source": {"type": "git", "url": "https://github.com/reactphp/event-loop.git", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/event-loop/zipball/bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "reference": "bbe0bd8c51ffc05ee43f1729087ed3bdf7d53354", "shasum": ""}, "require": {"php": ">=5.3.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "suggest": {"ext-pcntl": "For signal handling support when using the StreamSelectLoop"}, "type": "library", "autoload": {"psr-4": {"React\\EventLoop\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "ReactPHP's core reactor event loop that libraries can use for evented I/O.", "keywords": ["asynchronous", "event-loop"], "support": {"issues": "https://github.com/reactphp/event-loop/issues", "source": "https://github.com/reactphp/event-loop/tree/v1.5.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-13T13:48:05+00:00"}, {"name": "react/promise", "version": "v3.2.0", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "8a164643313c71354582dc850b42b33fa12a4b63"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/8a164643313c71354582dc850b42b33fa12a4b63", "reference": "8a164643313c71354582dc850b42b33fa12a4b63", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpstan/phpstan": "1.10.39 || 1.4.10", "phpunit/phpunit": "^9.6 || ^7.5"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v3.2.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-05-24T10:39:05+00:00"}, {"name": "react/socket", "version": "v1.15.0", "source": {"type": "git", "url": "https://github.com/reactphp/socket.git", "reference": "216d3aec0b87f04a40ca04f481e6af01bdd1d038"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/socket/zipball/216d3aec0b87f04a40ca04f481e6af01bdd1d038", "reference": "216d3aec0b87f04a40ca04f481e6af01bdd1d038", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.0", "react/dns": "^1.11", "react/event-loop": "^1.2", "react/promise": "^3 || ^2.6 || ^1.2.1", "react/stream": "^1.2"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36", "react/async": "^4 || ^3 || ^2", "react/promise-stream": "^1.4", "react/promise-timer": "^1.10"}, "type": "library", "autoload": {"psr-4": {"React\\Socket\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Async, streaming plaintext TCP/IP and secure TLS socket server and client connections for ReactPHP", "keywords": ["Connection", "Socket", "async", "reactphp", "stream"], "support": {"issues": "https://github.com/reactphp/socket/issues", "source": "https://github.com/reactphp/socket/tree/v1.15.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-12-15T11:02:10+00:00"}, {"name": "react/stream", "version": "v1.4.0", "source": {"type": "git", "url": "https://github.com/reactphp/stream.git", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/stream/zipball/1e5b0acb8fe55143b5b426817155190eb6f5b18d", "reference": "1e5b0acb8fe55143b5b426817155190eb6f5b18d", "shasum": ""}, "require": {"evenement/evenement": "^3.0 || ^2.0 || ^1.0", "php": ">=5.3.8", "react/event-loop": "^1.2"}, "require-dev": {"clue/stream-filter": "~1.2", "phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"psr-4": {"React\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "Event-driven readable and writable streams for non-blocking I/O in ReactPHP", "keywords": ["event-driven", "io", "non-blocking", "pipe", "reactphp", "readable", "stream", "writable"], "support": {"issues": "https://github.com/reactphp/stream/issues", "source": "https://github.com/reactphp/stream/tree/v1.4.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2024-06-11T12:45:25+00:00"}, {"name": "sebastian/diff", "version": "6.0.1", "source": {"type": "git", "url": "https://github.com/sebastian<PERSON>mann/diff.git", "reference": "ab83243ecc233de5655b76f577711de9f842e712"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/sebas<PERSON><PERSON>mann/diff/zipball/ab83243ecc233de5655b76f577711de9f842e712", "reference": "ab83243ecc233de5655b76f577711de9f842e712", "shasum": ""}, "require": {"php": ">=8.2"}, "require-dev": {"phpunit/phpunit": "^11.0", "symfony/process": "^4.2 || ^5"}, "type": "library", "extra": {"branch-alias": {"dev-main": "6.0-dev"}}, "autoload": {"classmap": ["src/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "description": "Diff implementation", "homepage": "https://github.com/sebastian<PERSON>mann/diff", "keywords": ["diff", "udiff", "unidiff", "unified diff"], "support": {"issues": "https://github.com/sebastian<PERSON>mann/diff/issues", "security": "https://github.com/sebastian<PERSON>mann/diff/security/policy", "source": "https://github.com/sebastian<PERSON>mann/diff/tree/6.0.1"}, "funding": [{"url": "https://github.com/sebastian<PERSON>mann", "type": "github"}], "time": "2024-03-02T07:30:33+00:00"}, {"name": "squizlabs/php_codesniffer", "version": "3.10.1", "source": {"type": "git", "url": "https://github.com/PHPCSStandards/PHP_CodeSniffer.git", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/PHPCSStandards/PHP_CodeSniffer/zipball/8f90f7a53ce271935282967f53d0894f8f1ff877", "reference": "8f90f7a53ce271935282967f53d0894f8f1ff877", "shasum": ""}, "require": {"ext-simplexml": "*", "ext-tokenizer": "*", "ext-xmlwriter": "*", "php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^4.0 || ^5.0 || ^6.0 || ^7.0 || ^8.0 || ^9.3.4"}, "bin": ["bin/phpcbf", "bin/phpcs"], "type": "library", "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "notification-url": "https://packagist.org/downloads/", "license": ["BSD-3-<PERSON><PERSON>"], "authors": [{"name": "<PERSON>", "role": "Former lead"}, {"name": "<PERSON>", "role": "Current lead"}, {"name": "Contributors", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer/graphs/contributors"}], "description": "PHP_CodeSniffer tokenizes PHP, JavaScript and CSS files and detects violations of a defined set of coding standards.", "homepage": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "keywords": ["phpcs", "standards", "static analysis"], "support": {"issues": "https://github.com/PHPCSStandards/PHP_CodeSniffer/issues", "security": "https://github.com/PHPCSStandards/PHP_CodeSniffer/security/policy", "source": "https://github.com/PHPCSStandards/PHP_CodeSniffer", "wiki": "https://github.com/PHPCSStandards/PHP_CodeSniffer/wiki"}, "funding": [{"url": "https://github.com/PHPCSStandards", "type": "github"}, {"url": "https://github.com/jrfnl", "type": "github"}, {"url": "https://opencollective.com/php_codesniffer", "type": "open_collective"}], "time": "2024-05-22T21:24:41+00:00"}, {"name": "symfony/polyfill-php80", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php80.git", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php80/zipball/77fa7995ac1b21ab60769b7323d600a991a90433", "reference": "77fa7995ac1b21ab60769b7323d600a991a90433", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php80\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.0+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php80/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T15:07:36+00:00"}, {"name": "symfony/polyfill-php81", "version": "v1.30.0", "source": {"type": "git", "url": "https://github.com/symfony/polyfill-php81.git", "reference": "3fb075789fb91f9ad9af537c4012d523085bd5af"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/polyfill-php81/zipball/3fb075789fb91f9ad9af537c4012d523085bd5af", "reference": "3fb075789fb91f9ad9af537c4012d523085bd5af", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"thanks": {"name": "symfony/polyfill", "url": "https://github.com/symfony/polyfill"}}, "autoload": {"files": ["bootstrap.php"], "psr-4": {"Symfony\\Polyfill\\Php81\\": ""}, "classmap": ["Resources/stubs"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Symfony polyfill backporting some PHP 8.1+ features to lower PHP versions", "homepage": "https://symfony.com", "keywords": ["compatibility", "polyfill", "portable", "shim"], "support": {"source": "https://github.com/symfony/polyfill-php81/tree/v1.30.0"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-06-19T12:30:46+00:00"}, {"name": "symfony/stopwatch", "version": "v7.1.1", "source": {"type": "git", "url": "https://github.com/symfony/stopwatch.git", "reference": "5b75bb1ac2ba1b9d05c47fc4b3046a625377d23d"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/stopwatch/zipball/5b75bb1ac2ba1b9d05c47fc4b3046a625377d23d", "reference": "5b75bb1ac2ba1b9d05c47fc4b3046a625377d23d", "shasum": ""}, "require": {"php": ">=8.2", "symfony/service-contracts": "^2.5|^3"}, "type": "library", "autoload": {"psr-4": {"Symfony\\Component\\Stopwatch\\": ""}, "exclude-from-classmap": ["/Tests/"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "Provides a way to profile code", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/stopwatch/tree/v7.1.1"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2024-05-31T14:57:53+00:00"}], "aliases": [], "minimum-stability": "stable", "stability-flags": {"andersundsehr/aus-driver-amazon-s3": 20, "mogic/mogic-phpcs": 20, "mogic/tknucera": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {}, "platform-dev": {}, "platform-overrides": {"php": "8.2"}, "plugin-api-version": "2.6.0"}