/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./Resources/Private/**/*.{html,js}"],
  theme: {
    extend: {
      fontFamily: {
        tktype: ['"TKType"', 'Verdana, Tahoma, Arial, sans-serif'],
        tktypeMedium: ['"TKTypeMedium"', 'Verdana, Tahoma, Arial, sans-serif'],
        tktypeBold: ['"TKTypeBold"', 'Verdana, Tahoma, Arial, sans-serif'],
        orbitronBold: ['"Orbitron-Bold"', 'Verdana, Tahoma, Arial, sans-serif'],
      },
      colors: {
        // only styleguide relevant
        primaryPurpleOpacity: 'rgb(165,20,130,0.85)',

        // design relevant
        lightPurple: {
          DEFAULT: '#D699C2',
          50: '#EBCCE1',
          25: '#F5E5F0',
        },
        brightPurple: {
          DEFAULT: '#B44B91',
        },
        nuceraPurple: {
          DEFAULT: '#A51482',
        },
        midPurple: {
          DEFAULT: '#7F187D',
        },
        darkPurple: {
          DEFAULT: '#5D0D62',
        },
        ultraPurple: {
          DEFAULT: '#411646',
        },
        darkmetalGray: {
          DEFAULT: '#4B5564',
          150: '#2D3440',
          200: '#111B2C',
          250: '#262F3E'
        },
        urbanGray: {
          DEFAULT: '#636D82 ',
        },
        techGray: {
          DEFAULT: '#78879B',
        },
        industryGray: {
          DEFAULT: '#AAB8CC',
        },
        skyGray: {
          DEFAULT: '#D9DEE8',
        },
        hydroGray: {
          DEFAULT: '#EEF0F2',
          50: '#F7F7F9',
        },

        accentGreen: '#9BC832',
        accentBlueLight: '#8DD3D5',
        accentPurple: '#C4189A',
        pitchBlack: '#000000',
        oxygenWhite: '#ffffff',
        whiteOpacity12: 'rgba(255,255,255,0.12)',
        whiteOpacity20: 'rgba(255,255,255,0.2)',
        red: {
          DEFAULT: '#E02424',
          warning:  '#CC1010',
          5: '#FDF4F4',
          120: '#A20008',
          140: '#6F0005',
        },
        green: {
          DEFAULT: '#498B18',
          5: '#F3FAF7',
          100: '#0E9F6E'
        },
      },
      fontSize: {
        'big': '1.25rem', // 20px
        'big-mobile': '1.125rem', // 18px
        'normal': '1.125rem', // 18px
        'normal-mobile': '1.125rem', // 18px
        'small': '1rem', // 16px
        'small-mobile': '1rem', // 16px
        'extrasmall': '0.875rem', // 14px
        'extrasmall-mobile': '0.875rem', // 14px
        'h1': '4rem', // 64px
        'h1-mobile': '3rem', // 48px
        'h2': '3rem', // 48px
        'h2-mobile': '2.5rem', // 36px
        'h3': '2.25rem', // 36px
        'h3-mobile': '2rem', // 32px
        'subtitle': '1.5rem', // 24px
        'subtitle-mobile': '1.25rem', // 20px
      },
      lineHeight: {
        'big': '150%',
        'big-mobile': '150%',
        'normal': '150%',
        'normal-mobile': '150%',
        'small': '150%',
        'small-mobile': '150%',
        'extrasmall': '150%',
        'extrasmall-mobile': '150%',
        'h1': '120%',
        'h1-mobile': '120%',
        'h2': '130%',
        'h2-mobile': '130%',
        'h3': '130%',
        'h3-mobile': '130%',
        'subtitle': '140%',
        'subtitle-mobile': '140%',
        'highlighted': '130%',
      },
      aspectRatio: {
        '1x1': '1 / 1',
        '4x3': '4 / 3',
        '16x9': '16 / 9',
        '16x10': '16 / 10',
        '3x2': '3 / 2',
        '3x1': '3 / 1',
        '2x1': '2 / 1',
        '5x2': '5 / 2',
        'golden': '1 / 1.618',
        '2x3': '2 / 3',
        '3x4': '3 / 4',
      },
      padding: {
        '2.5': '10px',
        '1.5': '6px',
      },
      transitionProperty: {
        'height': 'max-height',
        'spacing': 'margin, padding',
      },
      containers: {
        'mobile': '45.9375rem', // 767px - 2rem (32px)
      },
    }
  },
  plugins: [],
}

