<?php
    use TYPO3\CMS\Core\DataHandling\PageDoktypeRegistry;
    use TYPO3\CMS\Core\Utility\GeneralUtility;

    if (!defined('TYPO3')) {
        die('Access denied.');
    }

    $articlePageDoktype = 116;

    $dokTypeRegistry = GeneralUtility::makeInstance(PageDoktypeRegistry::class);
    $dokTypeRegistry->add(
        $articlePageDoktype,
        [
            'allowedTables' => '*',
        ],
    );
?>
