<?php

//fluid content
\FluidTYPO3\Flux\Core::registerProviderExtensionKey('Mogic.tknucera', 'Page');
\FluidTYPO3\Flux\Core::registerProviderExtensionKey('Mogic.tknucera', 'Content');

\TYPO3\CMS\Core\Utility\ExtensionManagementUtility::addPageTSConfig('<INCLUDE_TYPOSCRIPT: source="FILE: EXT:tknucera/Configuration/TSconfig/Page/index.tsconfig">');

$GLOBALS['TYPO3_CONF_VARS']['BE']['stylesheets']['tknucera'] = 'EXT:tknucera/Resources/Public/Styles/backend.css';

// Register custom icons
$iconRegistry = \TYPO3\CMS\Core\Utility\GeneralUtility::makeInstance(\TYPO3\CMS\Core\Imaging\IconRegistry::class);
$iconRegistry->registerIcon(
    'tx-article-page',
    \TYPO3\CMS\Core\Imaging\IconProvider\SvgIconProvider::class,
    ['source' => 'EXT:tknucera/Resources/Public/Icons/Content/content-area.svg']
);

// Preset global registrieren
if (empty($GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['Default'])) {
    $GLOBALS['TYPO3_CONF_VARS']['RTE']['Presets']['Default'] = 'EXT:tknucera/Configuration/RTE/Default.yaml';
}

?>
