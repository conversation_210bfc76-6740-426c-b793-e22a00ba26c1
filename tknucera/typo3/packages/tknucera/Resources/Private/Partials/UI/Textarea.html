<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="textAreaData" value="{
    'name': name,
    'label': label,
  }" />

  <f:variable name="textAreaDefaultClasses" value="w-full flex justify-start items-start px-4 pt-[11px] pb-[10px]" />
  <f:variable name="textAreaClasses" value="{textAreaDefaultClasses} border border-industryGray" />
  <f:variable name="textAreaSuccessClasses" value="group-has-[.success]:border-green-100 group-has-[.success]:bg-green-5" />
  <f:variable name="textAreaErrorClasses" value="group-has-[.error]:border-red-warning group-has-[.error]:bg-red-5" />
  <f:variable name="textAreaHelperClasses" value="group-hover:text-darkmetalGray-250 group-has-[.success]:text-green-100 group-has-[.error]:text-red-warning" />
  <f:variable name="textAreaToplineClasses" value="group-hover:text-darkmetalGray-250" />

  <f:if condition="{type} == 'ghost'">
    <f:then>
      <f:variable name="textAreaClasses" value="
        {textAreaDefaultClasses} bg-darkmetalGray-200 text-hydroGray placeholder:text-industryGray
        hover:bg-nuceraPurple hover:placeholder-shown:placeholder:text-hydroGray focus:bg-darkmetalGray-200
        focus:hover:bg-darkmetalGray-200 focus:hover:placeholder-transparent" />
        <f:variable name="textAreaSuccessClasses" value="group-has-[.success]:border group-has-[.success]:border-green-100" />
        <f:variable name="textAreaErrorClasses" value="group-has-[.error]:border group-has-[.error]:border-red-warning" />
        <f:variable name="textAreaHelperClasses" value="" />
        <f:variable name="textAreaToplineClasses" value="" />
    </f:then>
  </f:if>
  <f:if condition="{type} == 'outline'">
    <f:then>
      <f:variable name="textAreaClasses" value="
        {textAreaDefaultClasses} bg-transparent border border-hydroGray-50 focus:border-hydroGray
        bg-darkmetalGray-200 text-hydroGray placeholder:text-industryGray hover:ring-hydroGray
        hover:ring-2 hover:placeholder:text-hydroGray focus:hover:placeholder-transparent" />
        <f:variable name="textAreaSuccessClasses" value="group-has-[.success]:border-green-100 group-has-[.success]:ring-green-100" />
        <f:variable name="textAreaErrorClasses" value="group-has-[.error]:border-red-warning group-has-[.error]:ring-red-warning" />
        <f:variable name="textAreaHelperClasses" value="" />
        <f:variable name="textAreaToplineClasses" value="" />
    </f:then>
  </f:if>

  <f:if condition="{success}">
    <f:then>
      <f:variable name="textAreaClasses" value="{textAreaClasses} success" />
    </f:then>
  </f:if>
  <f:if condition="{error}">
    <f:then>
      <f:variable name="textAreaClasses" value="{textAreaClasses} error" />
    </f:then>
  </f:if>

  <label x-data="" x-id="['textArea']" :for="$id('textArea')" class="group text-small inline-flex flex-col gap-1">
    <span x-id="['textAreaLabel']" class="sr-only">{textAreaData.label}</span>
    <span class="{textAreaToplineClasses}">Topline</span>
    <textarea
      :id="$id('textArea')"
      class="{textAreaClasses} {textAreaSuccessClasses} {textAreaErrorClasses}"
      rows="5"
      :aria-labelledby="$id('textAreaLabel')"
      placeholder="Lorem Ipsum">
    </textarea>
    <p class="text-extrasmall {textAreaHelperClasses}">
        Form Helper
    </p>
  </label>

</f:section>

</html>
