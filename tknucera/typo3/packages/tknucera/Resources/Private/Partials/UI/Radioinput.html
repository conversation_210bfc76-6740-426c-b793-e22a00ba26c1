<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="radioInputData" value="{
    'name': name,
    'label': label,
    'value': value,
    'checked': checked,
    'disabled': disabled
  }" />


  <f:variable name="radioInputOuterClasses" value="
    absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-0 w-8 h-8
    rounded-full opacity-0 bg-hydroGray group-hover:opacity-100
    group-has-[:checked]:bg-lightPurple pointer-events-none transition-opacity duration-200"
  />
  <f:variable name="radioCustomInputClasses" value="
    absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
    z-10 w-5 h-5 rounded-full border border-techGray flex items-center justify-center
    group-has-[:checked]:border-nuceraPurple
    group-has-[:disabled]:opacity-50
    peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0
    peer-focus:outline-2 peer-focus:outline-accentGreen"
  />
  <f:variable name="radioInnerClasses" value="w-2 h-2 rounded-full bg-nuceraPurple opacity-0 group-has-[:checked]:opacity-100 transition-opacity duration-200" />

  <f:if condition="{dark}">
    <f:variable name="radioInputOuterClasses" value="
      absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-0 w-8 h-8
      rounded-full opacity-0 bg-hydroGray group-hover:opacity-100
      group-has-[:checked]:bg-lightPurple pointer-events-none transition-opacity duration-200"
    />
    <f:variable name="radioCustomInputClasses" value="
      absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
      z-10 w-5 h-5 rounded-full border border-techGray flex items-center justify-center
      group-has-[:checked]:border-nuceraPurple
      group-has-[:disabled]:opacity-50
      peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0
      peer-focus:outline-2 peer-focus:outline-accentGreen"
    />
  </f:if>

  <div
    class="inline-flex items-center"
    x-data="(radio({radioInputData -> v:format.json.encode()}))"
    x-id="['radioInput']"
  >
    <label class="relative cursor-pointer group inline-block w-8 h-8">
      <span class="sr-only">{radioInputData.label}</span>
      <input
        type="radio"
        name="{radioInputData.name}"
        value="{radioInputData.value}"
        :checked="checked"
        :disabled="disabled"
        :id="$id('radioInput')"
        class="absolute w-0 h-0 opacity-0 peer"
      />
      <span class="{radioInputOuterClasses}"></span>
      <span class="{radioCustomInputClasses}">
        <span class="{radioInnerClasses}"></span>
      </span>
    </label>
  </div>

</f:section>

</html>
