<div?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:tknucera="http://typo3.org/ns/Mogic/tknucera/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="tableData" value="{
    headings: headings,
    rows: rows,
    label: label
  }" />

  <table tabindex='1' class="hidden lg:table w-full border-collapse text-left" aria-label="{tableData.label}" role="table">
    <thead class="border-b-2 border-nuceraPurple">
      <tr>
        <f:for each="{tableData.headings}" as="heading">
          <th scope="col" class="text-nuceraPurple py-6 px-4 snap-start" style="width: calc(100vw - 1.5rem);">{heading}</th>
        </f:for>
      </tr>
    </thead>
    <tbody>
      <f:for each="{tableData.rows}" as="row" iteration="rowIterator">
        <f:variable name="trClass" value="" />
        <f:if condition="{rowIterator.index} % 2 == 0">
          <f:variable name="trClass" value="bg-hydroGray-50" />
        </f:if>
        <tr class="{trClass}">
          <f:for each="{row}" as="value" key="label">
            <td class="py-6 px-4 " style="width: calc(100vw - 1.5rem);">{value}</td>
          </f:for>
        </tr>
      </f:for>
    </tbody>
  </table>

  <div class="lg:hidden w-full space-y-4">
    <f:for each="{tableData.rows}" as="row" iteration="rowIterator">
      <table class="w-full shadow-md" aria-label="{label}">
        <tbody>
          <f:for each="{row}" as="value" key="label" iteration="cellIterator">
            <f:variable name="trClass" value="" />
            <f:if condition="{cellIterator.index} % 2 == 0">
              <f:variable name="trClass" value="bg-hydroGray-50" />
            </f:if>

            <f:variable name="firstHeadingWrapper" value="" />
            <f:variable name="firstHeading" value="" />
            <f:variable name="firstRowBorder" value="" />
            <f:variable name="rowHeadingClass" value="text-small" />

            <f:if condition="{cellIterator.index} == 0">
              <f:variable name="firstHeading" value="text-nuceraPurple subtitle font-bold" />
              <f:variable name="firstHeadingWrapper" value="text-lg text-nuceraPurple" />
              <f:variable name="firstRowBorder" value="border-b-2 border-nuceraPurple" />
              <f:variable name="rowHeadingClass" value="text-big" />
            </f:if>

            <tr class="border-b border-gray-100 last:border-b-0 {trClass}">
              <td class="py-3 px-4 {firstRowBorder}">
                <div class="flex flex-col gap-2 {firstHeadingWrapper}">
                  <span class="{firstHeading}">{value}</span>
                  <span class="{rowHeadingClass}">{tableData.headings.{cellIterator.index}}</span>
                </div>
              </td>
            </tr>
          </f:for>
        </tbody>
      </table>
    </f:for>
  </div>
</f:section>
</html>
