<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
      xmlns:tknucera="Mogic\tknucera\ViewHelpers"
>

<f:section name="p">
  <f:if condition="{content}">
    <f:if condition="{richtext}">
      <f:then>
        <div class="{css}"><f:format.htmlentitiesDecode><tknucera:renderMarkup text="{content -> f:format.html()}" /></f:format.htmlentitiesDecode></div>
      </f:then>
      <f:else>
        <p class="{css}">{content -> f:format.nl2br()}</p>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="p-big">
  <f:if condition="{content}">
    <f:if condition="{richtext}">
      <f:then>
        <div class="big {css}"><f:format.htmlentitiesDecode><tknucera:renderMarkup text="{content -> f:format.html()}" /></f:format.htmlentitiesDecode></div>
      </f:then>
      <f:else>
        <p class="big {css}">{content -> f:format.nl2br()}</p>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="p-small">
  <f:if condition="{content}">
    <f:if condition="{richtext}">
      <f:then>
        <div class="small {css}"><f:format.htmlentitiesDecode><tknucera:renderMarkup text="{content -> f:format.html()}" /></f:format.htmlentitiesDecode></div>
      </f:then>
      <f:else>
        <p class="small {css}">{content -> f:format.nl2br()}</p>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="p-extrasmall">
  <f:if condition="{content}">
    <f:if condition="{richtext}">
      <f:then>
        <div class="extrasmall {css}"><f:format.htmlentitiesDecode><tknucera:renderMarkup text="{content -> f:format.html()}" /></f:format.htmlentitiesDecode></div>
      </f:then>
      <f:else>
        <p class="extrasmall {css}">{content -> f:format.nl2br()}</p>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:comment>
  <div class="text-center"></div>
</f:comment>

</html>
