<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
      xmlns:tknucera="Mogic\Cvag\ViewHelpers"
>

<!-- infobox -->
<f:section name="Main">
  <f:switch expression="{settings.backgroundColor}">
    <f:case value="light"><f:variable name="backgroundClasses" value="bg-primaryBlue-10 text-primaryBlue" /></f:case>
    <f:case value="medium"><f:variable name="backgroundClasses" value="bg-primaryBlue-10 text-primaryBlue" /></f:case>
    <f:case value="dark"><f:variable name="backgroundClasses" value="bg-darkmetalGray-150 text-oxygenWhite" /></f:case>
    <f:defaultCase><f:variable name="backgroundClasses" value="" /></f:defaultCase>
  </f:switch>

  <f:switch expression="{settings.padding}">
    <f:case value="small"><f:variable name="paddingClasses" value="p-4" /></f:case>
    <f:case value="medium"><f:variable name="paddingClasses" value="p-10" /></f:case>
    <f:case value="large"><f:variable name="paddingClasses" value="p-20" /></f:case>
    <f:defaultCase><f:variable name="paddingClasses" value="" /></f:defaultCase>
  </f:switch>

  <f:switch expression="{settings.margin}">
    <f:case value="small"><f:variable name="marginClasses" value="m-4" /></f:case>
    <f:case value="medium"><f:variable name="marginClasses" value="m-10" /></f:case>
    <f:case value="large"><f:variable name="marginClasses" value="m-20" /></f:case>
    <f:defaultCase><f:variable name="marginClasses" value="" /></f:defaultCase>
  </f:switch>

  <f:if condition="{cUid}!==''}">
    <f:then>
      <div id="{cUid}" class="corporate_grid {backgroundClasses} {paddingClasses} {marginClasses}">
        <f:render section="InnerContent" arguments="{_all}" />
      </div>
    </f:then>
    <f:else>
      <div class="{backgroundClasses} {paddingClasses} {marginClasses}">
        <f:render section="InnerContent" arguments="{_all}" />
      </div>
    </f:else>
  </f:if>
</f:section>

<f:section name="InnerContent">
  {content -> f:format.raw()}
</f:section>

</html>
