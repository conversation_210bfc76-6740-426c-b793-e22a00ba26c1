<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<!-- H1 -->
<f:section name="h1">
  <f:if condition="{content}">
    <h1 class="h1">{content -> f:format.nl2br()}</h1>
  </f:if>
</f:section>

<f:section name="h1-purple-bg">
  <f:if condition="{content}">
    <h1 class="h1 highlightPositive highlightHeadline">{content -> f:format.nl2br()}</h1>
  </f:if>
</f:section>

<f:section name="h1-white-bg">
  <f:if condition="{content}">
    <h1 class="h1 highlightNegative highlightHeadline">{content -> f:format.nl2br()}</h1>
  </f:if>
</f:section>

<!-- H2 -->
<f:section name="h2">
  <f:if condition="{content}">
    <h2 class="h2">{content -> f:format.nl2br()}</h2>
  </f:if>
</f:section>

<f:section name="h2-purple-bg">
  <f:if condition="{content}">
    <h2 class="h2 highlightPositive highlightHeadline">{content -> f:format.nl2br()}</h2>
  </f:if>
</f:section>

<f:section name="h2-white-bg">
  <f:if condition="{content}">
    <h2 class="h2 highlightNegative highlightHeadline">{content -> f:format.nl2br()}</h2>
  </f:if>
</f:section>

<!-- H3 -->
<f:section name="h3">
  <f:if condition="{content}">
    <h3 class="h3">{content -> f:format.nl2br()}</h3>
  </f:if>
</f:section>

<!-- Subtitle -->
<f:section name="subtitle">
  <f:if condition="{content}">
    <h4 class="subtitle">{content -> f:format.nl2br()}</h4>
  </f:if>
</f:section>

<f:section name="subtitle-bold">
  <f:if condition="{content}">
    <h4 class="subtitle font-tktypeBold">{content -> f:format.nl2br()}</h4>
  </f:if>
</f:section>

<f:section name="subtitle-black">
  <f:if condition="{content}">
    <h4 class="subtitle font-corporateBlack">{content -> f:format.nl2br()}</h4>
  </f:if>
</f:section>

</html>
