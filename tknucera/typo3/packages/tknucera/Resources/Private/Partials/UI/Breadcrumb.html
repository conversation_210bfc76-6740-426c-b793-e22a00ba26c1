<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">
  <v:page.breadCrumb entryLevel="1" as="menu">
    <div class="flex flex-row gap-2 items-center flex-wrap">
      <f:link.page pageUid="{v:variable.typoscript(path: 'tknucera.pids.root_page')}" class="hover:underline">
        tknucera.de
      </f:link.page>
      <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-right', class: 'w-[20px] h-[20px]', additionalAttributes: {'aria-hidden': 'true'} }" />
      <f:for each="{menu}" as="menuitem" iteration="iter">
        <f:if condition="{iter.isLast}">
          <f:then>
           {menuitem.linktext}
          </f:then>
          <f:else>
            <f:link.page pageUid="{menuitem.uid}" class="hover:underline">
              {menuitem.linktext}
            </f:link.page>
            <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-right', class: 'w-[20px] h-[20px]', additionalAttributes: {'aria-hidden': 'true'} }" />
          </f:else>
        </f:if>
      </f:for>
    </div>

  </v:page.breadCrumb>
</f:section>

</html>
