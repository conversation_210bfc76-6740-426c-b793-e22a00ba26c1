<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Image">
  <f:if condition="{image}">
    <f:if condition="{image.link}">
      <f:then>
        <f:link.typolink parameter="{image.link}">
          <f:image src="{image.id}"
                   class="{customClasses}"
                   treatIdAsReference="1"
                   alt="{image.alternative}"
                   width="{width}c"
                   height="{height}c"
                   loading="lazy"
                   fileExtension="webp"/>
        </f:link.typolink>
      </f:then>
      <f:else>
        <f:image src="{image.id}"
                 class="{customClasses}"
                 treatIdAsReference="1"
                 alt="{image.alternative}"
                 width="{width}c"
                 height="{height}c"
                 loading="lazy"
                 fileExtension="webp"/>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="ImageFocuspoint">
  <f:if condition="{image}">
    <f:if condition="{roundedCorners}">
      <f:then>
        <f:variable name="roundedCornerClass" value=" rounded-lg overflow-hidden" />
      </f:then>
      <f:else>
        <f:variable name="roundedCornerClass" value="" />
      </f:else>
    </f:if>
    <f:if condition="{imageratio}!=''">
      <f:then>
        <f:if condition="{imageratio}!='autoHeight'">
          <f:then>
            <v:variable.set name="focuspointClass" value="vf-container w-full relative aspect-{imageratio} overflow-hidden" />
          </f:then>
          <f:else>
            <v:variable.set name="focuspointClass" value="vf-container w-full relative h-full overflow-hidden" />
          </f:else>
        </f:if>
      </f:then>
      <f:else>
        <v:variable.set name="focuspointClass" value="w-full" />
      </f:else>
    </f:if>
    <f:if condition="{focuspoint} && {focuspoint}=='center-top'">
      <f:then>
        <f:variable name="focusPointX" value="1" />
        <f:variable name="focusPointY" value="1" />
      </f:then>
      <f:else>
        <f:variable name="focusPointX" value="{image.focus_point_x/100}" />
        <f:variable name="focusPointY" value="{image.focus_point_y/100}" />
      </f:else>
    </f:if>
    <f:if condition="{image.link}">
      <f:then>
        <f:link.typolink parameter="{image.link}">
          <div class="{focuspointClass}{roundedCornerClass}"
               data-focus-x="{focusPointX}"
               data-focus-y="{focusPointY}"
               data-image-w="{image.width}"
               data-image-h="{image.height}">
            <f:image src="{image.id}"
                     treatIdAsReference="1"
                     alt="{image.alternative}"
                     title="{image.title}"
                     width="{width}c"
                     loading="lazy"
                     fileExtension="webp"/>
          </div>
        </f:link.typolink>
      </f:then>
      <f:else>
        <div class="{focuspointClass}{roundedCornerClass}"
             data-focus-x="{focusPointX}"
             data-focus-y="{focusPointY}"
             data-image-w="{image.width}"
             data-image-h="{image.height}">
          <f:image src="{image.id}"
                   treatIdAsReference="1"
                   alt="{image.alternative}"
                   title="{image.title}"
                   width="{width}c"
                   loading="lazy"
                   fileExtension="webp"/>
        </div>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="ImageFocuspointPredefined">
  <f:if condition="{image}">
    <f:if condition="{image.link}">
      <f:then>
        <f:link.typolink parameter="{image.link}">
          <div class="vf-container relative aspect-4x3 md:aspect-2x3 rounded-lg overflow-hidden"
               data-focus-x="{image.focus_point_x/100}"
               data-focus-y="{image.focus_point_y/100}"
               data-image-w="{image.width}"
               data-image-h="{image.height}">
            <f:image src="{image.id}"
                     treatIdAsReference="1"
                     alt="{image.alternative}"
                     title="{image.title}"
                     width="{width}c"
                     loading="lazy"
                     fileExtension="webp"/>
          </div>
        </f:link.typolink>
      </f:then>
      <f:else>
        <div class="vf-container relative aspect-4x3 md:aspect-2x3 rounded-lg overflow-hidden"
             data-focus-x="{image.focus_point_x/100}"
             data-focus-y="{image.focus_point_y/100}"
             data-image-w="{image.width}"
             data-image-h="{image.height}">
          <f:image src="{image.id}"
                   treatIdAsReference="1"
                   alt="{image.alternative}"
                   title="{image.title}"
                   width="{width}c"
                   loading="lazy"
                   fileExtension="webp"/>
        </div>
      </f:else>
    </f:if>
  </f:if>
</f:section>

<f:section name="ImageFocuspointHeroImage">
  <f:if condition="{image}">
    <f:variable name="width" value="1600" />
    <f:variable name="height" value="1000" />
    <f:if condition="{imageWidth}">
      <f:variable name="width" value="{imageWidth}" />
    </f:if>
    <f:if condition="{imageHeight}">
      <f:variable name="height" value="{imageHeight}" />
    </f:if>
    <f:if condition="{image.link}">
      <f:then>
        <f:link.typolink parameter="{image.link}">
          <div class="vf-container relative h-[375px] md:h-[600px] max-w-[2560px] mx-auto overflow-hidden"
               data-focus-x="{image.focus_point_x/100}"
               data-focus-y="{image.focus_point_y/100}"
               data-image-w="{width}"
               data-image-h="{height}">
            <f:image src="{image.id}"
                     treatIdAsReference="1"
                     alt="{image.alternative}"
                     title="{image.title}"
                     width="{width}c"
                     height="{height}c"
                     loading="eager"
                     fileExtension="webp"/>
          </div>
        </f:link.typolink>
      </f:then>
      <f:else>
        <div class="vf-container relative h-[375px] md:h-[600px] max-w-[2560px] mx-auto overflow-hidden"
             data-focus-x="{image.focus_point_x/100}"
             data-focus-y="{image.focus_point_y/100}"
             data-image-w="{width}"
             data-image-h="{height}">
          <f:image src="{image}"
                   treatIdAsReference="1"
                   alt=""
                   title=""
                   width="{width}c"
                   height="{height}c"
                   loading="eager"
                   fileExtension="webp"/>
        </div>
      </f:else>
    </f:if>
  </f:if>
</f:section>

</html>
