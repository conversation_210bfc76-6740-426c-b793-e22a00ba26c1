<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <f:variable name="tabsData" value="{
    label: label,
    tabs: tabs
  }" />

  <div x-data="tabs({tabsData -> v:format.json.encode()})" class="w-full">
    <div class="w-full relative">
      <button
        x-ref="prevBtn"
        @click="scrollTabs(-1)"
        :disabled="isPrevDisabled"
        class="md:hidden absolute left-0 top-1/2 -translate-y-1/2 text-white bg-darkmetalGray border-b-2 border-skyGray h-full px-1 md:px-4 py-2.5 z-20 disabled:opacity-50"
      >
        <f:render
          partial="UI/Icon"
          section="Main"
          arguments="{
            name: 'chevron-left',
            class: 'w-5 h-5',
            additionalAttributes: {'aria-hidden': 'true'}
          }"
        />
      </button>
      <button
        x-ref="nextBtn"
        @click="scrollTabs(1)"
        :disabled="isNextDisabled()"
        class="md:hidden absolute right-0 top-1/2 -translate-y-1/2 text-white bg-darkmetalGray border-b-2 border-skyGray h-full px-1 md:px-4 py-2.5 z-20 disabled:opacity-50"
      >
        <f:render
          partial="UI/Icon"
          section="Main"
          arguments="{
            name: 'chevron-right',
            class: 'w-5 h-5',
            additionalAttributes: {'aria-hidden': 'true'}
          }"
        />
      </button>

      <div x-ref="tabWrapper" class="overflow-hidden w-full md:overflow-visible">
        <div
          x-ref="tabList"
          role="tablist"
          :aria-label="label"
          class="flex flex-row transition-transform duration-300 ease-in-out md:transition-none"
        >

          <template x-for="(tab, index) in tabs" :key="index">
            <button
              role="tab"
              :aria-selected="isTabActive(index)"
              :aria-controls="`tabpanel-${index}`"
              :id="`tab-${index}`"
              @click="activateTab(index)"
              class="px-0.5 md:px-4 py-2.5 text-small font-TKTypeMedium flex justify-center
              bg-darkmetalGray border-b-2 hover:border-white whitespace-nowrap
                min-w-0 flex-1 md:flex-initial"
              :class="activeTab === index ? 'text-white border-white bg-urbanGray' : 'text-skyGray border-skyGray'"
            >
            <span x-text="tab.title" class="truncate"></span>
          </button>
          </template>
        </div>
      </div>
    </div>

    <div class="bg-darkmetalGray p-4">
      <template x-for="(tab, index) in tabs" :key="index">
        <div
          role="tabpanel"
          :id="`tabpanel-${index}`"
          :aria-labelledby="`tab-${index}`"
          tabindex="0"
          class="focus:outline-none text-white"
          :class="activeTab === index ? '' : 'hidden'"
          x-text="tab.content"
        ></div>
      </template>
    </div>
  </div>

</f:section>
</html>
