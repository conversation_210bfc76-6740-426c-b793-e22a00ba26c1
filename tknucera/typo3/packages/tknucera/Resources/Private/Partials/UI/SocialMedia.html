<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">
  <ul class="flex flex-row flex-wrap justify-start align-center gap-3">
    <li>
      <f:link.external
        uri="{v:variable.typoscript(path: 'tknucera.social_media_links.facebook')}"
        title="Facebook"
        class="text-normal md:text-small hover:underline underline-offset-2"
        additionalAttributes="{target: '_blank', 'rel': 'noopener noreferrer', 'aria-label': 'Facebook'}"
      >
        <f:render partial="UI/Icon" section="Main" arguments="{name: 'facebook', class: 'h-6 w-6', additionalAttributes: {'aria-hidden': 'true'} }"/>
      </f:link.external>
    </li>
    <li>
      <f:link.external
        uri="{v:variable.typoscript(path: 'tknucera.social_media_links.linkedin')}"
        title="LinkedIn"
        class="text-normal md:text-small hover:underline underline-offset-2"
        additionalAttributes="{target: '_blank', 'rel': 'noopener noreferrer', 'aria-label': 'LinkedIn'}"
      >
        <f:render partial="UI/Icon" section="Main" arguments="{name: 'linkedin', class: 'h-6 w-6', additionalAttributes: {'aria-hidden': 'true'} }"/>
      </f:link.external>
    </li>
    <li>
      <f:link.external
        uri="{v:variable.typoscript(path: 'tknucera.social_media_links.twitter')}"
        title="twitter"
        class="text-normal md:text-small hover:underline underline-offset-2"
        additionalAttributes="{target: '_blank', 'rel': 'noopener noreferrer', 'aria-label': 'twitter'}"
      >
        <f:render partial="UI/Icon" section="Main" arguments="{name: 'twitter', class: 'h-6 w-6 pt-0.5', additionalAttributes: {'aria-hidden': 'true'} }"/>
      </f:link.external>
    </li>
    <li>
      <f:link.external
        uri="{v:variable.typoscript(path: 'tknucera.social_media_links.youtube')}"
        title="Youtube"
        class="text-normal md:text-small hover:underline underline-offset-2"
        additionalAttributes="{target: '_blank', 'rel': 'noopener noreferrer', 'aria-label': 'Youtube'}"
      >
        <f:render partial="UI/Icon" section="Main" arguments="{name: 'youtube', class: 'h-6 w-6', additionalAttributes: {'aria-hidden': 'true'} }"/>
      </f:link.external>
    </li>
    <li>
      <f:link.external
        uri="{v:variable.typoscript(path: 'tknucera.social_media_links.instagram')}"
        title="Instagram"
        class="text-normal md:text-small hover:underline underline-offset-2"
        additionalAttributes="{target: '_blank', 'rel': 'noopener noreferrer', 'aria-label': 'Instagram'}"
      >
        <f:render partial="UI/Icon" section="Main" arguments="{name: 'instagram', class: 'h-6 w-6', additionalAttributes: {'aria-hidden': 'true'} }" />
      </f:link.external>
    </li>
  </ul>
</f:section>

</html>
