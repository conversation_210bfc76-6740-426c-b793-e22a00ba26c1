<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Tag">

  <f:variable name="tagData" value="{
    'label': label,
  }" />

  <span class="inline-flex items-center bg-nuceraPurple text-small hover:bg-midPurple text-hydroGray text-TKTypeBold font-bold line-height-normal px-2 py-1 uppercase">
    # {tagData.label}
  </span>

</f:section>

<f:section name="ContentHint">

  <f:variable name="contentHintData" value="{
    'label': label,
    'icon': icon
  }" />

  <f:variable name="tagClasses" value="inline-flex items-center px-3 text-small py-2 bg-darkmetalGray-250 text-white uppercase rounded-sm" />

  <f:if condition="{contentHintData.icon}">
    <f:variable name="tagClasses" value="inline-block p-1 bg-darkmetalGray-250 text-white rounded-sm" />
  </f:if>

  <span class="{tagClasses}">
    <f:if condition="{contentHintData.icon}">
      <f:render partial="UI/Icon" section="Main" arguments="{
        name: '{contentHintData.icon}',
        class: 'inline-block w-8 h-8'
      }" />
    </f:if>
    {contentHintData.label}
  </span>

</f:section>
</html>
