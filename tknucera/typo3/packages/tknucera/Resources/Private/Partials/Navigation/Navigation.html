<button?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Desktop">
  <f:variable name="level1Uids" value="" />
  <f:variable name="subNavClasses" value="relative flex w-full flex-col py-8 border-r border-urbanGray [&>li]:flex [&>li>a]:flex [&>li>a]:flex-row [&>li>a]:justify-between items-center [&>li>a]:w-full [&>li>a]:px-4 [&>li>a]:py-3" />

  <nav id="mainNavigation" role="navigation" class="w-full sticky top-0 lg:top-[44px] z-50 py-4 bg-darkmetalGray-250 text-hydroGray">
    <div class="px-[3rem] lg:corporate_grid gap-0">
      <div class="flex w-full items-center justify-end col-start-3 col-end-5 lg:hidden">
        <button type="button" onclick="tknuceraNavigation.openMobileNavigation(this)" class="w-8 h-8 showMobileNav">
          <f:render partial="UI/Icon" section="Main" arguments="{name: 'solid-menu', class: 'w-8 h-8', additionalAttributes: {'aria-hidden': 'true'} }" />
          <span class="sr-only">Mobile Navigation öffnen</span>
        </button>
        <button onclick="tknuceraNavigation.closeMobileNavigation(this)" class="w-8 h-8 hideMobileNav hidden">
          <span class="sr-only">Mobile Navigation schließen</span>
          <f:render partial="UI/Icon" section="Main" arguments="{name: 'close', class: 'w-8 h-8', additionalAttributes: {'aria-hidden': 'true'} }" />
        </button>
      </div>
      <div class="w-full items-center col-start-1 md:col-start-3 lg:col-start-3 col-end-5 md:col-end-7 lg:col-end-9 lg:gap-2 xl:gap-3 hidden lg:flex">
        <v:menu expandAll="1"
                pageUid="{v:variable.typoscript(path: 'tknucera.pids.root_page')}"
                entryLevel="0"
                levels="1"
                as="level1"
                excludePages=""
                classActive="border-b rounded-none hover:rounded border-nuceraPurple"
                useShortcutData="false"
                useShortcutUid="false"
        >
          <f:for each="{level1}" as="level1_Item" iteration="iterator">
            <f:if condition="{iterator.isFirst}">
              <f:then>
                <f:variable name="level1Uids" value="{level1_Item.uid}" />
              </f:then>
              <f:else>
                <f:variable name="level1Uids" value="{level1Uids},{level1_Item.uid}" />
              </f:else>
            </f:if>
            <f:if condition="{f:link.page(pageUid: level1_Item.uid)}">
              <f:then>
                <f:if condition="{level1_Item.hasSubPages}">
                  <f:then>
                    <div class="">
                      <button type="button"
                                    class="{level1_Item.class} showFlyout"
                                    onclick="tknuceraNavigation.showFlyout('{level1_Item.uid}', this);return false;"
                                    aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-controls="flyout-id-{level1_Item.uid}">
                        <span class="block w-full h-full pt-[5px] pb-1 px-2.5 rounded focus:bg-nuceraPurple hover:bg-nuceraPurple showFlyout">
                          {level1_Item.title}
                        </span>
                      </button>
                      <div id="navigationFlyouts" class="hidden lg:block text-hydroGray">
                        <f:for each="{v:iterator.explode(content: '{level1Uids}', glue: ',')}" as="flyoutId" iteration="iterator">
                          <div id="flyout-id-{flyoutId}" class="hidden navigationFlyout bg-darkmetalGray-250  absolute left-0 top-full w-full z-40 drop-shadow-xl">
                            <div class="corporate_grid gap-0 relative">
                              <div class="flex w-full items-start col-start-1 md:col-start-2 lg:col-start-3 col-end-5 md:col-end-5 lg:col-end-6">
                                <v:menu expandAll="1"
                                        pageUid="{flyoutId}"
                                        entryLevel="0"
                                        levels="2"
                                        as="level2"
                                        lang="{page.sys_language_uid}"
                                        excludePages=""
                                        classActive=""
                                        useShortcutData="false"
                                        useShortcutUid="false">
                                  <ul class="{subNavClasses} h-full subNavlist" role="menu">
                                    <li class="w-full" role="menuitem">
                                      <f:link.page
                                      pageUid="{flyoutId}"
                                      class="hover:bg-nuceraPurple {f:if(condition: '{flyoutId}=={page.uid}', then: 'font-tktypeBold', else: '')}">
                                        <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.overview'}" />: {v:page.info(pageUid: flyoutId, field: 'title')}</f:link.page>
                                    </li>
                                    <f:for each="{level2}" as="level2_Item" iteration="iterator">
                                      <f:if condition="{f:link.page(pageUid: level2_Item.uid)}">
                                        <li class="w-full" role="menuitem">
                                          <f:if condition="{level2_Item.hasSubPages}">
                                            <f:then>
                                              <button type="button"
                                                           class="{level2_Item.class} px-4 py-3 w-full flex items-center justify-between hover:bg-nuceraPurple showSubnav"
                                                            aria-haspopup="true"
                                                            aria-expanded="false"
                                                            aria-controls="flyout-id-{level2_Item.uid}"
                                                            onclick="tknuceraNavigation.showSubnav('{level2_Item.uid}', this);return false;">
                                                {level2_Item.title}
                                                <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-right', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
                                              </button>
                                              <div id="subnav-id-{level2_Item.uid}" class="navigationSubnav absolute left-full top-0 w-full h-full hidden">
                                                <v:menu expandAll="1"
                                                        pageUid="{level2_Item.uid}"
                                                        entryLevel="0"
                                                        levels="2"
                                                        as="level3"
                                                        lang="{page.sys_language_uid}"
                                                        excludePages=""
                                                        classActive="font-bold"
                                                        useShortcutData="false"
                                                        useShortcutUid="false">
                                                  <ul class="{subNavClasses}" id="flyout-id-{level2_Item.uid}" role="menu">
                                                    <li class="w-full" role="menuitem">
                                                      <f:link.page
                                                      pageUid="{level2_Item.uid}"
                                                      class="ml-0.5 hover:bg-nuceraPurple {f:if(condition: '{level2_Item.uid}=={page.uid}', then: 'font-tktypeBold', else: '')}"
                                                    >
                                                      <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.overview'}" />: {v:page.info(pageUid: level2_Item.uid, field: 'title')}</f:link.page>
                                                    </li>
                                                    <f:for each="{level3}" as="level3_Item" iteration="iterator">
                                                      <f:if condition="{f:link.page(pageUid: level3_Item.uid)}">
                                                        <li class="w-full" role="menuitem">
                                                          <f:link.page pageUid="{level3_Item.uid}"
                                                                       title="{level3_Item.title}"
                                                                       class="{level3_Item.class} ml-0.5 hover:bg-nuceraPurple"
                                                                       onclick="tknuceraNavigation.hideFlyouts();">
                                                            {level3_Item.title}
                                                          </f:link.page>
                                                        </li>
                                                      </f:if>
                                                    </f:for>
                                                  </ul>
                                                </v:menu>
                                              </div>
                                            </f:then>
                                            <f:else>
                                              <f:link.page pageUid="{level2_Item.uid}"
                                                           title="{level2_Item.title}"
                                                           class="{level2_Item.class} ml-0.5 hover:bg-nuceraPurple"
                                                           onclick="tknuceraNavigation.hideFlyouts();">
                                                {level2_Item.title}
                                              </f:link.page>
                                            </f:else>
                                          </f:if>
                                        </li>
                                      </f:if>
                                    </f:for>
                                  </ul>
                                </v:menu>
                              </div>
                            </div>
                          </div>
                        </f:for>
                      </div>
                    </div>
                  </f:then>
                  <f:else>
                    <f:link.page pageUid="{level1_Item.uid}"
                                  title="{level1_Item.title}"
                                  class="{level1_Item.class} showFlyout">
                        <span class="block w-full h-full pt-[5px] pb-1 px-2.5 rounded focus:bg-nuceraPurple hover:bg-nuceraPurple showFlyout">
                        {level1_Item.title}
                      </span>
                    </f:link.page>
                  </f:else>
                </f:if>
              </f:then>
              <f:else>
                <!-- no link -->
              </f:else>
            </f:if>
          </f:for>
        </v:menu>
      </div>
      <div class="tx-indexedsearch-searchbox hidden lg:flex justify-end w-full items-center col-start-1 md:col-start-7 lg:col-start-11 xl:col-start-10 col-end-5 lg:col-end-10 lg:col-end-13 xl:col-end-13">
        <f:variable name="searchresultPage" value="{v:variable.typoscript(path: 'tknucera.pids.searchresults')}" />
        <form method="post" class="tx-indexedsearch-form  w-full relative" action="#">
          <f:form.textfield
            type="text"
            name="tx_indexedsearch_pi2[search][sword]"
            property="tx_indexedsearch_pi2[search][sword]"
            id="searchInput"
            value="{sword}"
            placeholder="{f:render(partial: 'Data/LanguageLabel', section: 'Main', arguments: '{key: \'tx_tknucera.label.searchLabel\'}')}"
            class="tx-indexedsearch-searchbox-sword indexed-search-autocomplete-sword input py-2 bg-darkmetalGray-200 font-normal pl-9 placeholder-shown:pl-0 w-full"
            additionalAttributes="{aria-label: 'Textsuche'}"
          />
          <div class="absolute left-2 top-2 w-5 h-5 flex items-center justify-end">
            <f:render partial="UI/Icon" section="Main" arguments="{name: 'search', class: 'w-5 h-5', additionalAttributes: {'aria-hidden': 'true'} }" />
          </div>
          <div class="search-autocomplete-results  no-results" data-mode="word" data-searchonclick="false" data-maxresults="10" data-minlength="2" data-searchurl="{f:uri.action(action: 'search', pageType: '7423794', noCache: 1, extensionName: 'indexedSearchAutocomplete', controller: 'Search')}"></div>
        </form>
      </div>
    </div>
  </nav>
  <script>
    let tknuceraNavigation = {
      adoptSubNavHeight: (id) => {
        let target = document.getElementById('subnav-id-' + id).closest('ul');
        target.style.height = 'auto';
        let targetHeight = target.offsetHeight,
          subNavHeight = document.getElementById('subnav-id-' + id).querySelector('ul').offsetHeight;
        if (subNavHeight < targetHeight) {
          target.style.height = '100%';
        } else {
          target.style.height = subNavHeight + 'px';
        }
      },
      showSubnav: (id, link) => {
        if (!link.classList.contains('is-showSubnav')) {
          tknuceraNavigation.hideSubnavs(() => {
            link.classList.add('is-showSubnav');
            link.setAttribute('aria-expanded', 'true');
            document.getElementById('subnav-id-' + id).classList.remove('hidden');
            tknuceraNavigation.adoptSubNavHeight(id);
          });
        } else {
          link.classList.remove('is-showSubnav');
          link.setAttribute('aria-expanded', 'false');
          tknuceraNavigation.hideSubnavs();
          tknuceraNavigation.adoptSubNavHeight(id);
        }
      },
      hideSubnavs: (callback) => {
        let subnavs = document.getElementsByClassName('navigationSubnav'),
          openSubnavLinks = document.getElementsByClassName('showSubnav');
        if (subnavs) {
          for (let i = 0;i < subnavs.length;i++) {
            subnavs[i].classList.add('hidden');
            if (i === subnavs.length - 1) {
              for (let j = 0;j < openSubnavLinks.length;j++) {
                openSubnavLinks[j].classList.remove('is-showSubnav');
                if (i === openSubnavLinks.length - 1) {
                  if (callback) {
                    callback();
                  }
                }
              }
            }
          }
        }
      },
      showFlyout: (id, link) => {
        if (!link.classList.contains('is-showFlyout')) {
          tknuceraNavigation.hideFlyouts(() => {
            link.classList.add('is-showFlyout');
            link.setAttribute('aria-expanded', 'true');
            const flyout = document.getElementById('flyout-id-' + id);
            flyout.classList.remove('hidden');
          });
        } else {
          link.classList.remove('is-showFlyout');
          link.setAttribute('aria-expanded', 'false');
          tknuceraNavigation.hideFlyouts();
        }
      },

      hideFlyouts: (callback) => {
        let flyouts = document.getElementsByClassName('navigationFlyout'),
          openFlyoutLinks = document.getElementsByClassName('showFlyout');
        if (flyouts) {
          for (let i = 0;i < flyouts.length;i++) {
            flyouts[i].classList.add('hidden');
            if (i === flyouts.length - 1) {
              for (let j = 0;j < openFlyoutLinks.length;j++) {
                openFlyoutLinks[j].classList.remove('is-showFlyout');
                if (j === openFlyoutLinks.length - 1) {
                  if (callback) {
                    callback();
                  }
                }
              }
            }
          }
        }
      },
      clickHandler: () => {
        const specifiedElement = document.querySelector('nav');
        document.addEventListener('click', event => {
          const isClickInside = specifiedElement.contains(event.target);
          if (!isClickInside) {
            tknuceraNavigation.hideFlyouts();
          }
        })
      },
      openMobileNavigation: (element) => {
        document.querySelector('html').classList.add('no-scroll');
        document.querySelector('.mobileNavigationLayer').classList.remove('hidden');
        document.querySelector('.hideMobileNav').classList.remove('hidden');
        element.classList.add('hidden');
        const navLayer = document.querySelector(".mobileNavigationLayer");

        tknuceraNavigation.trapFocus(navLayer);
      },
      closeMobileNavigation: (element) => {
        document.querySelector('html').classList.remove('no-scroll');
        document.querySelector('.mobileNavigationLayer').classList.add('hidden');
        document.querySelector('.showMobileNav').classList.remove('hidden');
        element.classList.add('hidden');
        tknucera.mobileNav.closeFirstSubmenu();
        tknucera.mobileNav.closeSecondSubmenu();
      },
      trapFocus: (element, withoutCloseButton = false) => {

      const focusableSelectors = `
        a[href], button, input, textarea, select, details, [tabindex]:not([tabindex="-1"])
      `;

      const closeMobileButton = document.querySelector(".hideMobileNav");

      let focusableEls = Array.from(element.querySelectorAll(focusableSelectors));
      if(!withoutCloseButton) {
        focusableEls.push(closeMobileButton);

      }
      focusableEls = focusableEls.filter(el => el.offsetParent !== null);

      if (focusableEls.length === 0) return;

      const firstFocusableEl = focusableEls[0];
      const lastFocusableEl = focusableEls[focusableEls.length - 1];

      firstFocusableEl.focus();

      document.addEventListener("keydown", function onKeydown(e) {
        if (e.key === "Tab") {
          if (e.shiftKey) {
            if (document.activeElement === firstFocusableEl) {
              e.preventDefault();
              lastFocusableEl.focus();
            }
          } else {
            if (document.activeElement === lastFocusableEl) {
              e.preventDefault();
              firstFocusableEl.focus();
            }
          }
        }
        if (e.key === "Escape") {
          tknuceraNavigation.closeMobileNavigation(
            document.querySelector(".hideMobileNav")
          );
          document.removeEventListener("keydown", onKeydown);
        }
      });
    }

    };
    tknuceraNavigation.clickHandler();
  </script>
</f:section>

<f:section name="Mobile">
  <nav id="mobileNavigation" role="dialog" aria-modal="true" class="mobileNavigationLayer fixed h-dvh w-full overflow-y-scroll top-0 left-0 z-40 bg-hydroGray text-darkmetalGray-150 hidden lg:hidden">
    <div class="flex flex-col justify-between items-center min-h-dvh w-full">
      <div class="flex w-full flex-col pt-24 overflow-y-scroll">
        <div class="overflow-hidden mx-2 mb-8">
          <v:menu expandAll="1"
                  pageUid="{v:variable.typoscript(path: 'tknucera.pids.root_page')}"
                  entryLevel="0"
                  levels="1"
                  as="level1mobile"
                  excludePages=""
                  classActive="text-primaryBlue"
                  useShortcutData="false"
                  useShortcutUid="false"
          >
            <ul id="mobileNav" class="relative text-[18px] left-0" style="width:300%;">
              <f:for each="{level1mobile}" as="level1mobile_Item" iteration="iterator">
                <li class="li_level1 w-1/3 border-b-1 border-b border-b-industryGray">
                  <f:if condition="{level1mobile_Item.hasSubPages}">
                    <f:then>
                      <button
                        type="button"
                        aria-haspopup="true"
                        aria-expanded="false"
                        aria-controls="flyout-id-{level1mobile_Item.uid}"
                        onclick="tknucera.mobileNav.openFirstSubmenu(this);"
                        class="cursor-pointer flex p-4 flex-row w-full justify-between items-center {level1mobile_Item.class}"
                        >
                        <span>{level1mobile_Item.title}</span>
                        <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-right', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
                      </button>
                      <v:menu expandAll="1"
                              pageUid="{level1mobile_Item.uid}"
                              levels="1"
                              as="level2mobile"
                              excludePages=""
                              classActive="text-primaryBlue"
                              useShortcutData="false"
                              useShortcutUid="false"
                      >
                        <ul class="level2Ul hidden w-1/3 absolute top-0 left-1/3" role="menu" id="flyout-id-{level1mobile_Item.uid}">
                          <li class="border-b-1 border-b border-b-industryGray" role="menuitem">
                            <button onclick="tknucera.mobileNav.closeFirstSubmenu(this);" class="cursor-pointer flex p-4 flex-row gap-4 pr-0">
                              <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-left', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
                              <span>Zurück zu {level1mobile_Item.title}</span>
                            </button>
                          </li>
                          <li class="border-b-1 border-b border-b-industryGray" role="menuitem">
                          <f:link.page
                            pageUid="{level1Mobile_Item.uid}"
                            class="cursor-pointer flex p-4 flex-row gap-4 pr-0 {f:if(condition: '{level1Mobile_Item.uid}=={page.uid}', then: 'font-tktypeBold', else: '')}">
                              <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.overview'}" />: {v:page.info(pageUid: level1mobile_Item.uid, field: 'title')}</f:link.page>
                          </li>
                          <f:for each="{level2mobile}" as="level2mobile_Item" iteration="iterator">
                            <li class="border-b-1 border-b border-b-industryGray" role="menuitem">
                              <f:if condition="{level2mobile_Item.hasSubPages}">
                                <f:then>
                                  <button
                                     aria-haspopup="true"
                                    aria-expanded="false"
                                    aria-controls="flyout-id-{level2mobile_Item.uid}"
                                    onclick="tknucera.mobileNav.openSecondSubmenu(this);"
                                    class="cursor-pointer flex p-4 flex-row w-full justify-between items-center {level2mobile_Item.class}"
                                  >
                                    <span>{level2mobile_Item.title}</span>
                                    <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-right', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
                                  </button>
                                  <v:menu expandAll="1"
                                          pageUid="{level2mobile_Item.uid}"
                                          levels="1"
                                          as="level3mobile"
                                          excludePages=""
                                          classActive="text-primaryBlue"
                                          useShortcutData="false"
                                          useShortcutUid="false"
                                  >
                                    <ul class="level3Ul w-full absolute left-full top-0 hidden" role="menu" id="flyout-id-{level2mobile_Item.uid}">
                                      <li class="" role="menuitem">
                                        <button onclick="tknucera.mobileNav.closeSecondSubmenu(this);" class="cursor-pointer flex p-4 flex-row gap-4 pr-0">
                                          <f:render partial="UI/Icon" section="Main" arguments="{name: 'chevron-left', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
                                          <span>Zurück zu {level2mobile_Item.title}</span>
                                        </button>
                                      </li>
                                      <li class="border-b-1 border-b border-b-industryGray" role="menuitem">
                                        <f:link.page
                                          pageUid="{level2Mobile_Item.uid}"
                                          class="cursor-pointer flex p-4 flex-row gap-4 pr-0 {f:if(condition: '{level2Mobile_Item.uid}=={page.uid}', then: 'font-tktypeBold', else: '')}">
                                          <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.overview'}" />: {v:page.info(pageUid: level2mobile_Item.uid, field: 'title')}</f:link.page>
                                      </li>
                                      <f:for each="{level3mobile}" as="level3mobile_Item" iteration="iterator">
                                        <li class="border-b-1 border-b border-b-industryGray" role="menuitem">
                                          <f:if condition="{page.uid}=={level3mobile_Item.uid} || {page.uid}=={level3mobile_Item.shortcut}">
                                            <f:then>
                                              <f:link.page pageUid="{level3mobile_Item.uid}"
                                                           title="{level3mobile_Item.title}"
                                                           class="p-4 flex-row gap-4 pr-0 {level3mobile_Item.class}"
                                                           additionalAttributes="{onclick: 'tknucera.mobileNav.close()'}">
                                                {level3mobile_Item.title}
                                              </f:link.page>
                                            </f:then>
                                            <f:else>
                                              <f:link.page pageUid="{level3mobile_Item.uid}"
                                                           title="{level3mobile_Item.title}"
                                                           class="block p-4 {level3mobile_Item.class}">
                                                {level3mobile_Item.title}
                                              </f:link.page>
                                            </f:else>
                                          </f:if>
                                        </li>
                                      </f:for>
                                    </ul>
                                  </v:menu>
                                </f:then>
                                <f:else>
                                  <f:link.page pageUid="{level2mobile_Item.uid}"
                                               title="{level2mobile_Item.title}"
                                               class="block p-4 {level2mobile_Item.class}">
                                    {level2mobile_Item.title}
                                  </f:link.page>
                                </f:else>
                              </f:if>
                            </li>
                          </f:for>
                        </ul>
                      </v:menu>
                    </f:then>
                    <f:else>
                      <f:link.page pageUid="{level1mobile_Item.uid}"
                                   title="{level1mobile_Item.title}"
                                   class="block p-4 {level1mobile_Item.class}">
                        {level1mobile_Item.title}
                      </f:link.page>
                    </f:else>
                  </f:if>
                </li>
              </f:for>
            </ul>
          </v:menu>
        </div>
        <div class="corporate_grid mb-8">
          <div class="col-span-full flex flex-col gap-6">
            <f:render partial="Navigation/Header" section="Language" arguments="{listClass: 'bg-hydroGray border border-industryGray w-fit', itemClass: 'py-2 px-4'}" />
            <f:render partial="Navigation/Header" section="Global" arguments="{listClass: 'bg-hydroGray border border-industryGray w-fit', itemClass: 'py-2 px-4'}" />
            <f:render partial="Navigation/Header" section="Contact" arguments="{listClass: 'bg-hydroGray'}" />
            <f:render partial="Navigation/Header" section="Login" arguments="{listClass: 'bg-hydroGray'}" />
          </div>
        </div>
      </div>
      <div class="w-full flex p-4 flex-row gap-4 pr-0 bg-darkmetalGray-250 text-white">
        <f:render partial="Navigation/Footer" section="social-media" arguments="{_all}" />
      </div>
    </div>
  </nav>
  <script>
    let tknucera = {
      mobileNav: {
        close: () => {
          document.querySelector('html').classList.remove('no-scroll');document.querySelector('.mobileNavigationLayer').classList.add('hidden');document.querySelector('.showMobileNav').classList.remove('hidden');document.querySelector('.hideMobileNav').classList.add('hidden')
        },
        openFirstSubmenu: (ele) => {
          document.querySelectorAll('ul.level2Ul').forEach((ul)=>{ul.classList.add('hidden')});
          ele.nextElementSibling.classList.remove('hidden');
          ele.setAttribute('aria-expanded', 'true');
          document.getElementById('mobileNav').style.left = '-100%';
          document.getElementById('mobileNav').style.height = ele.nextElementSibling.offsetHeight + 'px';
          const menu = ele.nextElementSibling;
          tknuceraNavigation.trapFocus(menu, true);
        },
        openSecondSubmenu: (ele) => {
          document.querySelectorAll('ul.level3Ul').forEach((ul)=>{ul.classList.add('hidden')});
          ele.nextElementSibling.classList.remove('hidden');
          ele.setAttribute('aria-expanded', 'true');
          document.getElementById('mobileNav').style.left = '-200%';
          document.getElementById('mobileNav').style.height = ele.nextElementSibling.offsetHeight + 'px';
          const menu = ele.nextElementSibling;
          tknuceraNavigation.trapFocus(menu, true);
        },
        closeFirstSubmenu: (ele) => {
          document.querySelectorAll('ul.level2Ul').forEach((ul)=>{ul.classList.add('hidden')})
          document.getElementById('mobileNav').style.left = '0'
          document.getElementById('mobileNav').style.height = 'auto';
          if(ele) {
            ele.setAttribute('aria-expanded', 'false');
          } else {
            const nav = document.querySelector('.mobileNavigationLayer');
            nav.querySelectorAll('button[aria-expanded="true"]').forEach((btn)=>{btn.setAttribute('aria-expanded', 'false')});
          }
        },
        closeSecondSubmenu: (ele) => {
          document.querySelectorAll('ul.level3Ul').forEach((ul)=>{ul.classList.add('hidden')})

          if(ele) {
            document.getElementById('mobileNav').style.left = '-100%';
            const menu = ele.closest('ul.level2Ul');
            document.getElementById('mobileNav').style.height = menu.offsetHeight + 'px';
            ele.setAttribute('aria-expanded', 'false');
            tknuceraNavigation.trapFocus(menu, true);
          } else {
            document.getElementById('mobileNav').style.left = '0';
            const nav = document.querySelector('.mobileNavigationLayer');
            nav.querySelectorAll('button[aria-expanded="true"]').forEach((btn)=>{btn.setAttribute('aria-expanded', 'false')});
          }

        }
      }
    }
  </script>
</f:section>

</html>
