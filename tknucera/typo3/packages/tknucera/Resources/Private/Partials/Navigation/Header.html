<?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">
  <header class="w-full bg-darkmetalGray-250 sticky top-0 z-[60] lg:block text-white">
    <div class="corporate_grid">
      <div class="corporate_grid_full">
        <f:link.page pageUid="{v:variable.typoscript(path: 'tknucera.pids.root_page')}" class="absolute top-0 z-60">
          <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo', class: 'w-20 h-20 lg:w-32 lg:h-32 bg-nuceraPurple', additionalAttributes: {'aria-hidden': 'true'} }" />
          <span class="sr-only">
            <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.home'}" />
          </span>
        </f:link.page>
        <div class="hidden lg:flex justify-end flex-row gap-4 py-2.5 text-grey-100 text-extrasmall leading-extrasmall font-tktype text-hydroGray">
          <f:render section="Language" arguments="{_all}" />
          <f:render section="Global" arguments="{_all}" />
          <f:render section="Contact" arguments="{_all}" />
          <f:render section="Login" arguments="{_all}" />
        </div>
      </div>
    </div>
  </header>
</f:section>

<f:section name="Global">

  <f:variable name="class" value="bg-darkmetalGray-200" />
  <f:variable name="classForItem" value="py-2 px-4 hover:bg-nuceraPurple" />

  <f:if condition="{listClass}">
    <f:variable name="class" value="{listClass}" />
  </f:if>
  <f:if condition="{itemClass}">
    <f:variable name="classForItem" value="{itemClass}" />
  </f:if>

  <f:render section="dropdown" arguments="{
    options: {
      0: {label: 'Global 2', url: '#'},
      1: {label: 'Global 3', url: '#'}
    },
    currentItem: 'Global',
    'icon-name': 'location',
    listClass: class,
    itemClass: classForItem
  }" />
</f:section>

<f:section name="Contact">
  <f:link.page
    pageUid="{}"
    title="{}"
    class="flex gap-2 items-center"
  >
    <f:render partial="UI/Icon" section="Main" arguments="{name: 'envelope', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
    <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.contact'}" />
  </f:link.page>
</f:section>

<f:section name="Login">
  <f:link.page
    pageUid="{}"
    title="{}"
    class="flex gap-2 items-center"
  >
    <f:render partial="UI/Icon" section="Main" arguments="{name: 'account-circle', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />
    Login
  </f:link.page>
</f:section>

<f:section name="Language">
  <f:variable name="hasTranslation"><f:spaceless>
    <f:uri.page pageUid="{page.uid}" additionalParams="{L: '1'}" />
  </f:spaceless></f:variable>

  <f:variable name="class" value="bg-darkmetalGray-200" />
  <f:variable name="classForItem" value="py-2 px-4 hover:bg-nuceraPurple" />

  <f:if condition="{listClass}">
    <f:variable name="class" value="{listClass}" />
  </f:if>
  <f:if condition="{itemClass}">
    <f:variable name="classForItem" value="{itemClass}" />
  </f:if>

  <v:page.languageMenu hideNotTranslated="true">

    <f:for each="{languageMenu}" as="item" iteration="iterator">
      <f:if condition="{item.current}">
        <f:variable name="currentLanguage" value="{item.label}" />
      </f:if>
    </f:for>

    <f:render section="dropdown" arguments="{options: languageMenu, currentItem: currentLanguage, 'icon-name': 'solid-globe', listClass: class, itemClass: classForItem}" />
  </v:page.languageMenu>
</f:section>

<f:section name="dropdown">
  <div
    x-data="{ open: false }"
    x-id="['dropdown']"
    x-on:click.outside="open = false"
    class="relative flex gap-2 items-center {class}"
>
    <f:render partial="UI/Icon" section="Main" arguments="{name: '{icon-name}', class: 'w-6 h-6', additionalAttributes: {'aria-hidden': 'true'} }" />

    <button
        type="button"
        x-bind:id="$id('toggle')"
        x-on:click.stop="open = !open"
        x-bind:aria-expanded="open"
        x-bind:aria-controls="$id('list')"
        aria-haspopup="listbox"
        class="flex items-center gap-2"
    >
        {currentItem}
        <f:render section="Main" partial="UI/Icon" arguments="{
          name: 'chevron-down',
          class: 'ml-auto w-4 h-4 transition-rotate duration-200',
          additionalAttributes: {'aria-hidden': 'true', 'x-bind:class': 'open ? \'rotate-180\' : \'\''}
        }"/>
    </button>

    <ul
        class="absolute z-[60] top-8 w-full flex flex-col {listClass}"
        x-show="open"
        x-on:click.outside="open = false"
        x-bind:id="$id('list')"
        role="listbox"
        x-bind:aria-labelledby="$id('toggle')"
        x-cloak
    >
        <f:for each="{options}" as="item" iteration="iterator">
            <li role="option" class="{itemClass}">
                <a href="{item.url}">
                    {item.label}
                </a>
            </li>
        </f:for>
    </ul>
  </div>

</f:section>

</html>
