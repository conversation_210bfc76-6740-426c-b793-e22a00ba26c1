<li?xml version="1.0" encoding="utf-8"?>
<html data-namespace-typo3-fluid="true"
      xmlns="http://www.w3.org/1999/xhtml"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>

<f:section name="Main">

  <footer class="w-full relative text-white bg-darkmetalGray py-16">
    <div class="corporate_grid">
      <div class="corporate_grid_full grid grid-cols-1 md:grid-cols-12 gap-y-12 gap-x-8">
        <div class="col-span-full lg:col-span-2 mb-16 lg:mb-0">
          <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo', class: 'absolute top-0 w-32 h-32 bg-nuceraPurple', additionalAttributes: {'aria-hidden': 'true'} }" />
          <f:render section="social-media" arguments="{class: 'hidden lg:flex'}" />
        </div>

        <div class="col-span-12 lg:col-span-10 grid grid-cols-12 lg:grid-cols-10 gap-x-4 gap-y-4 md:gap-y-8 lg:gap-10">
          <v:menu
            expandAll="1"
            pageUid="{v:variable.typoscript(path: 'tknucera.pids.footer_menu_main')}"
            entryLevel="0"
            as="level1"
            excludePages=""
            classActive=""
            lang="{page.sys_language_uid}"
            useShortcutData="true"
            useShortcutUid="false"
          >
            <f:for each="{level1}" as="level1_Item" iteration="iterator">
              <div class="col-span-12 sm:col-span-4 md:col-span-3 lg:col-span-2 flex flex-col flex-1 gap-x-4 md:gap-y-[20px]">
                <f:link.page
                  pageUid="{level1_Item.uid}"
                  title="{level1_Item.title}"
                  class="text-normal font-tktypeMedium md hover:underline underline-offset-2"
                >
                  {level1_Item.title}
                </f:link.page>
                <v:menu
                  expandAll="1"
                  pageUid="{level1_Item.uid}"
                  entryLevel="0"
                  as="level2"
                  excludePages=""
                  classActive=""
                  useShortcutData="true"
                  lang="{page.sys_language_uid}"
                  useShortcutUid="false"
                >
                  <ul class="flex-col flex-1 gap-2 hidden sm:flex">
                    <f:for each="{level2}" as="level2_Item" iteration="iterator">
                      <li>
                        <f:link.page
                          pageUid="{level2_Item.uid}"
                          title="{level2_Item.title}"
                          class="text-small hover:underline underline-offset-2"
                        >
                          {level2_Item.title}
                        </f:link.page>
                      </li>
                    </f:for>
                  </ul>
                </v:menu>
              </div>
            </f:for>
          </v:menu>
        </div>

        <div class="col-span-12 flex-col gap-4 hidden md:flex lg:hidden">
          <f:render section="social-media" />
        </div>

        <div class="col-span-12 flex flex-col gap-4">
          <v:menu
            expandAll="1"
            pageUid="{v:variable.typoscript(path: 'tknucera.pids.footer_menu_meta')}"
            entryLevel="0"
            as="metaNav"
            excludePages=""
            classActive=""
            useShortcutData="true"
            lang="{page.sys_language_uid}"
            useShortcutUid="false"
          >
            <div class="flex flex-col md:flex-row gap-4 lg:gap-8">
              <f:for each="{metaNav}" as="metaNav_Item" iteration="iterator">
                <f:link.page pageUid="{metaNav_Item.uid}"
                              title="{metaNavItem.title}"
                              class="text-normal md:text-small hover:underline underline-offset-2">
                  {metaNav_Item.title}
                </f:link.page>
              </f:for>
            </div>
          </v:menu>
        </div>

        <div class="col-span-12 flex-col gap-4 flex md:hidden">
          <f:render section="social-media" />
        </div>
      </div>
    </div>
  </footer>
</f:section>

<f:section name="social-media">
    <div class="flex flex-col md:justify-center h-1/2 lg:h-full md:mt-4 {class}">
      <p class="mb-4 md:mb-3 font-tktypeMedium">Social Media</p>
      <f:render partial="UI/SocialMedia" section="Main" />
    </div>
</f:section>

</html>
