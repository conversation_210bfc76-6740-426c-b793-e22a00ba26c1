<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
      xmlns:focuspoint="http://typo3.org/ns/HDNET/Focuspoint/ViewHelpers"
      xmlns:m="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      xmlns:ybpn="http://typo3.org/ns/Mogic/Ybpn/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default" />
<f:section name="Configuration">
  <flux:form id="styleguide"
             label="tknucera: Styleguide Layout"
             description="Styleguide Layout für die Darstellung von Design-Elementen und Komponenten">
    <flux:grid>
      <flux:grid.row>
        <flux:grid.column colPos="0" name="content" />
        <flux:grid.column colPos="1" name="content2" />
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Main">
  <!--TYPO3SEARCH_begin-->
  <v:content.render column="0" />
  <div class="corporate_grid bg-softWhite p-8">
    <div class="corporate_grid_full my-12">
      <div class="flex flex-col gap-4 p-4">
        <div class="h3 my-4">Paragraph normal</div>
        <f:render partial="UI/Paragraph" section="p" arguments="{content: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet'}" />
        <div class="h3 my-4">Paragraph big</div>
        <f:render partial="UI/Paragraph" section="p-big" arguments="{content: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet big'}" />
        <div class="h3 my-4">Paragraph small</div>
        <f:render partial="UI/Paragraph" section="p-small" arguments="{content: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet small'}" />
        <div class="h3 my-4">Paragraph extrasmall</div>
        <f:render partial="UI/Paragraph" section="p-extrasmall" arguments="{content: 'Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore magna aliquyam erat, sed diam voluptua. At vero eos et accusam et justo duo dolores et ea rebum. Stet clita kasd gubergren, no sea takimata sanctus est Lorem ipsum dolor sit amet extrasmall'}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Dropdown normal</div>
      <div class="flex flex-row flex-wrap p-4">
        <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'} }" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Dropdown ghost</div>
      <div class="flex flex-row flex-wrap p-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
        <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'ghost'}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Dropdown outline</div>
      <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
          <div class=" p-4 rounded-lg flex flex-row flex-wrap min-h-[270px]" style="width:100%;background: rgba(255, 255, 255, .2);
          -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);">
            <f:render partial="UI/Dropdown" section="Main" arguments="{options: {1: 'Apple', 2: 'Sunflower', 3: 'Zebra'}, type: 'outline'}" />
          </div>
      </div>
    </div>
    <div class="corporate_grid_full ">
        <div class="h3 my-4">Textarea normal</div>
        <div class="flex flex-row flex-wrap p-4 gap-4">
          <f:render partial="UI/Textarea" section="Main" arguments="{_all}" />
          <f:render partial="UI/Textarea" section="Main" arguments="{success: 1}" />
          <f:render partial="UI/Textarea" section="Main" arguments="{error: 1}" />
        </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Textarea ghost</div>
      <div class="flex flex-row flex-wrap p-4 gap-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
        <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost'}" />
        <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost', success: 1}" />
        <f:render partial="UI/Textarea" section="Main" arguments="{type: 'ghost', error: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Textarea outline</div>
      <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
          <div class="p-4 rounded-lg flex flex-row flex-wrap min-h-[270px] gap-4" style="width:100%;background: rgba(255, 255, 255, .2);
          -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);">
            <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline'}" />
            <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline', success: 1}" />
            <f:render partial="UI/Textarea" section="Main" arguments="{type: 'outline', error: 1}" />
          </div>
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Text input normal</div>
      <div class="flex flex-row flex-wrap p-4 gap-4">
        <f:render partial="UI/Textinput" section="Main" arguments="{_all}" />
        <f:render partial="UI/Textinput" section="Main" arguments="{success: 1}" />
        <f:render partial="UI/Textinput" section="Main" arguments="{error: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Text input ghost</div>
      <div class="flex flex-row flex-wrap p-4 gap-4 bg-darkmetalGray-150 text-skyGray min-h-[270px]">
        <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost'}" />
        <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost', success: 1}" />
        <f:render partial="UI/Textinput" section="Main" arguments="{type: 'ghost', error: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Text input outline</div>
      <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 min-h-[270px]">
          <div class=" p-4 rounded-lg flex flex-row flex-wrap min-h-[270px] gap-4" style="width:100%;background: rgba(255, 255, 255, .2);
          -webkit-backdrop-filter: blur(4px);
          backdrop-filter: blur(4px);">
            <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline'}" />
            <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline', success: 1}" />
            <f:render partial="UI/Textinput" section="Main" arguments="{type: 'outline', error: 1}" />
          </div>
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Radio input</div>
      <div class="flex flex-row flex-wrap p-4 gap-4">
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputName', value: 'radioInputValue1', label: 'Radio Input 1'}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputName', value: 'radioInputValue2', label: 'Radio Input 2'}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputName', value: 'radioInputValue3', label: 'Radio Input 3'}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputName', value: 'radioInputValue4', label: 'Radio Input 4', checked: 1}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputName', value: 'radioInputValue5', label: 'Radio Input 5', disabled: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Radio input dark</div>
      <div class="flex flex-row flex-wrap p-4 gap-4 bg-darkmetalGray-150 text-skyGray">
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputNameDark', value: 'radioInputValue1', label: 'Radio Input 1', dark: 1}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputNameDark', value: 'radioInputValue2', label: 'Radio Input 2', dark: 1}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputNameDark', value: 'radioInputValue3', label: 'Radio Input 3', dark: 1}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputNameDark', value: 'radioInputValue4', label: 'Radio Input 4', dark: 1, checked: 1}" />
        <f:render partial="UI/Radioinput" section="Main" arguments="{name: 'radioInputNameDark', value: 'radioInputValue5', label: 'Radio Input 5', dark: 1, disabled: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Checkbox input</div>
      <div class="flex flex-row flex-wrap p-4 gap-4">
        <f:render partial="UI/Checkboxinput" section="Main" arguments="{name: 'checkboxInputName', value: 'checkboxInputValue1', label: 'Checkbox Input 1'}" />
        <f:render partial="UI/Checkboxinput" section="Main" arguments="{name: 'checkboxInputName', value: 'checkboxInputValue3', label: 'Checkbox Input 2'}" />
        <f:render partial="UI/Checkboxinput" section="Main" arguments="{name: 'checkboxInputName', value: 'checkboxInputValue4', label: 'Checkbox Input 2', checked: 1}" />
        <f:render partial="UI/Checkboxinput" section="Main" arguments="{name: 'checkboxInputName', value: 'checkboxInputValue5', label: 'Checkbox Input 3', disabled: 1}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Tabs</div>
      <div class="flex flex-row flex-wrap">
        <f:variable name="tabs" value="{
          0: {title: 'Overview', content: 'This is the overview content with a very long title that should be truncated.'},
          1: {title: 'Features', content: 'Here are the key features of the product.'},
          2: {title: 'Documentation', content: 'Documentation content goes here.'},
          3: {title: 'Support', content: 'Support information and contact details.'},
          4: {title: 'Downloads', content: 'Download links and resources.'},
          5: {title: 'About', content: 'About this project and team information.'}
        }" />
        <f:render partial="UI/Tabs" section="Main" arguments="{
          label: 'Sample Tabs',
          tabs: tabs
        }" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Tags</div>
      <div class="flex flex-row flex-wrap p-4 gap-4">
        <f:render partial="UI/Tag" section="Tag" arguments="{label: 'Chlor-Alkali'}" />
        <f:render partial="UI/Tag" section="ContentHint" arguments="{icon: 'chat'}" />
        <f:render partial="UI/Tag" section="ContentHint" arguments="{icon: 'pdf'}" />
        <f:render partial="UI/Tag" section="ContentHint" arguments="{icon: 'play'}" />
        <f:render partial="UI/Tag" section="ContentHint" arguments="{label: 'Meinung des CEO'}" />
      </div>
    </div>
    <div class="corporate_grid_full">
        <div class="h3 my-4">Logo</div>
        <div class="flex flex-row  gap-8 items-start">
          <div class="w-full text-white flex flex-col gap-4">
              <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo', class: 'bg-nuceraPurple p-4'}" />
              <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo-bar', class: 'bg-nuceraPurple p-4'}" />
          </div>
          <div class="w-full text-white flex flex-col gap-4">
              <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo', class: 'bg-darkmetalGray p-4'}" />
              <f:render partial="UI/Icon" section="Main" arguments="{name: 'logo-bar', class: 'bg-darkmetalGray p-4'}" />
          </div>
        </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Headlines</div>
      <div class="flex flex-col gap-4 p-4 bg-techGray">
        <f:render partial="UI/Headline" section="h1" arguments="{content: 'H1 Headline'}" />
        <f:render partial="UI/Headline" section="h1-purple-bg" arguments="{content: 'H1 Headline Purple Background'}" />
        <f:render partial="UI/Headline" section="h1-white-bg" arguments="{content: 'H1 Headline White Background'}" />
        <f:render partial="UI/Headline" section="h2" arguments="{content: 'H2 Headline'}" />
        <f:render partial="UI/Headline" section="h2-purple-bg" arguments="{content: 'H2 Headline Purple Background'}" />
        <f:render partial="UI/Headline" section="h2-white-bg" arguments="{content: 'H2 Headline White Background'}" />
        <f:render partial="UI/Headline" section="h3" arguments="{content: 'H3 Headline'}" />
        <f:render partial="UI/Headline" section="subtitle" arguments="{content: 'Subtitle'}" />
        <f:render partial="UI/Headline" section="subtitle-bold" arguments="{content: 'Subtitle Bold'}" />
      </div>
    </div>
    <div class="corporate_grid_full ">
      <div class="h3 my-4">Table</div>
      <div class="flex flex-col gap-4 p-4">
        <f:variable name="headings" value="{
            0: 'Name',
            1: 'Datum',
            2: 'Präsentation',
            3: 'Corporate News',
            4: 'Webcast',
            5: 'Kennzahlen'
          }" />

        <f:variable name="row" value="{
            0: 'Q1/H1-Ergebnis 2025/2026',
            1: '15/05/2025',
            2: 'Row Data 2',
            3: 'Row Data 3',
            4: 'Row Data 4',
            5: 'Row Data 5'
        }" />
        <f:variable name="rows" value="{
            0: row,
            1: row,
            2: row,
            3: row,
            4: row,
            5: row,
          }" />

        <f:render partial="UI/Table" section="Main" arguments="{
          label: 'Sample Table',
          headings: headings,
          rows: rows
        }" />
      </div>
    </div>
    <div class="corporate_grid_full">
      <f:variable name="imagePath" value="EXT:tknucera/Resources/Public/Images/dummy2.png" />
      <div class="h3 my-4">Images</div>
      <div class="corporate_grid_halfLeft">
        <div class="h4 my-4">Original Ratio</div>
        <f:render
          partial="UI/Image"
          section="Image"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-0.2',
              focus_point_y: '-0.2'
            },
            customClasses: 'block w-full h-auto'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 1x1</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '1x1',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 4x3</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '4x3',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 3x2</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '3x2',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 3x1</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '3x1',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 16x9</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '16x9',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 16x10</div>
        <f:render partial="UI/Image" section="ImageFocuspoint" arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '16x10',
            customClasses: 'block'
          }" />
      </div>
      <div class="corporate_grid_halfRight">
        <div class="h4 my-4">Aspect Ratio: 2x1</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '2x1',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 5x2</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '5x2',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: Golden</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: 'golden',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 2x3</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '2x3',
            customClasses: 'block'
          }"
        />
        <div class="h4 my-4">Aspect Ratio: 3x4</div>
        <f:render
          partial="UI/Image"
          section="ImageFocuspoint"
          arguments="{
            image: {
              id: '{imagePath}',
              alternative: 'Dummy Image',
              width: '2048',
              height: '1366',
              focus_point_x: '-20',
              focus_point_y: '-20'
            },
            imageratio: '3x4',
            customClasses: 'block'
          }"
        />
      </div>
    </div>
  </div>
  <!--TYPO3SEARCH_end-->
</f:section>
</html>
