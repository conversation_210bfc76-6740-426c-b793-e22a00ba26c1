<?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="accordeonList" label="Akkordeon (Rahmen)" description="Akkordeon Rahmen in den Akkordeon Elemente platziert werden können">
    <flux:form.option.group value="tknucera-content-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-area.svg" />
    <flux:grid>
      <flux:grid.row>
        <flux:grid.column label="Akkordeon Einträge" name="accordeonList" colPos="0">
          <flux:form.variable name="allowedContentTypes" value="tknucera_accordeonitem" />
        </flux:grid.column>
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgba(231, 165, 0, 1);height: 0.5rem"></div>
</f:section>

<f:section name="Main">
  <div id="c{f:render(partial: 'Data/RecordUid', section: 'main', arguments: _all) -> f:spaceless()}" class="scroll-mt-16">
    <dl class="accordeon border-t border-solid border-darkmetalGray">
      <f:for each="{flux:content.get(area: 'accordeonList')}" as="contentElement">
        <f:format.raw>{contentElement}</f:format.raw>
      </f:for>
    </dl>
  </div>
</f:section>

</html>
