<hr?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="keyNumbers"
             label="Schlüsselzahlen"
             description="Schlüsselzahlen mit Icon und Beschreibung">
    <flux:form.option.group value="tknucera-content-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-text.svg" />

    <flux:field.text
      name="settings.title"
      label="Überschrift"
      rows="1"
      enableRichText="0"
    />

    <flux:form.section name="sections" label="Abschnitte">
        <flux:form.object name="section" label="Abschnitt">
            <flux:field.text
                name="subtitle"
                label="Untertitel"
                rows="1"
                enableRichText="0"
            />
            <flux:field.text
                name="description"
                label="Beschreibung"
                enableRichText="0"
                rows="5"
            />
        </flux:form.object>
    </flux:form.section>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgb(208,0,231);height: 0.5rem;margin-bottom: 1rem"></div>
    <p class="">{settings.title}</p>
    <f:for each="{sections}" as="sectionObject">
      <span>
        <p>{sectionObject.section.subtitle}</p>
        <p>{sectionObject.section.description}</p>
      </span>
    </f:for>
</f:section>

<f:section name="Main">
  <div class="py-10 px-4 lg:px-40 bg-nuceraPurple text-white flex flex-col gap-8 lg:flex-row lg:items-stretch items-center justify-center">
    <p class="text-subtitle font-tktypeBold text-center flex items-center">{settings.title}</p>
    <f:for each="{sections}" as="sectionObject">
      <span class="relative flex flex-col justify-center py-6 lg:py-0 lg:pl-8 lg:border-l border-white
        after:content-[''] after:absolute after:bottom-0 after:left-1/2
        after:-translate-x-1/2 after:w-1/2 after:border-b lg:after:border-0"
      >
        <p class="text-h2-mobile font-tktypeBold">{sectionObject.section.subtitle}</p>
        <p class="text-small">{sectionObject.section.description}</p>
      </span>
    </f:for>
  </div>
</f:section>

</html>
