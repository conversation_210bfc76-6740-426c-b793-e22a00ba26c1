<?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="text"
             label="Text"
             description="Textelement für Headlines und/oder Bodytext sowie eines Link (Button)">
    <flux:form.option.group value="tknucera-layout-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-text.svg" />

    <f:comment>
      Optionen:
      - wrapper ohne extra styles aber mit id für sprungmarke
      - wrapper kann hintergrundfarbe, hintergrundbild und Abstände erhalten
    </f:comment>

    <flux:form.sheet name="settings.text"
                     label="Text"
                     description="">
      <flux:field.text name="settings.bodytext"
                       label="Text"
                       enableRichText="1"
                       required="1"
                       richtextConfiguration="Default"/>
    </flux:form.sheet>
    <flux:form.sheet name="settings.images"
                     label="Bild(er)"
                     description="">
      <flux:field.inline.fal name="settings.image"
                             label="Bild 1"
                             maxItems="1"
                             minItems="0"
                             collapseAll="1"
                             allowedExtensions="jpeg,jpg,png,webp,svg"/>
      <flux:field.inline.fal name="settings.image2"
                             label="Bild 2 (für Bildkombinationen)"
                             maxItems="1"
                             minItems="0"
                             collapseAll="1"
                             allowedExtensions="jpeg,jpg,png,webp,svg"/>
      <flux:field.text name="settings.imageCaption"
                       label="Bildunterschrift"
                       enableRichText="1"
                       richtextConfiguration="Default"/>
      <flux:field.select name="settings.imageRatio"
                         label="Bildverhältnis"
                         default=""
                         items="{
        0:{0:'Originalgröße', 1:''},
        1:{0:'4:3', 1:'4x3'},
        2:{0:'16:9', 1:'16x9'},
        3:{0:'16:10', 1:'16x10'},
        4:{0:'1:1', 1:'1x1'},
        5:{0:'2:1', 1:'2x1'},
        6:{0:'2:3', 1:'2x3'},
        7:{0:'2:4', 1:'2x4'},
        8:{0:'3:4', 1:'3x4'},
        9:{0:'3:2', 1:'3x2'},
        10:{0:'5:2', 1:'5x2'},
        11:{0:'Goldener Schnitt', 1:'golden'},
        12:{0:'Bildkombination (rechts)', 1:'imageSetRight'},
        13:{0:'Bildkombination (links)', 1:'imageSetLeft'}
        }" />
      <flux:field.checkbox name="settings.roundedCorners"
                           label="Bild mit abgerundeten Ecken anzeigen?"
                           default="1"/>
    </flux:form.sheet>
    <flux:form.sheet name="settings.design"
                     label="Design"
                     description="">
      <flux:field.select name="settings.backgroundColor"
                         label="Hintergrundfarbe"
                         items="{
        0:{0:'keine Hintergrundfarbe', 1:''},
        1:{0:'Dezente Hintergrundfarbe', 1:'light'},
        2:{0:'Mittlere Hintergrundfarbe', 1:'medium'},
        3:{0:'Dunkle Hintergrundfarbe', 1:'dark'}
        }" />

      <flux:field.select name="settings.padding"
                         label="Innen-Abstände"
                         items="{
        0:{0:'keine Innen-Abstände', 1:''},
        1:{0:'Kleine Innen-Abstände', 1:'small'},
        2:{0:'Mittlere Innen-Abstände', 1:'medium'},
        3:{0:'Dunkle Innen-Abstände', 1:'large'}
        }" />

      <flux:field.select name="settings.margin"
                         label="Außen-Abstände"
                         items="{
        0:{0:'keine Außen-Abstände', 1:''},
        1:{0:'Kleine Außen-Abstände', 1:'small'},
        2:{0:'Mittlere Außen-Abstände', 1:'medium'},
        3:{0:'Dunkle Außen-Abstände', 1:'large'}
        }" />
    </flux:form.sheet>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgb(208,0,231);height: 0.5rem;margin-bottom: 1rem"></div>
  <div>
    {settings.bodytext}
  </div>
</f:section>

<f:section name="Main">
  <f:variable name="cUid"
              value="c{f:render(partial: 'Data/RecordUid', section: 'main', arguments: _all) -> f:spaceless()}" />

  <f:render partial="UI/Box/ContentWrapper"
            section="Main"
            contentAs="content"
            arguments="{
            cUid: cUid,
            settings: settings
            }">
    <f:format.html>{settings.bodytext}</f:format.html>
  </f:render>
</f:section>

</html>
