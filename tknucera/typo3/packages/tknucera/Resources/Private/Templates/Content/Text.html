<?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="text" label="Text" description="Textelement für Headlines und/oder Bodytext sowie eines Link (Button)">
    <flux:form.option.group value="tknucera-layout-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-text.svg" />
    <flux:field.text name="settings.bodytext" label="Text" enableRichText="1" required="1" />
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgba(231, 165, 0, 1);height: 0.5rem;margin-bottom: 1rem"></div>
  <div>
    {settings.bodytext}
  </div>
</f:section>

<f:section name="Main">
  <div id="c{f:render(partial: 'Data/RecordUid', section: 'main', arguments: _all) -> f:spaceless()}" class="">
    <f:format.html>{settings.bodytext}</f:format.html>
  </div>
</f:section>

</html>
