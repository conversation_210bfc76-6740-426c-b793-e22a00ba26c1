<?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
        xmlns:encore="http://typo3.org/ns/Ssch/Typo3Encore/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="accordeonItem" label="Akkordeon (Eintrag)" description="Akkordeon Eintrag mit Titel und eigenem Inhaltsbereich für entsprechende Inhaltselemente">
    <flux:form.option.group value="tknucera-content-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-accordeon.svg" />

    <flux:field.text name="settings.title"
                     label="Titel"
                     enableRichText="0"
                     rows="1" />
    <flux:grid>
      <flux:grid.row>
        <flux:grid.column label="Akkordeon Inhalte" name="accordeonContent" colPos="0" />
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgba(231, 165, 0, 1);height: 0.5rem"></div>
</f:section>

<f:section name="Main">
    <dt class="accordeonHead subtitle text-darkmetalGray-200 py-8 flex w-full justify-between cursor-pointer items-center trnsition-rotate duration-200">
        {settings.title}
        <span class="relative w-6 h-6">
          <span class="absolute top-0 left-0 rotate-90">
            <f:render partial="UI/Icon" section="Main" arguments="{name: 'minus', class: 'w-6 h-6 transition-rotate duration-200'}" />
          </span>
          <f:render partial="UI/Icon" section="Main" arguments="{name: 'minus', class: 'absolute top-0 left-0 w-6 h-6 transition-rotate duration-200'}" />
        </span>
    </dt>
    <dd class="accordeonBody overflow-hidden transition-height ease-in-out delay-0 duration-300 border-b border-solid border-darkmetalGray">
        <div class="py-4">
            <f:for each="{flux:content.get(area: 'accordeonContent')}" as="contentElement">
              <f:format.raw>{contentElement}</f:format.raw>
            </f:for>
        </div>
    </dd>
</f:section>

</html>
