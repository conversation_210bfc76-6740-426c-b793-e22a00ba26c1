<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>
<div class="flex flex-col min-h-screen">
  <f:debug>{_all}</f:debug>
  <header class="h-[119px] w-full relative z-1 bg-white" style="box-shadow: 0 -24px 20px 20px rgba(0,0,0,0.45);">
    <div class="corporate_grid">
      ((header))
    </div>
  </header>
  <nav role="navigation" class="corporate_grid relative">
    <div class="absolute -top-8 left-12 z-2">
      ((navigation))
    </div>
  </nav>
  <main role="main" id="main" class="flex-grow">
    <f:render section="Main"/>
  </main>
  <footer class="h-[500px] bg-darkGrey text-white">
    <div class="corporate_grid">
      <div class="corporate_grid_full">
        <f:debug title="Footer Navigation Debug">{footernavigation}</f:debug>
        <f:if condition="{footernavigation}">
          <nav class="footer-navigation">
            <f:for each="{footernavigation}" as="menuItem">
              <div class="footer-menu-item">
                <f:link.page pageUid="{menuItem.data.uid}" class="text-white hover:text-gray-300">
                  {menuItem.title}
                </f:link.page>
                <f:if condition="{menuItem.children}">
                  <ul class="footer-submenu">
                    <f:for each="{menuItem.children}" as="subMenuItem">
                      <li>
                        <f:link.page pageUid="{subMenuItem.data.uid}" class="text-white hover:text-gray-300">
                          {subMenuItem.title}
                        </f:link.page>
                      </li>
                    </f:for>
                  </ul>
                </f:if>
              </div>
            </f:for>
          </nav>
        </f:if>
        <f:else>
          <p>No footer menu found. Check if page ID {$tknucera.pids.footer_menu} exists and has subpages.</p>
        </f:else>
      </div>
    </div>
  </footer>
</div>
</html>
