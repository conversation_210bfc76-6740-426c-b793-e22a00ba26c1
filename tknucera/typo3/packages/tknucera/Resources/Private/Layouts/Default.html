<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>
<div class="flex flex-col min-h-screen">
  <header class="h-[119px] w-full relative z-1 bg-white" style="box-shadow: 0 -24px 20px 20px rgba(0,0,0,0.45);">
    <div class="corporate_grid">
      ((header))
    </div>
  </header>
  <nav role="navigation" class="corporate_grid relative">
    <div class="absolute -top-8 left-12 z-2">
      ((navigation))
    </div>
  </nav>
  <main role="main" id="main" class="flex-grow">
    <f:render section="Main"/>
  </main>
  <footer class="h-[500px] bg-darkGrey text-white">
    <div class="corporate_grid">
      ((footer))
    </div>
  </footer>
</div>
</html>
