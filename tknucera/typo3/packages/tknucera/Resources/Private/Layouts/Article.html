<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>

<f:variable name="pageMediaPublicUrl" value="{page -> f:cObject(typoscriptObjectPath: 'lib.pageMediaImage')}" />


<f:debug>{_all}</f:debug>

<div class="flex flex-col min-h-screen">
  <f:render partial="Navigation/Header" section="Main" arguments="{_all}" />
  <f:render partial="Navigation/Navigation" section="Desktop" arguments="{_all}" />
  <main role="main" id="main" class="flex-grow bg-fixed bg-cover" style="background-image: url('{pageMediaPublicUrl}');">
    <div class="corporate_grid">
      <div class="corporate_grid_full">
        <div class="py-6 md:py-10">
          <f:render partial="UI/Breadcrumb" section="Main" arguments="{_all}" />
        </div>
      </div>
    </div>
    <div class="mt-[552px] px-[188px] mb-20">
      <f:render section="Main"/>

      <div class="flex flex-col gap-6 px-[112px] py-10 bg-ultraPurple text-hydroGray">
        <p class="text-extrasmall font-tktypeBold uppercase">
          <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.follow_us'}" />
        </p>
        <f:render partial="UI/SocialMedia" section="Main" />
      </div>
    </div>
  </main>
  <f:render partial="Navigation/Footer" section="Main" arguments="{_all}" />
  <f:render partial="Navigation/Navigation" section="Mobile" arguments="{_all}" />
</div>
</html>
