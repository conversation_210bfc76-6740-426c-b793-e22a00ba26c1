<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>
<f:layout name="Default"/>

<f:variable name="pageMediaPublicUrl" value="{page -> f:cObject(typoscriptObjectPath: 'lib.pageMediaImage')}" />
<v:variable.set name="categories" value="{f:cObject(typoscriptObjectPath: 'lib.contentCategories', data:'{recordUid: record.uid}') -> f:split(separator: ',')}" />
<div class="flex flex-col min-h-screen">
  <f:render partial="Navigation/Header" section="Main" arguments="{_all}" />
  <f:render partial="Navigation/Navigation" section="Desktop" arguments="{_all}" />
  <main role="main" id="main" class="flex-grow bg-fixed bg-cover" style="background-image: url('{pageMediaPublicUrl}');">
    <div class="corporate_grid">
      <div class="corporate_grid_full relative ">
        <div class="py-6 md:py-10">
          <f:render partial="UI/Breadcrumb" section="Main" arguments="{_all}" />
        </div>
        <div class="articleMainContent relative mt-44 px-10 lg:px-32 mb-10 lg:mb-20">
          <div class="flex flex-wrap gap-2 mt-20 mb-6">
            <f:if condition="{categories.0} !== ''">
              <f:for each="{categories}" as="category">
                <f:render partial="UI/Tag" section="Tag" arguments="{label: category}" />
              </f:for>
            </f:if>
          </div>
          <f:render section="Main"/>
          <div class="flex flex-col gap-3 lg:gap-6 px-10 lg:px-28 py-4 lg:py-10 bg-ultraPurple text-hydroGray">
            <p class="text-extrasmall font-tktypeBold uppercase">
              <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.follow_us'}" />
            </p>
            <f:render partial="UI/SocialMedia" section="Main" />
          </div>
        </div>
      </div>
    </div>

  </main>
  <f:render partial="Navigation/Footer" section="Main" arguments="{_all}" />
  <f:render partial="Navigation/Navigation" section="Mobile" arguments="{_all}" />
</div>
</html>
