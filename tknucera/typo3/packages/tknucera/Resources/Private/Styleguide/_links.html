<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Links</title>
</head>
<body>

<template id="link">
    <a
      href="#"
      aria-label="Where this linkg is going if text of the link is not descriptive"
      rel="noopener noreferrer"
      class="underline text-normal line-height-normal text-TKTypeRegular text-darkmetalGray-250">
        Lorem ipsum
    </a>
</template>

<template id="link-dark">
  <a href="#"
    aria-label="Where this linkg is going if text of the link is not descriptive"
    rel="noopener noreferrer"
    class="underline text-normal line-height-normal text-TKTypeRegular text-white">
      Lorem ipsum
  </a>
</template>

<template id="linked-object">
    <a
      href="#"
      aria-label="Where this linkg is going if text of the link is not descriptive"
      rel="noopener noreferrer"
      class="hover:underline underline-offset-4 text-nuceraPurple text-normal line-height-normal text-TKTypeRegular flex items-center gap-1">
        Lorem ipsum
        <svg class="inline-block" width="21" height="21"><use href="assets/images/spritemap.svg#arrow-right"/></svg>
    </a>
    <a
      href="#"
      aria-label="Where this linkg is going if text of the link is not descriptive"
      rel="noopener noreferrer"
      class="hover:underline underline-offset-4 text-nuceraPurple text-normal line-height-normal text-TKTypeRegular flex items-center gap-1">
        <svg class="inline-block" width="18" height="19"><use href="assets/images/spritemap.svg#chevron-right "/></svg>
        Lorem ipsum
    </a>
</template>

<template id="linked-object-dark">
      <a
      href="#"
      aria-label="Where this linkg is going if text of the link is not descriptive"
      rel="noopener noreferrer"
      class="hover:underline underline-offset-4 text-white text-normal line-height-normal text-TKTypeRegular flex items-center gap-1">
        Lorem ipsum
        <svg class="inline-block" width="21" height="21"><use href="assets/images/spritemap.svg#arrow-right"/></svg>
    </a>
    <a
      href="#"
      aria-label="Where this linkg is going if text of the link is not descriptive"
      rel="noopener noreferrer"
      class="hover:underline underline-offset-4 text-white text-normal line-height-normal text-TKTypeRegular flex items-center gap-1">
        <svg class="inline-block" width="18" height="19"><use href="assets/images/spritemap.svg#chevron-right "/></svg>
        Lorem ipsum
    </a>
</template>
</body>
</html>
