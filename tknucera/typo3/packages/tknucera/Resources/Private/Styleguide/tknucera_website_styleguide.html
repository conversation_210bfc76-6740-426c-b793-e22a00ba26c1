<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ThyssenKrupp Nucera - Styleguide in HTML/CSS/JS</title>

    <link rel="icon" href="assets/images/favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="assets/images/favicon.ico" type="image/x-icon"/>

    <link rel="icon" type="image/gif" href="assets/images/favicon.gif"/>
    <link rel="icon" type="image/gif" href="assets/images/favicon.gif"/>

    <link rel="stylesheet" href="assets/css/focuspoint.css" />

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio,container-queries"></script>

    <script src="assets/js/tailwindconfig.js"></script>

    <style type="text/tailwindcss">

        @layer base {
          @font-face {
              font-family: 'TKType';
              font-style: normal;
              font-weight: 400;
              font-display: swap;
              src: url('assets/fonts/TKTypeRegular.woff2') format('woff2'),
              url('assets/fonts/TKTypeRegular.woff') format('woff'),
              url('assets/fonts/TKTypeRegular.ttf') format('truetype');
          }
          @font-face {
              font-family: 'TKTypeMedium';
              font-style: normal;
              font-weight: 500;
              font-display: swap;
              src: url('assets/fonts/TKTypeMedium.woff2') format('woff2'),
              url('assets/fonts/TKTypeMedium.woff') format('woff'),
              url('assets/fonts/TKTypeMedium.ttf') format('truetype');
          }
          @font-face {
              font-family: 'TKTypeBold';
              font-style: normal;
              font-weight: 700;
              font-display: swap;
              src: url('assets/fonts/TKTypeBold.woff2') format('woff2'),
              url('assets/fonts/TKTypeBold.woff') format('woff');
          }
          @font-face {
              font-family: 'Orbitron-Bold';
              font-style: normal;
              font-weight: 700;
              font-display: swap;
              src: url('assets/fonts/Orbitron-Bold.woff2') format('woff2'),
              url('assets/fonts/Orbitron-Bold.woff') format('woff'),
              url('assets/fonts/Orbitron-Bold.ttf') format('truetype');
          }
        }

        @layer utilities {
            .font-small {
                font-size: 75%;
            }
            .font-large {
                font-size: 120%;
            }
        }

        @layer components {
            body {
                @apply font-tktype text-pitchBlack;
            }
            .corporate_grid {
                @apply grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12 w-full max-w-[1280px] mx-auto;
            }

            .corporate_grid_full {
                @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-9 lg:col-end-13;
            }

            .corporate_grid_halfLeft {
                @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-5 lg:col-end-7;
            }

            .corporate_grid_halfRight {
                @apply col-start-1 md:col-start-5 lg:col-start-7 col-end-5 md:col-end-9 lg:col-end-13;
            }

            .corporate_grid_flex3Cols {
                @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
            }

            .corporate_grid_flex3Cols .corporate_grid_flexCol {
                @apply w-full md:w-1/3;
            }

            /* headlines */
            .h1 {
                @apply font-tktypeBold text-h1-mobile lg:text-h1 leading-h1-mobile lg:leading-h1;
            }

            .h2 {
                @apply font-tktypeMedium text-h2-mobile lg:text-h2 leading-h2-mobile lg:leading-h2;
            }

            .h3 {
                @apply font-tktypeMedium text-h3-mobile lg:text-h3 leading-h3-mobile lg:leading-h3;
            }

            .subtitle {
                @apply text-subtitle-mobile lg:text-subtitle leading-subtitle-mobile lg:leading-subtitle;
            }

            p a {
                @apply underline;
            }

            .h1:has(+ .h3) {
                /* if spacing should be different for headline sets */
            }

            p {
                @apply text-normal-mobile md:text-normal leading-normal-mobile md:leading-normal;
            }

            .small {
                @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
            }

            .extrasmall {
                @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
            }

            .big {
                @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
            }

            .small p {
                @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
            }

            .extrasmall p {
                @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
            }

            .big p {
                @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
            }

            .button {
                @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-white bg-nuceraPurple px-6 py-4
                hover:shadow-lg group-hover:shadow-lg
                outline-offset-0
                focus:outline-accentGreen focus:outline-[2px] group-focus:outline-accentGreen group-focus:outline-[2px]
                active:outline-nuceraPurple active:bg-midPurple group-active:outline-accentGreen group-active:bg-midPurple
                disabled:bg-nuceraPurple disabled:opacity-50 disabled:pointer-events-none tracking-[0.025rem];
            }

            .button-medium {
                @apply button text-small leading-small py-3;
            }

            .button-small {
                @apply button text-small leading-small py-2 px-4;
            }

            .button-squared {
                @apply button px-4 justify-center
            }

            .button-medium-squared {
                @apply button-medium px-3 justify-center
            }

            .button-small-squared {
                @apply button-small px-2 justify-center
            }

            .button-white {
                @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-nuceraPurple px-6 py-3.5 border outline-offset-2 border-solid border-nuceraPurple bg-oxygenWhite
                hover:shadow-lg hover:bg-lightPurple-25
                active:bg-lightPurple-50
                focus:outline-accentGreen focus:outline-[2px]
                disabled:border-nuceraPurple
                disabled:text-nuceraPurple disabled:pointer-events-none  disabled:opacity-50 tracking-[0.025rem];
            }

            .button-medium-white {
                @apply button-white text-small leading-small py-2.5;
            }

            .button-small-white {
                @apply button-white text-small leading-small py-1.5 px-4;
            }

            .button-white-squared {
                @apply button-white px-4 justify-center;
            }

            .button-medium-white-squared {
                @apply button-medium-white px-3 justify-center;
            }

            .button-small-white-squared {
                @apply button-small-white px-2 justify-center;
            }

            .button-dark {
                @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-oxygenWhite px-6 py-3.5 border outline-offset-2 border-solid border-oxygenWhite
                hover:shadow-lg hover:bg-whiteOpacity12
                active:bg-whiteOpacity20
                focus:outline-accentGreen focus:outline-[2px]
                disabled:pointer-events-none disabled:opacity-50 tracking-[2.5%];
            }

            .button-medium-dark {
                @apply button-dark text-small leading-small py-2.5;
            }

            .button-small-dark {
                @apply button-dark text-small leading-small py-1.5 px-4;
            }

            .button-dark-squared {
                @apply button-dark px-4 justify-center;
            }

            .button-medium-dark-squared {
                @apply button-medium-dark px-3 justify-center;
            }

            .button-small-dark-squared {
                @apply button-small-dark px-2 justify-center;
            }

            /* only for styleguide */
            .layoutPlaceholder {
                @apply bg-darkmetalGray w-full text-white p-4;
            }

            .colorList {
                @apply flex flex-row gap-6 mb-12 flex-wrap;
            }
            .colorList > div {
                @apply h-24 w-24 rounded-full shadow-lg flex items-center justify-center;
            }

            .highlightHeadline {
                @apply leading-highlighted;
            }

            .highlightHeadline span {
                @apply inline-block px-2 pt-1 lg:pt-2 -mr-4 lg:px-3.5 lg:-mr-7 mb-3;
            }

            h2.highlightHeadline span {
                @apply pt-1 px-1.5 -mr-3 lg:px-2.5 lg:-mr-5 mb-3;
            }

            h3.highlightHeadline span {
                @apply pt-1 px-1 -mr-2 lg:px-2 lg:-mr-4 mb-2;
            }

            .highlightPositive span {
                @apply bg-nuceraPurple text-white;
            }

            .highlightNegative span {
                @apply bg-white text-nuceraPurple;
            }

            .container-dark {
                @apply text-oxygenWhite bg-darkmetalGray-150 py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
            }

            .container-dark-noMarginTop {
                @apply container-dark pt-0;
            }

            .container-white {
                @apply bg-oxygenWhite text-pitchBlack py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
            }

            .container-white-noMarginTop {
                @apply container-white pt-0;
            }

            .container-bgImageLight {
                @apply bg-oxygenWhite text-pitchBlack px-0;
            }

            .container-bgImageDark {
                @apply text-oxygenWhite bg-darkmetalGray-150 px-0;
            }

            .input {
                @apply w-full pt-[11px] pb-[10px] px-4 focus:placeholder-transparent;
            }

            .input-error,
            .input-success {
               @apply valid:border-2 invalid:border-2
            }

            .textInput-standard {
                @apply input border-industryGray focus:border-techGray text-darkmetalGray-150 placeholder:text-urbanGray border border-industryGray;
            }

            .textInput-standard-success {
                @apply textInput-standard input-success valid:border-green-100 valid:bg-green-5 ;
            }

            .textInput-standard-error {
                @apply textInput-standard input-error invalid:border-red-warning invalid:bg-red-5;
            }

            .textInput-ghost {
              @apply input border border-transparent
                focus:border-hydroGray
                bg-darkmetalGray-200 text-hydroGray
                placeholder:text-industryGray
                hover:bg-nuceraPurple
                hover:placeholder-shown:placeholder:text-hydroGray
                focus:bg-darkmetalGray-200
                focus:hover:bg-darkmetalGray-200
                focus:hover:placeholder-transparent;
            }

            .textInput-ghost-success {
                @apply textInput-ghost input-success valid:border-green-100;
            }

            .textInput-ghost-error {
                @apply textInput-ghost input-error invalid:border-red-warning;
            }

            .textInput-outline {
              @apply input bg-transparent border border-hydroGray-50
                  focus:border-hydroGray bg-darkmetalGray-200
                  text-hydroGray placeholder:text-industryGray
                  hover:border-hydroGray hover:placeholder:text-hydroGray
                  focus:hover:placeholder-transparent
                  hover:placeholder-shown:outline hover:placeholder-shown:outline-1 hover:placeholder-shown:outline-white
                  focus:outline-none focus:hover:outline-none;
            }

            .textInput-outline-success {
                @apply textInput-outline input-success valid:border-green-100;
            }

            .textInput-outline-error {
                @apply textInput-outline input-error invalid:border-red-warning;
            }

            .textArea {
                @apply w-full flex justify-start items-start px-4 pt-[11px] pb-[10px];
            }

            .textArea-standard {
                @apply textArea;
            }

            .textArea-standard-success {
                @apply textArea-standard valid:border-green-100;
            }

            .textArea-standard-error {
                @apply textArea-standard invalid:border-red-warning;
            }

           .textArea-ghost {
              @apply textArea bg-darkmetalGray-200 text-hydroGray placeholder:text-industryGray
                hover:bg-nuceraPurple
                hover:placeholder-shown:placeholder:text-hydroGray
                focus:bg-darkmetalGray-200
                focus:hover:bg-darkmetalGray-200
                focus:hover:placeholder-transparent;
            }
            .textArea-ghost-success {
                @apply textArea-ghost valid:border-green-100;
            }

            .textArea-ghost-error {
                @apply textArea-ghost invalid:border-red-warning;
            }
            .textArea-outline {
                @apply textArea bg-transparent border-2 border-transparent focus:border-hydroGray bg-darkmetalGray-200
                text-hydroGray placeholder:text-industryGray hover:border-hydroGray hover:border-2
                hover:placeholder:text-hydroGray focus:hover:placeholder-transparent;
            }
            .textArea-outline-success {
                @apply textArea-outline valid:border-green-100;
            }
            .textArea-outline-error {
                @apply textArea-outline invalid:border-red-warning;
            }
        }

        .forced-screen-width {
             width: 100vw;
             position: relative;
             left: 50%;
             right: 50%;
             margin-left: -50vw;
             margin-right: -50vw;
         }

        .forced-screen-width-unset {
           width: unset;
           position: unset;
           left: unset;
           right: unset;
           margin-left: unset;
           margin-right: unset;

       }
    </style>

    <!-- required for styleguide -->
    <link rel="stylesheet" href="assets/css/prism.css" />
    <link rel="stylesheet" href="assets/css/gridpreview.css" />
</head>
<body style="min-height:120000px">

<!-- styleguide header -->
<header class="text-[#ffffff] py-12 lg:h-[40vh]" style="background: transparent url('assets/images/dummy.jpg') no-repeat center center;background-size: cover;">
    <div class="corporate_grid h-full">
        <div class="col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-8 lg:col-end-11 flex flex-col h-full justify-start">
            <div class="text-7xl mt-12 font-bold font-orbitronBold text-nuceraPurple">Web UI Library</div>
            <div class="text-2xl mt-4 text-nuceraPurple">Digital Corporate Design &ndash; Elemente und Komponenten</div>
        </div>
        <div class="col-start-1 row-start-1 md:col-start-8 lg:col-start-11 col-end-2 md:col-end-9 lg:col-end-13">
            <div class="w-full flex text-nuceraPurple">
                <svg viewBox="0 0 132 132"><use href="assets/images/spritemap.svg#logo" /></svg>
            </div>
        </div>
    </div>
</header>

<!-- styleguide content index -->
<nav class="py-12 bg-darkmetalGray text-[#ffffff]">
    <div class="corporate_grid">
        <div class="corporate_grid_full">
            <div id="contentIndex" class="mt-12 mb-8 flex gap-x-4 flex-wrap"></div>
        </div>
    </div>
</nav>

<!-- styleguide main -->
<main role="main" class="relative pb-8">

    <!-- color overview -->
    <section id="colorSection" class="corporate_section" data-section-info='
    {
        "title": "Colors",
        "description": "Farbsystem",
        "version": "1.0.0",
        "templates": ""
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-8">Purple</div>
                    <div class="colorList">
                        <div class="bg-darkPurple"></div>
                        <div class="bg-midPurple"></div>
                        <div class="bg-nuceraPurple"></div>
                        <div class="bg-brightPurple"></div>
                        <div class="bg-lightPurple"></div>
                    </div>

                    <div class="h3 my-8">Gray</div>
                    <div class="colorList">
                        <div class="bg-darkmetalGray-200"></div>
                        <div class="bg-darkmetalGray-150"></div>
                        <div class="bg-darkmetalGray"></div>
                        <div class="bg-industryGray"></div>
                        <div class="bg-techGray"></div>
                        <div class="bg-urbanGray"></div>
                        <div class="bg-skyGray"></div>
                        <div class="bg-hydroGray"></div>
                        <div class="bg-hydroGray-50"></div>
                    </div>

                    <div class="h3 my-8">Accent</div>
                    <div class="colorList">
                        <div class="bg-accentGreen"></div>
                        <div class="bg-accentBlueLight"></div>
                        <div class="bg-accentPurple"></div>
                    </div>

                    <div class="h3 my-8">Black/White</div>
                    <div class="colorList">
                        <div class="bg-pitchBlack"></div>
                        <div class="bg-oxygenWhite"></div>
                    </div>

                    <div class="h3 my-8">Functionals</div>
                    <div class="colorList">
                        <div class="bg-red-140"></div>
                        <div class="bg-red-120"></div>
                        <div class="bg-red"></div>
                        <div class="bg-green"></div>
                        <div class="bg-green-5"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- logo overview -->
    <section id="logoSection" class="corporate_section" data-section-info='
    {
        "title": "Logo",
        "description": "Logo und seine Größen",
        "version": "1.0.0",
        "templates": ""
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="flex flex-row gap-8 items-center">
                        <div class="w-full text-nuceraPurple flex flex-col gap-4">
                            <svg viewBox="0 0 134 134"><use href="assets/images/spritemap.svg#logo" /></svg>
                            <svg viewBox="0 0 414 77"><use href="assets/images/spritemap.svg#logo-bar" /></svg>
                        </div>
                        <div class="w-full text-darkmetalGray-200 flex flex-col gap-4">
                            <svg viewBox="0 0 134 134"><use href="assets/images/spritemap.svg#logo" /></svg>
                            <svg viewBox="0 0 414 77"><use href="assets/images/spritemap.svg#logo-bar" /></svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- breadcrumb overview -->
    <section id="breadcrumbSection" class="corporate_section" data-section-info='
    {
        "title": "Breadcrumb",
        "description": "...",
        "version": "1.0.0",
        "templates": "_breadcrumb.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="breadcrumb" data-component-title="Breadcrumb"></div>
                </div>
            </div>
            <div class="placeTemplate" data-template="breadcrumb-dark" data-component-title="Breadcrumb Dark"></div>
        </div>
    </section>

    <!-- grid overview -->
    <section id="gridSection" class="corporate_section" data-section-info='
    {
        "title": "Grid",
        "description": "Grid mit 12 Spalten für Desktop, 8 Spalten für Tablets und 4 Spalten im mobile view.",
        "version": "1.0.0",
        "templates": "_grid.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="grid1col" data-component-title="1 Spalte"><h1>hello</h1></div>
            <div class="placeTemplate" data-template="grid2cols" data-component-title="2 Spalten"></div>
            <div class="placeTemplate" data-template="grid2cols_b" data-component-title="2 Spalten, 66/33"></div>
            <div class="placeTemplate" data-template="grid2cols_c" data-component-title="2 Spalten, 33/66"></div>
            <div class="placeTemplate" data-template="grid3cols" data-component-title="3 Spalten"></div>
            <div class="placeTemplate" data-template="grid4cols" data-component-title="4 Spalten"></div>
        </div>
    </section>

    <!-- headlines overview -->
    <section id="headlineSection" class="corporate_section" data-section-info='
    {
        "title": "Headlines",
        "description": "description",
        "version": "1.0.0",
        "templates": "_headlines.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="h1" data-component-title="H1"></div>
                    <div class="placeTemplate" data-template="h1-blue" data-component-title="H1 Blue Text"></div>
                    <div class="placeTemplate" data-template="h1-blue-bg" data-component-title="H1 Blue Background"></div>
                    <div class="placeTemplate" data-template="h1-white-bg" data-component-title="H1 White Background"></div>

                    <div class="placeTemplate" data-template="h2" data-component-title="H2"></div>
                    <div class="placeTemplate" data-template="h2-blue" data-component-title="H2 Blue Text"></div>
                    <div class="placeTemplate" data-template="h2-blue-bg" data-component-title="H2 Blue Background"></div>
                    <div class="placeTemplate" data-template="h2-white-bg" data-component-title="H2 White Background"></div>

                    <div class="placeTemplate" data-template="h3" data-component-title="H3"></div>

                    <div class="placeTemplate" data-template="subtitle" data-component-title="Subtitle"></div>
                    <div class="placeTemplate" data-template="subtitle-bold" data-component-title="Subtitle Bold"></div>
                    <div class="placeTemplate" data-template="subtitle-black" data-component-title="Subtitle Black"></div>

                </div>
            </div>
        </div>
    </section>

    <!-- paragraph overview -->
    <section id="paragrahpSection" class="corporate_section" data-section-info='
    {
        "title": "Paragraphs",
        "description": "description",
        "version": "1.0.0",
        "templates": "_paragraphs.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="p-big" data-component-title="Paragraph (big)"></div>
                    <div class="placeTemplate" data-template="p" data-component-title="Paragraph (normal)"></div>
                    <div class="placeTemplate" data-template="p-small" data-component-title="Paragraph (small)"></div>
                    <div class="placeTemplate" data-template="p-extrasmall" data-component-title="Paragraph (extrasmall)"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- buttons overview -->
    <section id="buttonsSection" class="corporate_section" data-section-info='
    {
        "title": "Buttons",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_buttons.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Large</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                </div>
            </div>

            <div class="corporate_grid bg-darkmetalGray-150 text-oxygenWhite">
                <div class="corporate_grid_full py-6">
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-dark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                </div>
            </div>

            <div class="corporate_grid bg-darkmetalGray-150 text-oxygenWhite" style="background-image: url('assets/images/dummy2.png');background-repeat: no-repeat;background-size: cover;background-position: top center">
                <div class="corporate_grid_full py-6">
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-large-dark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-large-dark-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                </div>
            </div>

            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="corporate_grid_full py-6">
                        <div class="flex flex-row gap-8 flex-wrap mb-8">
                            <div class="placeTemplate" data-template="button-large-solid-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-solid-checkmark-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-solid-arrow-right-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                        </div>
                        <div class="flex flex-row gap-8 flex-wrap mb-8">
                            <div class="placeTemplate" data-template="button-large-white-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-white-checkmark-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-white-arrow-right-disabled" data-component-title="Button"></div>
                            <div class="placeTemplate" data-template="button-large-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Medium</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-solid-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-medium-white-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-medium-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                </div>
            </div>
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Buttons Small</div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-solid" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-solid-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-solid-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-white" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-checkmark" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-arrow-right" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-circle-arrow-right" data-component-title="Button"></div>
                    </div>
                    <div class="flex flex-row gap-8 flex-wrap mb-8">
                        <div class="placeTemplate" data-template="button-small-white-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-checkmark-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-arrow-right-disabled" data-component-title="Button"></div>
                        <div class="placeTemplate" data-template="button-small-white-circle-arrow-right-disabled" data-component-title="Button"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- images overview -->
    <section id="imageSection" class="corporate_section" data-section-info='
    {
        "title": "Images",
        "description": "description",
        "version": "1.0.0",
        "templates": "_images.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_halfLeft">
                    <div class="placeTemplate" data-template="image" data-component-title="Image (original ratio)"></div>
                    <div class="placeTemplate" data-template="image-1x1" data-component-title="Image (1x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-4x3" data-component-title="Image (4x3 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x2" data-component-title="Image (3x2 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x1" data-component-title="Image (3x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-16x10" data-component-title="Image (16x10 ratio)"></div>
                    <div class="placeTemplate" data-template="image-16x9" data-component-title="Image (16x9 ratio)"></div>
                </div>
                <div class="corporate_grid_halfRight">
                    <div class="placeTemplate" data-template="image-2x1" data-component-title="Image (2x1 ratio)"></div>
                    <div class="placeTemplate" data-template="image-5x2" data-component-title="Image (5x2 ratio)"></div>
                    <div class="placeTemplate" data-template="image-golden" data-component-title="Image (golden ratio)"></div>
                    <div class="placeTemplate" data-template="image-2x3" data-component-title="Image (2x3 ratio)"></div>
                    <div class="placeTemplate" data-template="image-3x4" data-component-title="Image (3x4 ratio)"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- paragraph overview -->
    <section id="heroSection" class="corporate_section" data-section-info='
    {
        "title": "Hero",
        "description": "description",
        "version": "1.0.0",
        "templates": "_hero.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="hero" data-component-title="Hero"></div>
            <div class="placeTemplate" data-template="hero-search" data-component-title="Hero Search"></div>
        </div>
    </section>

    <!-- accordeon overview -->
    <section id="accordeonSection" class="corporate_section" data-section-info='
    {
        "title": "Accordeon",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_accordeon.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="accordeon" data-component-title="Accordeon"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- accordeon overview -->
    <section id="alertboxSection" class="corporate_section" data-section-info='
    {
        "title": "Hinweis-Kästen",
        "status": "final",
        "description": "description",
        "version": "1.0.0",
        "templates": "_alerts.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="success-note" data-component-title="Hinweis-Kasten"></div>
                    <div class="placeTemplate" data-template="success-note-2" data-component-title="Hinweis-Kasten"></div>
                    <div class="placeTemplate" data-template="error-note" data-component-title="Hinweis-Kasten"></div>
                </div>
            </div>
        </div>
    </section>




    <!-- TODO: final markup for the sections below -->

    <!-- cards overview -->
    <section id="cardsSection" class="corporate_section" data-section-info='
    {
        "title": "Cards",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_cards.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="placeTemplate" data-template="simpleCard" data-component-title="Card"></div>
                </div>
            </div>
            <div class="placeTemplate" data-template="cardTeaserRow" data-component-title="Card Teaser Reihe"></div>
        </div>
    </section>

    <!-- layouts overview -->
    <section id="layoutSection" class="corporate_section" data-section-info='
    {
        "title": "Layouts",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_layouts.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="layout-1" data-component-title="Layout 1"></div>
            <div class="placeTemplate" data-template="layout-2" data-component-title="Layout 2"></div>
            <div class="placeTemplate" data-template="layout-3" data-component-title="Layout 3"></div>
            <div class="placeTemplate" data-template="layout-4" data-component-title="Layout 4"></div>
            <div class="placeTemplate" data-template="layout-5" data-component-title="Layout 5"></div>
        </div>
    </section>

    <!-- container overview -->
    <section id="containerSection" class="corporate_section" data-section-info='
    {
        "title": "Container",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_container.html"
    }'>
        <div class="py-16">
            <div class="placeTemplate" data-template="container-white" data-component-title="Container 1"></div>
            <div class="placeTemplate" data-template="container-white-noMarginTop" data-component-title="Container 1"></div>
            <div class="placeTemplate" data-template="container-bgImageLight" data-component-title="Container 2"></div>
            <div class="placeTemplate" data-template="container-bgImageDark" data-component-title="Container 2"></div>
            <div class="placeTemplate" data-template="container-dark" data-component-title="Container 3"></div>
            <div class="placeTemplate" data-template="container-dark-noMarginTop" data-component-title="Container 4"></div>
        </div>
    </section>

    <!-- Forms overview -->
    <section id="formsSection" class="corporate_section" data-section-info='
    {
        "title": "Forms",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_forms.html"
    }'>
        <div class="py-16">
          <div class="corporate_grid">
             <div class="corporate_grid_full">
              <div class="h3 my-16">Checkbox Light</div>
              <div class="mb-8 p-4 text-hydroGray-50">
                <div class="placeTemplate" data-template="checkbox-light" data-component-title="Checkbox Light"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Checkbox Dark</div>
              <div class="mb-8 p-4 text-hydroGray-50 bg-darkmetalGray-150 dark">
                <div class="placeTemplate" data-template="checkbox-dark" data-component-title="Checkbox Dark"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Dropdown standard</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 text-techGray">
                <div class="placeTemplate" data-template="dropdown-standard" data-component-title="Dropdown Standard"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Dropdown ghost</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 bg-darkmetalGray-150 text-skyGray h-[300px]">
                <div class="placeTemplate" data-template="dropdown-ghost" data-component-title="Dropdown Ghost"></div>
              </div>
            </div>
              <div class="corporate_grid_full">
              <div class="h3 my-16">Dropdown outline</div>
              <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50 h-[400px]" style="background-image: url('assets/images/dummy2.png');background-repeat: no-repeat;background-size: cover;background-position: top center">
                  <div class=" p-4 rounded-lg flex flex-row gap-8 flex-wrap h-full" style="width:100%;background: rgba(255, 255, 255, .2);
                  -webkit-backdrop-filter: blur(4px);
                  backdrop-filter: blur(4px);">
                    <div class="placeTemplate" data-template="dropdown-outline" data-component-title="Dropdown Outline"></div>
                  </div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Input standard</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 text-techGray">
                <div class="placeTemplate" data-template="textInput-standard" data-component-title="Text Input Standard"></div>
                <div class="placeTemplate" data-template="textInput-standard-success" data-component-title="Text Input Standard"></div>
                <div class="placeTemplate" data-template="textInput-standard-error" data-component-title="Text Input Standard"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Input ghost</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 bg-darkmetalGray-150 text-skyGray">
                <div class="placeTemplate" data-template="textInput-ghost" data-component-title="Text Input Ghost"></div>
                <div class="placeTemplate" data-template="textInput-ghost-success" data-component-title="Text Input Ghost"></div>
                <div class="placeTemplate" data-template="textInput-ghost-error" data-component-title="Text Input Ghost"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Input outline</div>
              <div class="p-4 bg-darkmetalGray-150 text-hydroGray-50" style="background-image: url('assets/images/dummy2.png');background-repeat: no-repeat;background-size: cover;background-position: top center">
                  <div class="bg-white bg-opacity-10 p-4 rounded-lg flex flex-row gap-8 flex-wrap" style="width:100% ;background: rgba(255, 255, 255, .2);
                  -webkit-backdrop-filter: blur(4px);
                  backdrop-filter: blur(4px);">
                    <div class="placeTemplate" data-template="textInput-outline" data-component-title="Text Input Outline"></div>
                    <div class="placeTemplate" data-template="textInput-outline-success" data-component-title="Text Input Outline"></div>
                    <div class="placeTemplate" data-template="textInput-outline-error" data-component-title="Text Input Outline"></div>
                  </div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Area Standard</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 text-techGray">
                <div class="placeTemplate" data-template="textArea-standard" data-component-title="Text Area Standard"></div>
                <div class="placeTemplate" data-template="textArea-standard-success" data-component-title="Text Area Standard"></div>
                <div class="placeTemplate" data-template="textArea-standard-error" data-component-title="Text Area Standard"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Area Ghost</div>
              <div class="flex flex-row gap-8 flex-wrap mb-8 p-4 bg-darkmetalGray-150 text-skyGray">
                <div class="placeTemplate" data-template="textArea-ghost" data-component-title="Text Area Ghost"></div>
                <div class="placeTemplate" data-template="textArea-ghost-success" data-component-title="Text Area Ghost"></div>
                <div class="placeTemplate" data-template="textArea-ghost-error" data-component-title="Text Area Ghost"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Text Area Outline</div>
              <div class="mb-8 p-4 bg-darkmetalGray-150 text-hydroGray-50" style="background-image: url('assets/images/dummy2.png');background-repeat: no-repeat;background-size: cover;background-position: top center">
                  <div class="bg-white bg-opacity-10 p-4 rounded-lg flex flex-row gap-8 flex-wrap" style="width:100% ;background: rgba(255, 255, 255, .2);
                  -webkit-backdrop-filter: blur(4px);
                  backdrop-filter: blur(4px);">
                    <div class="placeTemplate" data-template="textArea-outline" data-component-title="Text Area Outline"></div>
                    <div class="placeTemplate" data-template="textArea-outline-success" data-component-title="Text Area Outline"></div>
                    <div class="placeTemplate" data-template="textArea-outline-error" data-component-title="Text Area Outline"></div>
                  </div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Radio Buttons Light</div>
              <div class="mb-8 p-4 text-hydroGray-50">
                <div class="placeTemplate" data-template="radioButtons-light" data-component-title="Radio Buttons Light"></div>
              </div>
            </div>
            <div class="corporate_grid_full">
              <div class="h3 my-16">Radio Buttons Dark</div>
              <div class="mb-8 p-4 text-hydroGray-50 bg-darkmetalGray-150">
                <div class="placeTemplate" data-template="radioButtons-dark" data-component-title="Radio Buttons Dark"></div>
              </div>
            </div>

          </div>
        </div>
    </section>

     <!-- Table overview -->
    <section id="tableSection" class="corporate_section" data-section-info='
    {
        "title": "Table",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_table.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Table</div>
                    <div class="w-full mb-8">
                        <div class="placeTemplate" data-template="table" data-component-title="Table"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <!-- Labels overview -->
    <section id="labelsSection" class="corporate_section" data-section-info='
    {
        "title": "Labels",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_labels.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Labels light</div>
                    <div class="w-full mb-8">
                      <div class="placeTemplate" data-template="labels" data-component-title="Labels"></div>
                      <div class="placeTemplate" data-template="labels-with-icon" data-component-title="Labels"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Labels dark</div>
                    <div class="w-full mb-8 bg-darkmetalGray-150 p-4">
                      <div class="placeTemplate" data-template="labels-dark" data-component-title="Labels"></div>
                      <div class="placeTemplate" data-template="labels-dark-with-icon" data-component-title="Labels"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <!-- Links overview -->
    <section id="linksSection" class="corporate_section" data-section-info='
    {
        "title": "Links",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_links.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Links light</div>
                    <div class="w-full mb-8">
                      <div class="placeTemplate" data-template="link" data-component-title="Links"></div>
                      <div class="placeTemplate" data-template="linked-object" data-component-title="Links"></div>
                    </div>
                </div>
                <div class="corporate_grid_full">
                    <div class="h3 my-16 ">Links dark</div>
                    <div class="w-full mb-8 p-4 bg-darkmetalGray-150 ">
                      <div class="placeTemplate" data-template="link-dark" data-component-title="Links"></div>
                      <div class="placeTemplate" data-template="linked-object-dark" data-component-title="Links"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <!-- Tabs overview -->
    <section id="tabsSection" class="corporate_section" data-section-info='
    {
        "title": "Tabs",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_tabs.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Tabs</div>
                    <div class="w-full mb-8 bg-darkmetalGray-150 p-4">
                      <div class="placeTemplate" data-template="tabs" data-component-title="Tabs"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <!-- Tooltips overview -->
    <section id="tooltpisSection" class="corporate_section" data-section-info='
    {
        "title": "Tooltips",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_tooltips.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Tooltips</div>
                    <div class="w-full mb-8 ">
                      <div class="placeTemplate" data-template="tooltips-purple" data-component-title="Tooltips"></div>
                      <div class="placeTemplate" data-template="tooltips-dark" data-component-title="Tooltips"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <!-- Tags overview -->
    <section id="tagsSection" class="corporate_section" data-section-info='
    {
        "title": "Tags",
        "status": "draft",
        "description": "description",
        "version": "1.0.0",
        "templates": "_tags.html"
    }'>
        <div class="py-16">
            <div class="corporate_grid">
                <div class="corporate_grid_full">
                    <div class="h3 my-16">Tags</div>
                    <div class="w-full mb-8 ">
                      <div class="placeTemplate" data-template="tags" data-component-title="Tags"></div>
                      <div class="placeTemplate" data-template="content-hint" data-component-title="Content Hint"></div>
                    </div>
                </div>
              </div>
            </div>
        </div>
    </section>

    <div id="pageOverlay" class="hidden absolute left-0 top-0 w-full h-full bg-[rgba(255,255,255,0.9)] z-10"></div>
</main>

<script src="assets/js/focuspoint.js"></script>
<script src="assets/js/functions.js"></script>

<!-- styleguide footer -->
<footer class="bg-[#333333] text-[#ffffff] min-h-[400px] py-12">
    <div class="corporate_grid">
        <div class="corporate_grid_full">
            footer
        </div>
    </div>
</footer>

<div id="codeViewer" class="hidden bg-nuceraPurple pt-12 fixed w-full h-[50%] bottom-0 z-[200]"></div>
<div id="gridPreview"><div class="previewRow"><div class="previewCol previewCol-1"></div><div class="previewCol previewCol-2"></div><div class="previewCol previewCol-3"></div><div class="previewCol previewCol-4"></div><div class="previewCol previewCol-5"></div><div class="previewCol previewCol-6"></div><div class="previewCol previewCol-7"></div><div class="previewCol previewCol-8"></div><div class="previewCol previewCol-9"></div><div class="previewCol previewCol-10"></div><div class="previewCol previewCol-11"></div><div class="previewCol previewCol-12"></div></div><div id="middleAxis"></div></div>


<!-- styleguide templates starts -->
<template id="sectionHeader"
><div class="sticky top-0 z-[9] w-full bg-primaryPurpleOpacity text-white py-8">
    <div class="corporate_grid">
        <div class="corporate_grid_full flex gap-4 items-center">
            <div class="text-[24px] px-3 py-1 bg-white inline-block -ml-3 text-nuceraPurple border border-nuceraPurple">
                <strong>###title###</strong>
                <span class="pl-2 text-[14px] text-[#555555]">###version###</span>
            </div>
            <div class="text-[20px]">###description###</div>
        </div>
    </div>
</div></template>
<!-- styleguide templates end -->
<div class="hidden">
    <svg fill="none" xmlns="http://www.w3.org/2000/svg">
        <symbol id="code" viewBox="0 0 15 15"><path d="M10.1464 10.1464L9.79289 10.5L10.5 11.2071L10.8536 10.8536L10.1464 10.1464ZM13.5 7.5L13.8536 7.85355L14.2071 7.5L13.8536 7.14645L13.5 7.5ZM10.8536 4.14645L10.5 3.79289L9.79289 4.5L10.1464 4.85355L10.8536 4.14645ZM4.14645 10.8536L4.5 11.2071L5.20711 10.5L4.85355 10.1464L4.14645 10.8536ZM1.5 7.5L1.14645 7.14645L0.792893 7.5L1.14645 7.85355L1.5 7.5ZM4.85355 4.85355L5.20711 4.5L4.5 3.79289L4.14645 4.14645L4.85355 4.85355ZM10.8536 10.8536L13.8536 7.85355L13.1464 7.14645L10.1464 10.1464L10.8536 10.8536ZM13.8536 7.14645L10.8536 4.14645L10.1464 4.85355L13.1464 7.85355L13.8536 7.14645ZM4.85355 10.1464L1.85355 7.14645L1.14645 7.85355L4.14645 10.8536L4.85355 10.1464ZM1.85355 7.85355L4.85355 4.85355L4.14645 4.14645L1.14645 7.14645L1.85355 7.85355ZM8.0068 1.4178L6.0068 13.4178L6.9932 13.5822L8.9932 1.5822L8.0068 1.4178Z" fill="#000000"/></symbol>
    </svg>
</div>
<script src="assets/js/prism.js"></script>
<script src="assets/js/app.js"></script>

</body>
</html>
