<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Forms elements</title>
</head>
<body>

<!-- TEXT INPUTS BEGINN -->
<template id="textInput-standard">
<label for="textInput-standard-id" class="group relative text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <input id="textInput-standard-id" type="text" class="peer pl-12 pr-14 textInput-standard" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-standard-success">
<label for="textInput-standard-id-success" class="group relative text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <input id="textInput-standard-id-success" type="text" class="peer pl-12 pr-14 textInput-standard textInput-standard-success" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250 peer-valid:text-green-100 peer-invalid:text-red-warning">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-standard-error">
<label for="textInput-standard-id-error" class="group relative text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <input id="textInput-standard-id-error" type="text" required class="peer pl-12 pr-14 textInput-standard textInput-standard-error" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250 peer-valid:text-green-100 peer-invalid:text-red-warning">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-ghost">
<label for="textInput-ghost-id" class="group relative text-small inline-flex flex-col gap-1">
    Topline
    <input id="textInput-ghost-id" type="text" class="peer pl-12 pr-14 textInput-ghost" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-ghost-success">
<label for="textInput-ghost-id-success" class="group relative text-small inline-flex flex-col gap-1">
    Topline
    <input id="textInput-ghost-id-success" type="text" class="peer pl-12 pr-14 textInput-ghost textInput-ghost-success" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-ghost-error">
<label for="textInput-ghost-id-error" class="group relative text-small inline-flex flex-col gap-1">
    Topline
    <input id="textInput-ghost-id-error" type="text" required class="peer pl-12 pr-14 textInput-ghost textInput-ghost-error" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-outline">
<label for="textInput-outline-id" class="group relative text-small inline-flex flex-col gap-1">
    Topline
    <input id="textInput-outline-id" type="text" class="peer pl-12 pr-14 textInput-outline" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-outline-success">
<label for="textInput-outline-id-success" class="group relative text-small inline-flex flex-col gap-1 flex flex-col gap-1">
    Topline
    <input id="textInput-outline-id-success" type="text" class="peer pl-12 pr-14 textInput-outline textInput-outline-success" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>

<template id="textInput-outline-error">
<label for="textInput-outline-id-error" class="group relative text-small inline-flex flex-col gap-1">
    Topline
    <input id="textInput-outline-id-error" type="text" required class="peer pl-12 pr-14 textInput-outline textInput-outline-error" placeholder="Lorem Ipsum">
    <svg aria-hidden="true" class="absolute left-4 top-1/2 -translate-y-1/2 size-6"><use href="assets/images/spritemap.svg#search-icon"/></svg>
    <button
        type="button"
        aria-label="Clear input value"
        class="absolute right-8 top-1/2 -translate-y-1/2 block peer-placeholder-shown:hidden"
    >
        <svg aria-hidden="true" class="size-6"><use href="assets/images/spritemap.svg#clear-icon"/></svg>
    </button>
    <p class="text-extrasmall">
        Form Helper
    </p>
</label>
</template>
<!-- TEXT INPUTS END -->

<!-- TEXT AREA BEGINN -->
<template id="textArea-standard">
<label for="textArea-standard-id" class="group text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <textarea id="textArea-standard-id"  class="peer textArea-standard" rows="5" placeholder="Lorem Ipsum"></textarea>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250">
        Form Helper
    </p>
</label>
</template>

<template id="textArea-standard-success">
<label for="textArea-standard-id-success" class="group text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <textarea id="textArea-standard-id-success" rows="5"  class="peer textArea-standard textArea-standard-success" placeholder="Lorem Ipsum"></textarea>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250 peer-valid:text-green-100 peer-invalid:text-red-warning">
        Form Helper
    </p>
</label>
</template>

<template id="textArea-standard-error">
<label for="textArea-standard-id-error" class="group text-small inline-flex flex-col gap-1">
    <span class="group-hover:text-darkmetalGray-250">Topline</span>
    <textarea id="textArea-standard-id-error" rows="5"  required class="peer textArea-standard textArea-standard-error" placeholder="Lorem Ipsum"></textarea>
    <p class="text-extrasmall group-hover:text-darkmetalGray-250 peer-valid:text-green-100 peer-invalid:text-red-warning">
        Form Helper
    </p>
</label>
</template>

<template id="textArea-ghost">
    <label for="textArea-ghost-id" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-ghost-id" rows="5"  class="textArea-ghost" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>

<template id="textArea-ghost-success">
    <label for="textArea-ghost-id-success" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-ghost-id-success" rows="5"  class="textArea-ghost textArea-ghost-success" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>

<template id="textArea-ghost-error">
    <label for="textArea-ghost-id-error" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-ghost-id-error" rows="5"  required class="textArea-ghost textArea-ghost-error" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>

<template id="textArea-outline">
    <label for="textArea-outline-id" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-outline-id" rows="5"  class="textArea-outline" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>

<template id="textArea-outline-success">
    <label for="textArea-outline-id-success" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-outline-id-success" rows="5"  class="textArea-outline textArea-outline-success" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>

<template id="textArea-outline-error">
    <label for="textArea-outline-id-error" class="group text-small inline-flex flex-col gap-1">
        Topline
        <textarea id="textArea-outline-id-error" rows="5"  required class="textArea-outline textArea-outline-error" placeholder="Lorem Ipsum"></textarea>
        <p class="text-extrasmall">
            Form Helper
        </p>
    </label>
</template>
<!-- TEXT AREA END -->

<!-- RADIO BUTTONS BEGINNG -->
<template id="radioButtons-light">
    <div class="inline-flex items-center">
      <label class="group relative flex items-center cursor-pointer" for="radioButton-id-default">
        <input
          name="radioButtonGroupName"
          type="radio"
          class="peer cursor-pointer appearance-none border border-urbanGray
                checked:bg-transparent checked:border-nuceraPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-lightPurple checked:group-hover:bg-lightPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default"
        >
        <span aria-hidden="true" class="absolute bg-nuceraPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default">Radio Button 1</label>
    </div>

    <div class="inline-flex items-center">
      <label class="group relative flex items-center cursor-pointer" for="radioButton-id-default-2">
        <input
          name="radioButtonGroupName"
          type="radio"
          class="peer cursor-pointer appearance-none border border-urbanGray
                checked:bg-transparent checked:border-nuceraPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-lightPurple checked:group-hover:bg-lightPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default-2"
        >
        <span aria-hidden="true" class="absolute bg-nuceraPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default-2">Radio Button 1</label>
    </div>

    <div class="inline-flex items-center">
      <label class="group relative flex items-center cursor-pointer" for="radioButton-id-default-disabled">
        <input
          name="radioButtonGroupName"
          type="radio"
          disabled
          class="peer cursor-pointer appearance-none border border-urbanGray
                checked:bg-transparent checked:border-nuceraPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-lightPurple checked:group-hover:bg-lightPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default-disabled"
        >
        <span aria-hidden="true" class="absolute bg-nuceraPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default-disabled">Radio Button 1</label>
    </div>
</template>

<template id="radioButtons-dark">
    <div class="group inline-flex items-center">
      <label class="relative flex items-center cursor-pointer rounded-full" for="radioButton-id-default-dark">
        <input
          name="radioButtonGroupNameDark"
          type="radio"
          class="peer cursor-pointer appearance-none border border-skyGray
                checked:bg-transparent checked:border-accentPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-nuceraPurple checked:group-hover:bg-nuceraPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default-dark"
        >
        <span aria-hidden="true" class="absolute bg-accentPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default-dark">Radio Button 1</label>
    </div>

     <div class="group inline-flex items-center">
      <label class="relative flex items-center cursor-pointer rounded-full" for="radioButton-id-default-dark-2">
        <input
          name="radioButtonGroupNameDark"
          type="radio"
          class="peer cursor-pointer appearance-none border border-skyGray
                checked:bg-transparent checked:border-accentPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-nuceraPurple checked:group-hover:bg-nuceraPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default-dark-2"
        >
        <span aria-hidden="true" class="absolute bg-accentPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default-dark-2">Radio Button 2</label>
    </div>

    <div class="group inline-flex items-center">
      <label class="relative flex items-center cursor-pointer rounded-full" for="radioButton-id-default-dark-disabled">
        <input
          name="radioButtonGroupNameDark"
          type="radio"
          disabled
          class="peer cursor-pointer appearance-none border border-skyGray
                checked:bg-transparent checked:border-accentPurple
                group-hover:bg-techGray
                group-hover:ring-4 group-hover:ring-techGray
                checked:group-hover:ring-nuceraPurple checked:group-hover:bg-nuceraPurple
                transition-all disabled:opacity-50"
          id="radioButton-id-default-dark-disabled"
        >
        <span aria-hidden="true" class="absolute bg-accentPurple w-2 h-2 rounded-full opacity-0 peer-checked:opacity-100 transition-opacity duration-200 top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
        </span>
      </label>
      <label class="sr-only" for="radioButton-id-default-dark-disabled">Radio Button 1</label>
    </div>
</template>
<!-- RADIO BUTTONS END -->

<!-- CHECKBOX BEGINNG -->
<template id="checkbox-light">
<label class="relative cursor-pointer group inline-block w-8 h-8">
  <input type="checkbox" id="checkbox-id-light" class="absolute w-0 h-0 opacity-0 peer" />

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-0 w-8 h-8 rounded-full opacity-0
               bg-hydroGray group-hover:opacity-100
               group-has-[:checked]:bg-lightPurple
               pointer-events-none transition-opacity duration-200"></span>

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-10 w-5 h-5 rounded-md border border-techGray flex items-center justify-center
               group-has-[:checked]:bg-nuceraPurple group-has-[:checked]:border-nuceraPurple
               peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0 peer-focus:outline-2 peer-focus:outline-accentGreen">
    <svg class="w-3 h-3 text-white hidden group-has-[:checked]:block">
      <use href="assets/images/spritemap.svg#checkmark" />
    </svg>
  </span>

  <span class="sr-only">Lorem ipsum</span>
</label>
<label class="relative cursor-pointer group inline-block w-8 h-8">
  <input type="checkbox" disabled id="checkbox-id-light-disabled" class="absolute w-0 h-0 opacity-0 peer" />

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-0 w-8 h-8 rounded-full opacity-0
               bg-hydroGray group-hover:opacity-100
               group-has-[:checked]:bg-lightPurple
               pointer-events-none transition-opacity duration-200"></span>

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-10 w-5 h-5 rounded-md border border-techGray flex items-center justify-center
               group-has-[:checked]:bg-nuceraPurple group-has-[:checked]:border-nuceraPurple group-has-[:disabled]:opacity-50
               peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0 peer-focus:outline-2 peer-focus:outline-accentGreen">
    <svg class="w-3 h-3 text-white hidden group-has-[:checked]:block">
      <use href="assets/images/spritemap.svg#checkmark" />
    </svg>
  </span>

  <span class="sr-only">Lorem ipsum</span>
</label>

</template>

<template id="checkbox-dark">
<label class="relative cursor-pointer group inline-block w-8 h-8">
  <input type="checkbox" id="checkbox-id-dark" class="absolute w-0 h-0 opacity-0 peer" />

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-0 w-8 h-8 rounded-full opacity-0
               bg-hydroGray group-hover:opacity-100
               group-has-[:checked]:bg-lightPurple
               pointer-events-none transition-opacity duration-200"></span>

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-10 w-5 h-5 rounded-md border border-techGray flex items-center justify-center
               group-has-[:checked]:bg-nuceraPurple group-has-[:checked]:border-nuceraPurple
               peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0 peer-focus:outline-2 peer-focus:outline-accentGreen">
    <svg class="w-3 h-3 text-white hidden group-has-[:checked]:block">
      <use href="assets/images/spritemap.svg#checkmark" />
    </svg>
  </span>

  <span class="sr-only">Lorem ipsum</span>
</label>

<label class="relative cursor-pointer group inline-block w-8 h-8">
  <input type="checkbox" id="checkbox-id-dark-disabled" class="absolute w-0 h-0 opacity-0 peer" />

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-0 w-8 h-8 rounded-full opacity-0
               bg-hydroGray group-hover:opacity-100
               group-has-[:checked]:bg-lightPurple
               pointer-events-none transition-opacity duration-200"></span>

  <span class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2
               z-10 w-5 h-5 rounded-md border border-techGray flex items-center justify-center
               group-has-[:checked]:bg-nuceraPurple group-has-[:checked]:border-nuceraPurple group-has-[:disabled]:opacity-50
               peer-focus:opacity-100 peer-focus:outline peer-focus:outline-offset-0 peer-focus:outline-2 peer-focus:outline-accentGreen">
    <svg class="w-3 h-3 text-white hidden group-has-[:checked]:block">
      <use href="assets/images/spritemap.svg#checkmark" />
    </svg>
  </span>

  <span class="sr-only">Lorem ipsum</span>
</label>
</template>
<!-- CHECKBOX END -->

<!-- DROPDOWN BEGINN -->
<template id="dropdown-standard">
  <div id="dropdown" class="relative text-darkmetalGray-250 w-48">
    <span class="text-darkmetalGray-250">Topline</span>
    <button
      id="dropdownButton"
      class="relative w-full bg-white inline-flex text-darkmetalGray-250 pt-[11px] pb-[10px] items-center border border-industryGray pl-10 pr-12"
      type="button"
      aria-expanded="false"
    >
      <svg class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-techGray" aria-hidden="true"><use href="assets/images/spritemap.svg#search-icon" /></svg>
      <span id="dropdownButton-innerHTML">Lorem Ipsum</span>
      <svg id="dropdownChevron" class="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-4 text-techGray transition-rotate duration-200" aria-hidden="true"><use href="assets/images/spritemap.svg#chevron-down" /></svg>
    </button>

    <div id="dropdownList" class="absolute w-full z-10 hidden bg-white border-b border-l border-r border-industryGray">
        <ul aria-labelledby="dropdownButton">
          <li class="hover:bg-lightPurple-25 block px-4 py-2">
            Dashboard
          </li>
          <li class="hover:bg-lightPurple-25 block px-4 py-2">
            Settings
          </li>
          <li class="hover:bg-lightPurple-25 block px-4 py-2">
            Earnings
          </li>
          <li class="hover:bg-lightPurple-25 block px-4 py-2">
            Sign out
          </li>
        </ul>
    </div>
  </div>
</template>

<template id="dropdown-ghost">
  <div id="dropdown" class="relative text-hydroGray w-48">
    <span class="text-hydroGray">Topline</span>
    <button
      id="dropdownButton"
      class="relative w-full inline-flex pt-[11px] pb-[10px] items-center pl-10 pr-12 bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple"
      type="button"
      aria-expanded="false"
    >
      <svg class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-hydroGray" aria-hidden="true"><use href="assets/images/spritemap.svg#search-icon" /></svg>
      <span id="dropdownButton-innerHTML">Lorem Ipsum</span>
      <svg class="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-4 text-hydroGray transition-rotate duration-200" id="dropdownChevron" aria-hidden="true"><use href="assets/images/spritemap.svg#chevron-down" /></svg>
    </button>

    <div id="dropdownList" class="absolute w-full z-10 hidden">
        <ul aria-labelledby="dropdownButton">
          <li class="bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
           Dashboard
          </li>
          <li class="bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Settings
          </li>
          <li class="bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Earnings
          </li>
          <li class="bg-darkmetalGray-200 text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Sign out
          </li>
        </ul>
    </div>
  </div>
</template>

<template id="dropdown-outline">
  <div id="dropdown" class="relative text-hydroGray w-48">
    <span class="text-hydroGray">Topline</span>
    <button
      id="dropdownButton"
      class="relative w-full inline-flex pt-[11px] pb-[10px] items-center pl-10 pr-12
      bg-transparent border text-hydroGray border-hydroGray outline outline-0 hover:outline-1 hover:outline-white"
      type="button"
      aria-expanded="false"
    >
      <svg class="absolute left-4 top-1/2 -translate-y-1/2 w-5 h-5 text-hydroGray" aria-hidden="true"><use href="assets/images/spritemap.svg#search-icon" /></svg>
      <span id="dropdownButton-innerHTML">Lorem Ipsum</span>
      <svg class="absolute right-4 top-1/2 -translate-y-1/2 w-5 h-4 text-hydroGray transition-rotate duration-200" id="dropdownChevron" aria-hidden="true"><use href="assets/images/spritemap.svg#chevron-down" /></svg>
    </button>

    <div id="dropdownList" class="absolute w-full z-10 hidden border-b border-l border-r border-hydroGray">
        <ul aria-labelledby="dropdownButton">
          <li class="text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
           Dashboard
          </li>
          <li class="text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Settings
          </li>
          <li class="text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Earnings
          </li>
          <li class="text-hydroGray hover:bg-nuceraPurple block px-4 py-2">
            Sign out
          </li>
        </ul>
    </div>
  </div>
</template>
<!-- DROPDOWN END -->

</body>
</html>
