import Alpine from "alpinejs";

import dropdown from "./Alpine/Components/dropdown";
import tabs from "./Alpine/Components/tabs";
import radio from "./Alpine/Components/radio";
import checkbox from "./Alpine/Components/checkbox";
window.Alpine = Alpine;

// Alpine scripts start here ...
Alpine.data('dropdown', dropdown);
Alpine.data('tabs', tabs);
Alpine.data('radio', radio);
Alpine.data('checkbox', checkbox);

Alpine.start();

// simple plain Javascript, without any dependencies

document.addEventListener("DOMContentLoaded", (event) => {
    // init images with vanillaFocus
    let myImages = new vanillafocus({
        selector: ".vf-container",
        reCalcOnWindowResize: true
    });


    const accordeon = (element) => {
        let config = {
            context: '.accordeon',
            head: '.accordeonHead',
            body: '.accordeonBody'
        };
        let fn = {
            showContent: (target) => {
                target.classList.add('activeContent');
                target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
                let timer = window.setTimeout(() => {
                    target.style.maxHeight = '';
                    window.clearTimeout(timer);
                }, 300);
            },
            hideContent: (target) => {
                target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
                let timer = window.setTimeout(() => {
                    target.classList.remove('activeContent');
                    target.style.maxHeight = '0px';
                    window.clearTimeout(timer);
                }, 10);
            },
            saveCurrentHeight: (target) => {
                target.setAttribute('originalHeight', target.offsetHeight);
            }
        }
        if (element) {
            fn.hideContent(element.querySelector(config.body));
        } else {
            document.querySelectorAll(config.context).forEach((element, index) => {
                element.querySelectorAll(config.body).forEach((target, index) => {
                    fn.saveCurrentHeight(target);
                    fn.hideContent(target);
                });
                element.querySelectorAll(config.head).forEach((head, index) => {
                    let thisBody = head.nextElementSibling;
                    head.addEventListener("click", () => {
                        if (!thisBody.classList.contains('activeContent')) {
                            fn.showContent(thisBody);
                            head.querySelector('svg').classList.add('rotate-90');
                            head.classList.replace('text-grey-100', 'text-black');
                        } else {
                            fn.hideContent(thisBody);
                            head.querySelector('svg').classList.remove('rotate-90');
                            head.classList.replace('text-black', 'text-grey-100');
                        }
                    });
                });
            });
        }
    };
    accordeon();

    let highlightHeadline = (element) => {
        let text = element.innerHTML.split(' '),
            hightlightedText = '';
        text.forEach((highlight, index) => {
            if (highlight === '<br>') {
                hightlightedText = hightlightedText + highlight;
            } else {
                hightlightedText = hightlightedText + '<span>' + highlight + '</span> ';
            }
            if (index === text.length - 1) {
                element.innerHTML = hightlightedText;
            }
        });
    }

    let highlightHeadlines = () => {
        let highlightHeadlines = document.querySelectorAll('.highlightHeadline');
        highlightHeadlines.forEach((headline, index) => {
            highlightHeadline(headline);
        });
    }
    highlightHeadlines();
});

