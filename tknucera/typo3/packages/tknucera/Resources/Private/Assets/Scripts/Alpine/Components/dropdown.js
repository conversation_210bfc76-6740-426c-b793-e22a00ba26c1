export default ({options}) => ({
    options: options,
    open: false,
    activedescendantId: null,
    selectedItem: null,
    selectedItemId: null,
    focusedIndex: -1,
    optionIds: [],

    init() {
        const firstKey = Object.keys(this.options)[0];
        this.selectedItem = this.options[firstKey];
        this.selectedItemId = firstKey;

        this.optionIds = Object.keys(this.options).map(key => `option-${key}`);
        this.focusedIndex = 0;
        this.activedescendantId = this.optionIds[0];
    },

    toggle() {
        this.open = !this.open;
        if (this.open) {
            this.$nextTick(() => {
                this.focusedIndex = Object.keys(this.options).findIndex(key => key === this.selectedItemId);
                if (this.focusedIndex === -1) this.focusedIndex = 0;
                this.activedescendantId = this.optionIds[this.focusedIndex];
            });
        }
    },

    close() {
        this.open = false;
        this.activedescendantId = null;
        this.$refs['listbox-button'].focus();
    },

    handleKeydown(event) {
        const optionKeys = Object.keys(this.options);

        switch (event.key) {
            case 'ArrowDown':
                event.preventDefault();
                if (!this.open) {
                    this.open = true;
                    this.focusedIndex = 0;
                } else {
                    this.focusedIndex = Math.min(this.focusedIndex + 1, optionKeys.length - 1);
                }
                this.activedescendantId = this.optionIds[this.focusedIndex];
                break;

            case 'ArrowUp':
                event.preventDefault();
                if (!this.open) {
                    this.open = true;
                    this.focusedIndex = optionKeys.length - 1;
                } else {
                    this.focusedIndex = Math.max(this.focusedIndex - 1, 0);
                }
                this.activedescendantId = this.optionIds[this.focusedIndex];
                break;

            case 'Home':
                event.preventDefault();
                if (this.open) {
                    this.focusedIndex = 0;
                    this.activedescendantId = this.optionIds[this.focusedIndex];
                }
                break;

            case 'End':
                event.preventDefault();
                if (this.open) {
                    this.focusedIndex = optionKeys.length - 1;
                    this.activedescendantId = this.optionIds[this.focusedIndex];
                }
                break;

            case 'Enter':
            case ' ':
                event.preventDefault();
                if (this.open) {
                    this.selectFocusedItem();
                } else {
                    this.open = true;
                    this.focusedIndex = Object.keys(this.options).findIndex(key => key === this.selectedItemId);
                    if (this.focusedIndex === -1) this.focusedIndex = 0;
                    this.activedescendantId = this.optionIds[this.focusedIndex];
                }
                break;

            case 'Escape':
                event.preventDefault();
                this.close();
                break;



            default:
                this.handleTypeAhead(event.key);
                break;
        }
    },

    handleTypeAhead(key) {
        if (key.length === 1 && key.match(/[a-zA-Z0-9]/)) {
            const optionValues = Object.values(this.options);
            const currentIndex = this.open ? this.focusedIndex : Object.keys(this.options).findIndex(id => id === this.selectedItemId);

            let foundIndex = -1;
            for (let i = currentIndex + 1; i < optionValues.length; i++) {
                if (optionValues[i].toLowerCase().startsWith(key.toLowerCase())) {
                    foundIndex = i;
                    break;
                }
            }

            if (foundIndex === -1) {
                for (let i = 0; i <= currentIndex; i++) {
                    if (optionValues[i].toLowerCase().startsWith(key.toLowerCase())) {
                        foundIndex = i;
                        break;
                    }
                }
            }

            if (foundIndex !== -1) {
                if (this.open) {
                    this.focusedIndex = foundIndex;
                    this.activedescendantId = this.optionIds[foundIndex];
                } else {
                    const optionKey = Object.keys(this.options)[foundIndex];
                    this.selectedItemId = optionKey;
                    this.selectedItem = this.options[optionKey];
                }
            }
        }
    },

    selectFocusedItem() {
        if (this.focusedIndex >= 0) {
            const optionKey = Object.keys(this.options)[this.focusedIndex];
            this.selectedItemId = optionKey;
            this.selectedItem = this.options[optionKey];
            this.close();
        }
    },

    selectItem(event) {
        const optionElement = event.currentTarget;
        const optionId = optionElement.id;
        const optionKey = optionId.replace('option-', '');

        if (this.options[optionKey]) {
            this.selectedItemId = optionKey;
            this.selectedItem = this.options[optionKey];
            this.close();
        }
    },

    handleMouseEnter(index) {
        this.focusedIndex = index;
        this.activedescendantId = this.optionIds[index];
    },

    handleClickOutside(event) {
        if (!this.$el.contains(event.target)) {
            this.open = false;
            this.activedescendantId = null;
        }
    }
})
