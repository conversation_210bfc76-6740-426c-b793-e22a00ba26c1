export default ({label, tabs}) => ({
    tabs: tabs || [],
    activeTab: 0,
    scrollIndex: 0,
    isScrollable: false,
    visibleTabsCount: 3,
    label: label,

    init() {
        this.initTabs();
        this.handleResize();

        window.addEventListener('resize', () => {
            this.handleResize();
        });

        this.$nextTick(() => {
            setTimeout(() => {
                this.handleResize();
            }, 10);
        });
    },

    initTabs() {
        this.activeTab = 0;
    },

    activateTab(index) {
        this.activeTab = index;
    },

    isTabActive(index) {
        return this.activeTab === index;
    },

    scrollTabs(direction) {
        if (!this.isScrollable) return;

        const maxScrollIndex = Math.max(0, this.tabs.length - this.visibleTabsCount);
        this.scrollIndex = Math.max(0, Math.min(maxScrollIndex, this.scrollIndex + direction));
        this.updateTabPosition();
    },

    updateTabPosition() {
        if (!this.isScrollable) return;

        const tabWrapper = this.$refs.tabWrapper;
        const tabList = this.$refs.tabList;
        const prevBtn = this.$refs.prevBtn;
        const nextBtn = this.$refs.nextBtn;

        if (!tabWrapper || !tabList) return;

        const containerWidth = tabWrapper.offsetWidth;
        let buttonWidth = prevBtn ? prevBtn.offsetWidth : 0;

        if (buttonWidth === 0 && prevBtn) {
            buttonWidth = 40;
        }

        const availableWidth = containerWidth - (buttonWidth * 2);
        const tabWidth = Math.floor(availableWidth / this.visibleTabsCount);

        const maxScrollIndex = Math.max(0, this.tabs.length - this.visibleTabsCount);
        const actualScrollIndex = Math.min(this.scrollIndex, maxScrollIndex);

        let translateX;
        if (actualScrollIndex >= maxScrollIndex && this.tabs.length > this.visibleTabsCount) {
            const totalTabsWidth = tabWidth * this.tabs.length;
            translateX = totalTabsWidth - availableWidth;
        } else {
            translateX = actualScrollIndex * tabWidth;
        }

        tabList.style.transform = `translateX(-${translateX}px)`;
        tabWrapper.style.paddingLeft = `${buttonWidth}px`;
        tabWrapper.style.paddingRight = `${buttonWidth}px`;

        if (prevBtn) prevBtn.disabled = actualScrollIndex === 0;
        if (nextBtn) nextBtn.disabled = actualScrollIndex >= maxScrollIndex;
    },

    handleResize() {
        const tabWrapper = this.$refs.tabWrapper;
        const tabList = this.$refs.tabList;
        const prevBtn = this.$refs.prevBtn;
        const nextBtn = this.$refs.nextBtn;

        if (!tabWrapper || !tabList) return;

        const mdScreen = window.matchMedia('(min-width: 768px)').matches;
        const tabButtons = tabList.querySelectorAll('[role="tab"]');

        if (mdScreen) {

            tabList.style.transform = 'translateX(0)';
            tabList.style.width = 'auto';
            tabWrapper.style.paddingLeft = '0';
            tabWrapper.style.paddingRight = '0';
            tabWrapper.style.overflow = 'visible';
            this.isScrollable = false;
            this.scrollIndex = 0;

            tabButtons.forEach(btn => {
                btn.style.width = 'auto';
                btn.style.minWidth = 'auto';
                btn.style.flexShrink = '';
            });
        } else {
            const containerWidth = tabWrapper.offsetWidth;
            let buttonWidth = prevBtn ? prevBtn.offsetWidth : 0;

            if (buttonWidth === 0 && prevBtn) {
                buttonWidth = 40;
            }

            const availableWidth = containerWidth - (buttonWidth * 2);
            const tabWidth = Math.max(50, Math.floor(availableWidth / this.visibleTabsCount));

            tabWrapper.style.overflow = 'hidden';
            this.isScrollable = true;
            this.scrollIndex = 0;

            tabButtons.forEach(btn => {
                btn.style.width = `${tabWidth}px`;
                btn.style.minWidth = `${tabWidth}px`;
                btn.style.flexShrink = '0';
                btn.style.marginRight = '0';
                btn.style.marginLeft = '0';
            });

            tabList.style.width = `${tabWidth * this.tabs.length}px`;
            tabList.style.gap = '0';
            this.updateTabPosition();
        }
    },

    isPrevDisabled() {
        return !this.isScrollable || this.scrollIndex === 0;
    },

    isNextDisabled() {
        const maxScrollIndex = Math.max(0, this.tabs.length - this.visibleTabsCount);
        return !this.isScrollable || this.scrollIndex >= maxScrollIndex;
    }
})
