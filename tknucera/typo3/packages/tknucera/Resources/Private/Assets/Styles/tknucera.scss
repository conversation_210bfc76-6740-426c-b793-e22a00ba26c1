@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @font-face {
        font-family: 'TKType';
        font-style: normal;
        font-weight: 400;
        font-display: swap;
        src: url('../Fonts/TKTypeRegular.woff2') format('woff2'),
        url('../Fonts/TKTypeRegular.woff') format('woff'),
        url('../Fonts/TKTypeRegular.ttf') format('truetype');
    }
    @font-face {
        font-family: 'TKTypeMedium';
        font-style: normal;
        font-weight: 500;
        font-display: swap;
        src: url('../Fonts/TKTypeMedium.woff2') format('woff2'),
        url('../Fonts/TKTypeMedium.woff') format('woff'),
        url('../Fonts/TKTypeMedium.ttf') format('truetype');
    }
    @font-face {
        font-family: 'TKTypeBold';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('../Fonts/TKTypeBold.woff2') format('woff2'),
        url('../Fonts/TKTypeBold.woff') format('woff');
    }
    @font-face {
        font-family: 'Orbitron-Bold';
        font-style: normal;
        font-weight: 700;
        font-display: swap;
        src: url('../Fonts/Orbitron-Bold.woff2') format('woff2'),
        url('../Fonts/Orbitron-Bold.woff') format('woff'),
        url('../Fonts/Orbitron-Bold.ttf') format('truetype');
    }
}

@layer components {
    body {
        @apply font-tktype text-pitchBlack;
    }
    .corporate_grid {
        @apply grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12 w-full max-w-[1440px] mx-auto;
    }

    .corporate_grid_full {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_halfLeft {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-5 lg:col-end-7;
    }

    .corporate_grid_halfRight {
        @apply col-start-1 md:col-start-5 lg:col-start-7 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_flex3Cols {
        @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
    }

    .corporate_grid_flex3Cols .corporate_grid_flexCol {
        @apply w-full md:w-1/3;
    }

    /* Share button positioning fixes */
    .share-button-container {
        &.share-absolute {
            @apply absolute;
            left: 100%;
        }

        &.share-sticky {
            @apply sticky;
            left: 0;
            margin-left: 100%;
            /* Compensate for parent padding */
            transform: translateX(0);
        }
    }

    /* Alternative: Use fixed positioning for consistent behavior */
    .share-button-fixed {
        @apply fixed right-0 z-20;
        top: 50%;
        transform: translateY(-50%);
    }

    /* headlines */
    .h1 {
        @apply font-tktypeBold text-h1-mobile lg:text-h1 leading-h1-mobile lg:leading-h1;
    }

    .h2 {
        @apply font-tktypeMedium text-h2-mobile lg:text-h2 leading-h2-mobile lg:leading-h2;
    }

    .h3 {
        @apply font-tktypeMedium text-h3-mobile lg:text-h3 leading-h3-mobile lg:leading-h3;
    }

    .subtitle {
        @apply text-subtitle-mobile lg:text-subtitle leading-subtitle-mobile lg:leading-subtitle;
    }

    p {
        @apply text-normal-mobile md:text-normal leading-normal-mobile md:leading-normal;
    }

    .small {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .small p {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall p {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big p {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .button {
        @apply relative inline-flex flex-row items-center gap-3 flex-nowrap text-normal
        leading-normal font-tktypeMedium text-white bg-nuceraPurple px-6 py-4
        hover:shadow-lg group-hover:shadow-lg
        outline-offset-0
        focus:outline-accentGreen focus:outline-[2px] group-focus:outline-accentGreen group-focus:outline-[2px]
        active:outline-nuceraPurple active:bg-midPurple group-active:outline-accentGreen group-active:bg-midPurple
        disabled:bg-nuceraPurple disabled:opacity-50 disabled:pointer-events-none tracking-[0.025rem];

    }

    .button-medium {
        @apply button text-small leading-small py-3;
    }

    .button-small {
        @apply button text-small leading-small py-2 px-4;
    }

    .button-squared {
        @apply button px-4 justify-center;
    }

    .button-medium-squared {
        @apply button-medium px-3 justify-center;
    }

    .button-small-squared {
        @apply button-small px-2 justify-center;
    }

    .button-white {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-nuceraPurple px-6 py-3.5 border outline-offset-2 border-solid border-nuceraPurple bg-oxygenWhite
        hover:shadow-lg hover:bg-lightPurple-25
        active:bg-lightPurple-50
        focus:outline-accentGreen focus:outline-[2px]
        disabled:border-nuceraPurple
        disabled:text-nuceraPurple disabled:pointer-events-none  disabled:opacity-50 tracking-[0.025rem];
    }

    .button-medium-white {
        @apply button-white text-small leading-small py-2.5;
    }

    .button-small-white {
        @apply button-white text-small leading-small py-1.5 px-4;
    }

    .button-white-squared {
        @apply button-white px-4 justify-center;
    }

    .button-medium-white-squared {
        @apply button-medium-white px-3 justify-center;
    }

    .button-small-white-squared {
        @apply button-small-white px-2 justify-center;
    }

    .button-dark {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-tktypeMedium text-oxygenWhite px-6 py-3.5 border outline-offset-2 border-solid border-oxygenWhite
        hover:shadow-lg hover:bg-whiteOpacity12
        active:bg-whiteOpacity20
        focus:outline-accentGreen focus:outline-[2px]
        disabled:pointer-events-none disabled:opacity-50 tracking-[2.5%];
    }

    .button-medium-dark {
        @apply button-dark text-small leading-small py-2.5;
    }

    .button-small-dark {
        @apply button-dark text-small leading-small py-1.5 px-4;
    }

    .button-dark-squared {
        @apply button-dark px-4 justify-center;
    }

    .button-medium-dark-squared {
        @apply button-medium-dark px-3 justify-center;
    }

    .button-small-dark-squared {
        @apply button-small-dark px-2 justify-center;
    }

    .button-chevron {
        @apply relative pl-14;
    }

    .button-arrow {
        @apply relative pl-14
    }

    .button-chevron-pink {
        @apply relative pl-14;
    }

    .button-arrow-pink {
        @apply relative pl-14;
    }

    .button-chevron-black {
        @apply relative pl-14;
    }

    .button-arrow-black {
        @apply relative pl-14;
    }

    .button-with-icon {
        content: '';
        @apply inline-block absolute left-6 top-1/2 w-6 h-6 -translate-y-1/2 bg-no-repeat bg-contain bg-center;
    }

    .button-chevron::after {
        @apply button-with-icon bg-[url(../Icons/chevron-right-white.svg)]
    }

    .button-arrow::after {
        @apply button-with-icon bg-[url(../Icons/arrow-right-white.svg)]
    }

    .button-chevron-pink::after {
        @apply button-with-icon bg-[url(../Icons/chevron-right-pink.svg)]
    }

    .button-arrow-pink::after {
        @apply button-with-icon bg-[url(../Icons/arrow-right-pink.svg)]
    }

    .button-chevron-black::after {
        @apply button-with-icon bg-[url(../Icons/chevron-right-white.svg)]
    }

    .button-arrow-black::after {
        @apply button-with-icon bg-[url(../Icons/arrow-right-white.svg)]
    }

    table {
        @apply border-collapse;
    }

    figure.table {
        @apply w-full;
    }

    table:not(.table-plain) {
        @apply w-full table-auto;
    }

    table:not(.table-plain) thead tr {
        @apply hidden md:table-row border-b-2 border-nuceraPurple text-nuceraPurple font-bold text-3xl;
    }

    table:not(.table-plain) tbody tr {
        @apply mb-4 last:mb-0 md:mb-0 flex flex-col md:table-row shadow-lg
        md:odd:bg-white md:even:bg-hydroGray-50;
    }

    table:not(.table-plain) th {
        @apply px-4 py-6 text-start;
    }

    table:not(.table-plain) td {
        @apply px-4 py-6 text-start
        first:border-0 first:odd:border-b-2 first:odd:border-t-0 first:odd:border-nuceraPurple odd:border-b odd:border-t md:odd:border-b-0 md:first:odd:border-0 md:odd:border-t-0  border-skyGray
        first:text-nuceraPurple first:font-bold first:text-subtitle md:first:text-normal md:first:text-black md:first:font-normal text-normal
        odd:bg-hydroGray-50 even:bg-white md:odd:bg-transparent md:even:bg-transparent md:first:bg-transparent first:border-b-2 first:border-nuceraPurple md:first:border-b-0;
    }

    table.table-plain td {
        @apply p-0 pr-4;
    }

    .link {
       @apply relative underline text-normal
    }

    .link-dark {
        @apply link text-darkmetalGray-250;
    }

    .link-white {
        @apply link text-white;
    }

    .link-chevron {
        @apply relative text-nuceraPurple pl-6;
    }

    .link-chevron-white {
        @apply link-chevron text-white pl-6;
    }

    .link-arrow-white {
        @apply relative text-white pr-6;
    }

    .link-with-icon {
        content: '';
        @apply inline-block absolute top-1/2 w-5 h-5 -translate-y-1/2 bg-no-repeat bg-contain bg-center
    }

    .link-chevron::after {
        content: '';
        @apply link-with-icon left-0 bg-[url(../Icons/chevron-right-pink.svg)]
    }

    .link-chevron-white::after {
        content: '';
        @apply link-with-icon left-0 bg-[url(../Icons/chevron-right-white.svg)]
    }

    .link-arrow {
        @apply relative text-nuceraPurple pr-6;
    }

    .link-arrow::after {
        @apply link-with-icon right-0 bg-[url(../Icons/arrow-right-pink.svg)]
    }

    .link-arrow-white::after {
        @apply link-with-icon right-0 bg-[url(../Icons/arrow-right-white.svg)]
    }

    /* only for styleguide */
    .layoutPlaceholder {
        @apply bg-darkmetalGray w-full text-white p-4;
    }

    .colorList {
        @apply flex flex-row gap-6 mb-12 flex-wrap;
    }
    .colorList > div {
        @apply h-24 w-24 rounded-full shadow-lg flex items-center justify-center;
    }

    .highlightHeadline {
        @apply leading-highlighted;
    }

    .highlightHeadline span {
        @apply inline-block px-2 pt-1 lg:pt-2 -mr-4 lg:px-3.5 lg:-mr-7 mb-3;
    }

    h2.highlightHeadline span {
        @apply pt-1 px-1.5 -mr-3 lg:px-2.5 lg:-mr-5 mb-3;
    }

    h3.highlightHeadline span {
        @apply pt-1 px-1 -mr-2 lg:px-2 lg:-mr-4 mb-2;
    }

    .highlightPositive span {
        @apply bg-nuceraPurple text-white;
    }

    .highlightNegative span {
        @apply bg-white text-nuceraPurple;
    }

    .container-dark {
        @apply text-oxygenWhite bg-darkmetalGray-150 py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
    }

    .container-dark-noMarginTop {
        @apply container-dark pt-0;
    }

    .container-white {
        @apply bg-oxygenWhite text-pitchBlack py-4 md:py-8 lg:py-12 px-4 md:px-8 lg:px-12;
    }

    .container-white-noMarginTop {
        @apply container-white pt-0;
    }

    .container-bgImageLight {
        @apply bg-oxygenWhite text-pitchBlack px-0;
    }

    .container-bgImageDark {
        @apply text-oxygenWhite bg-darkmetalGray-150 px-0;
    }

    .st_share_this {
        [class^="st_"] {
            @apply  bg-nuceraPurple h-6 w-6 flex items-center justify-center;

            .stButton {
                @apply flex mt-1 items-center justify-center ;

                .chicklets {
                    @apply h-6
                }

                .chicklets.facebook {
                   @apply bg-[url(../Icons/facebook-white.svg)];
                }

                .chicklets.twitter {
                   @apply bg-[url(../Icons/twitter-white.svg)] h-4 ml-1;
                }

                .chicklets.linkedin {
                   @apply bg-[url(../Icons/linkedin-white.svg)];
                }

                .chicklets.whatsapp {
                   @apply bg-[url(../Icons/whatsapp-white.svg)];
                }

                .chicklets.email {
                   @apply bg-[url(../Icons/envelope-white.svg)];
                }
            }
        }

    }
}

@layer utilities {
    .font-smaller {
        font-size: 75%;
    }
    .font-larger {
        font-size: 120%;
    }
}

.forced-screen-width {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.forced-screen-width-unset {
    width: unset;
    position: unset;
    left: unset;
    right: unset;
    margin-left: unset;
    margin-right: unset;
}

.vf-container {
    position: relative;
    top: 0;
    left: 0;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.vf-container img {
    position: absolute;
    left: 0;
    top: 0;
    margin: 0;
    display: block;
    /* fill and maintain aspect ratio */
    width: auto; height: auto;
    min-width: 100%; min-height: 100%;
    max-height: none; max-width: none;
}

[x-cloak] { display: none !important; }
