<html xmlns="http://www.w3.org/1999/xhtml" lang="en"
      xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
      xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
      data-namespace-typo3-fluid="true"
>

<f:layout name="Default" />

<f:section name="main">

<f:if condition="{configuration.globalSharing}==0">
	<f:if condition="{settings.categories}=='simpleWidget'">
		<f:then>
			<f:if condition="{settings.buttonType}== 'horizontal'">
				<f:then>
					<f:for each="{socials}" as="social">
						<span class='st_{social}_hcount' displayText='{social}'></span>
					</f:for>
				</f:then>
				<f:else>
					<f:if condition="{settings.buttonType}== 'vertical'">
						<f:then>
							<f:for each="{socials}" as="social">
								<span class='st_{social}_vcount' displayText='{social}'></span>
							</f:for>
						</f:then>
						<f:else>

                <div
                  x-data="{
                    open: false,
                    isSticky: false,
                    pageTopOffset: 0,
                    init() {
                      const articleContent = document.querySelector('.articleMainContent');
                      const header = document.querySelector('#mainHeader');
                      const headerHeight = header.offsetHeight;
                      const navigation = document.querySelector('#mainNavigation');
                      const navigationHeight = navigation.offsetHeight;

                      const articleContentTop = articleContent.getBoundingClientRect().top + window.scrollY;

                      window.addEventListener('scroll', () => {

                        this.pageTopOffset = window.scrollY;
                        this.isSticky = this.pageTopOffset > articleContentTop;
                      });

                      document.getElementById('async-buttons').addEventListener('load', () => {
                        const spans = document.querySelectorAll('.st_share_this .stButton')
                        spans.forEach((span) => span.style = '');

                        const listItems = document.querySelectorAll('.st_share_this li')
                        listItems.forEach((item) => {
                          const processed = item.getAttribute('st_processed');
                          if(!processed) item.remove();
                        });
                      });
                    }
                  }"
                  class="top-52 z-20 cursor-pointer inline-flex flex-col gap-2 px-3 py-4 items-center hover:bg-darkPurple text-white"
                  x-bind:class="[
                    open ? 'bg-darkPurple' : 'bg-nuceraPurple',
                    isSticky ? 'sticky left-full' : 'absolute right-10 lg:right-32'
                  ]"
                  x-on:click="open = !open"
                  x-on:click.outside = "open = false"
                >
                  <button type="button" class="" x-on:click.stop="open = !open">
                    <span class="block [writing-mode:sideways-lr] uppercase">
                      <f:render partial="Data/LanguageLabel" section="Main" arguments="{key: 'tx_tknucera.label.share'}" />
                    </span>
                  </button>
                  <ul x-show="open" class="st_share_this flex flex-col gap-3">
                    <f:for each="{socials}" as="social">
                      <li class='st_{social}' displayText="{f:if(condition:'{settings.displayText}',then:'{social}')}">
                      </li>
                    </f:for>
                  </ul>
                </div>

						</f:else>
					</f:if>
				</f:else>
			</f:if>
		</f:then>
	</f:if>
</f:if>

</f:section>

