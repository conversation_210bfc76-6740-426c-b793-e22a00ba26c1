/* classes for the RTE */

.h1 {
  font-family: 'TKTypeBold', Verdana, Tahoma, Arial, sans-serif;
  font-size: 3rem;
  line-height: 120%;

}

.highlightPositive {
  background-color: #A51482;
  color: #ffffff;
}

.highlightNegative {
  background-color: #ffffff;
  color: #A51482;
}

@media (min-width: 1024px) {
  .h1 {
    font-size: 4rem;
    line-height: 120%;
  }
}

.h2 {
  font-family: 'TKTypeMedium', Verdana, Tahoma, Arial, sans-serif;
  font-size: 2.5rem;
  line-height: 130%;
}

@media (min-width: 1024px) {
  .h2 {
    font-size: 3rem;
    line-height: 130%;
  }
}

.h3 {
  font-family: 'TKTypeMedium', Verdana, Tahoma, Arial, sans-serif;
  font-size: 2rem;
  line-height: 130%;
}

@media (min-width: 1024px) {
  .h3 {
    font-size: 2.25rem;
    line-height: 130%;
  }
}

.big {
  font-size: 1.125rem;
  line-height: 150%;
}

@media (min-width: 768px) {
  .big {
    font-size: 1.25rem;
    line-height: 150%;
  }
}

.small {
  font-size: 1rem;
  line-height: 150%;
}

@media (min-width: 768px) {
  .small {
    font-size: 1rem;
    line-height: 150%;
  }
}

.extrasmall {
  font-size: 0.875rem;
  line-height: 150%;
}

@media (min-width: 768px) {
  .extrasmall {
    font-size: 0.875rem;
    line-height: 150%;
  }
}

table {
    border-collapse: collapse;
}

figure.table {
    width: 100%;
}

table:not(.table-plain) {
    width: 100%;
    table-layout: auto;
}

table:not(.table-plain) thead tr {
    display: none;
    border-bottom: 2px solid #A51482;
    color: #A51482;
    font-weight: bold;
    font-size: 2.25rem;
}

@media (min-width: 768px) {
    table:not(.table-plain) thead tr {
        display: table-row;
    }
}

table:not(.table-plain) tbody tr {
    margin-bottom: 1rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

@media (min-width: 768px) {
    table:not(.table-plain) tbody tr {
        display: table-row;
        margin-bottom: 0;
    }

    table:not(.table-plain) tbody tr:nth-child(odd) {
        background-color: #ffffff;
    }

    table:not(.table-plain) tbody tr:nth-child(even) {
        background-color: #EEF0F2;
    }
}

table:not(.table-plain) th {
    padding: 1.5rem 1rem;
    text-align: left;
}


table:not(.table-plain) td {
    padding: 1.5rem 1rem;
    text-align: left;
    border-top: 1px solid #D9DEE8;
    border-bottom: 1px solid #D9DEE8;
    font-size: 1.125rem;
    color: #000000;
}

table.table-plain td {
    padding: 0;
    padding-right: 1rem;
}

.button {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  gap: 0.75rem;
  font-size: 1.125rem;
  line-height: 150%;
  font-family: "TKTypeMedium", Verdana, Tahoma, Arial, sans-serif;
  color: #ffffff;
  background-color: #A51482;
  padding: 1rem 1.5rem;
  letter-spacing: 0.025rem;
  outline-offset: 0;
}

.button:hover,
.button.group-hover {
  box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
}

.button:focus,
.button.group-focus {
  outline: 2px solid #9BC832;
}

.button:active {
  outline-color: #A51482;
  background-color: #7F187D;
}

.button.group-active {
  outline-color: #9BC832;
  background-color: #7F187D;
}

.button:disabled {
  background-color: #A51482;
  opacity: 0.5;
  pointer-events: none;
}

.button-chevron::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/chevron-right-white.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-chevron-pink::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/chevron-right-pink.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-chevron-black::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/chevron-right.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-arrow::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/arrow-right-white.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-arrow-pink::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/arrow-right-pink.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-arrow-black::after {
  content: '';
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  position: absolute;
  left: 24px;
  top: 50%;
  transform: translateY(-50%);
  background-image: url('../Icons/arrow-right.svg');
  background-repeat: no-repeat;
  background-size: contain;
  background-position: center;
}

.button-dark {
    display: inline-flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    line-height: 150%;
    font-family: 'TKTypeMedium', Verdana, Tahoma, Arial, sans-serif;
    color: black; /* only for rte */
    padding: 0.875rem 1.5rem;
    border: 1px solid black; /* only for rte */
    outline-offset: 2px;
    letter-spacing: 0.025rem;
    transition: all 0.2s ease;
}

.button-dark:hover {
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
    background-color: rgba(255,255,255,0.12);
}

.button-dark:active {
    background-color: rgba(255,255,255,0.2);
}

.button-dark:focus {
    outline: 2px solid #9BC832;
}

.button-dark:disabled {
    pointer-events: none;
    opacity: 0.5;
}

.button-white {
    display: inline-flex;
    flex-direction: row;
    flex-wrap: nowrap;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.125rem;
    line-height: 150%;
    font-family: 'TKTypeMedium', Verdana, Tahoma, Arial, sans-serif;
    color: #A51482;
    padding: 0.875rem 1.5rem;
    border: 1px solid #A51482;
    outline-offset: 2px;
    background-color: #ffffff;
    letter-spacing: 0.025rem;
    transition: all 0.2s ease;
}

.button-white:hover {
    box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1), 0 4px 6px -2px rgba(0,0,0,0.05);
    background-color: #F7F7F9;
}

.button-white:active {
    background-color: #EEF0F2;
}

.button-white:focus {
    outline: 2px solid #9BC832;
}

.button-white:disabled {
    border-color: #A51482;
    color: #A51482;
    pointer-events: none;
    opacity: 0.5;
}

.button-chevron,
.button-arrow,
.button-chevron-pink,
.button-arrow-pink,
.button-chevron-black,
.button-arrow-black{
  position: relative;
  padding-left: 3.5rem;
}

.link {
    position: relative;
    text-decoration: underline;
    font-size: 1.125rem;
}

.link-dark {
    position: relative;
    text-decoration: underline;
    font-size: 1.125rem;
    color: #111B2C;
}

.link-white {
    position: relative;
    text-decoration: underline;
    font-size: 1.125rem;
    color: black;
}

.link-chevron {
  position: relative;
  padding-left: 1.5rem;
  color: #A51482;
}

.link-arrow {
    position: relative;
    padding-right: 1.5rem;
    color: #A51482;
}

.link-chevron-white {
  position: relative;
  padding-left: 1.5rem;
}

.link-arrow-white {
    position: relative;
    padding-right: 1.5rem;
}

.link-chevron::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    width: 1.5rem;
    height: 1.5rem;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url('../Icons/chevron-right-pink.svg');
}

.link-chevron-white::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    left: 0;
    width: 1.5rem;
    height: 1.5rem;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url('../Icons/chevron-right.svg');
}

.link-arrow::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 1.5rem;
    height: 1.5rem;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url('../Icons/arrow-right-pink.svg');
}

.link-arrow-white::after {
    content: '';
    display: inline-block;
    position: absolute;
    top: 50%;
    right: 0;
    width: 1.5rem;
    height: 1.5rem;
    transform: translateY(-50%);
    background-repeat: no-repeat;
    background-size: contain;
    background-position: center;
    background-image: url('../Icons/arrow-right.svg');
}


