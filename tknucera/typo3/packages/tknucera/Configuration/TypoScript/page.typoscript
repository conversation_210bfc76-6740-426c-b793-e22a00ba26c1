page = PAGE
page {
    # has to be the first tag
    headTag = <head>

    headerData {
        20 = COA
        20 {
            20 = TEXT
            20.value (
                <meta name="viewport" content="width=device-width, initial-scale=1">
            )
        }

    }

    includeCSSLibs {
        tknucera = typo3_encore:tknucera
    }

    includeJSFooter {
        tknucera = typo3_encore:tknucera
        focuspoint = EXT:tknucera/Resources/Public/Scripts/focuspoint.js
    }

    10 = FLUIDTEMPLATE
    10 {

        templateRootPaths.20 = EXT:tknucera/Resources/Private/Templates/
        partialRootPaths.20  = EXT:tknucera/Resources/Private/Partials/
        layoutRootPaths.20   = EXT:tknucera/Resources/Private/Layouts/

        file.stdWrap.cObject = CASE
        file.stdWrap.cObject {
            key.field = doktype

            default = TEXT
            default.value = EXT:tknucera/Resources/Private/Templates/Page/Default.html
        }

        dataProcessing {

            # Footer Menu
            10 = TYPO3\CMS\Frontend\DataProcessing\MenuProcessor
            10 {
                special = directory
                special.value = {$tknucera.pids.footer_menu}
                levels = 3
                includeSpacer = 1
                as = footernavigation
            }
        }
    }
}


