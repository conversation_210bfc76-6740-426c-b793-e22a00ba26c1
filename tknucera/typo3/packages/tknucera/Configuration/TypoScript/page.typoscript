page = PAGE
page {
    # has to be the first tag
    headTag = <head>

    headerData {
        20 = COA
        20 {
            20 = TEXT
            20.value (
                <meta name="viewport" content="width=device-width, initial-scale=1">
            )
        }

    }

    includeCSSLibs {
        tknucera = typo3_encore:tknucera
    }

    includeJSFooter {
        tknucera = typo3_encore:tknucera
        focuspoint = EXT:tknucera/Resources/Public/Scripts/focuspoint.js
    }
}

tknucera {
    pids {
        footer_menu_main = {$tknucera.pids.footer_menu_main}
        footer_menu_meta = {$tknucera.pids.footer_menu_meta}
        root_page = {$tknucera.pids.root_page}
    }
    social_media_links {
        facebook = {$tknucera.social_media_links.facebook}
        instagram = {$tknucera.social_media_links.instagram}
        linkedin = {$tknucera.social_media_links.linkedin}
        xing = {$tknucera.social_media_links.xing}
        youtube = {$tknucera.social_media_links.youtube}
    }
}

lib.pageMediaImage = FILES
lib.pageMediaImage {
   references {
       table = pages
       uid.data = uid
       fieldName = media
   }
   begin = 0
   maxItems = 1
   renderObj = TEXT
   renderObj {
       data = file:current:publicUrl
       wrap = |
   }
}

lib.pageMediaImageUid = FILES
lib.pageMediaImageUid {
   references {
       table = pages
       uid.data = uid
       fieldName = media
   }
   begin = 0
   maxItems = 1
   renderObj = TEXT
   renderObj {
       data = file:current:uid
       wrap = |
   }
}


