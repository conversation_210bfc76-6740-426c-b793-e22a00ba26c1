page = PAGE
page {
    # has to be the first tag
    headTag = <head>

    headerData {
        20 = COA
        20 {
            20 = TEXT
            20.value (
                <meta name="viewport" content="width=device-width, initial-scale=1">
            )
        }

    }

    includeCSSLibs {
        tknucera = typo3_encore:tknucera
    }

    includeJSFooter {
        tknucera = typo3_encore:tknucera
        focuspoint = EXT:tknucera/Resources/Public/Scripts/focuspoint.js
    }
}

tknucera {
    pids {
        footer_menu_main = {$tknucera.pids.footer_menu_main}
        footer_menu_meta = {$tknucera.pids.footer_menu_meta}
        root_page = {$tknucera.pids.root_page}
        categories = {$tknucera.pids.categories}
    }
    social_media_links {
        facebook = {$tknucera.social_media_links.facebook}
        instagram = {$tknucera.social_media_links.instagram}
        linkedin = {$tknucera.social_media_links.linkedin}
        twitter = {$tknucera.social_media_links.twitter}
        youtube = {$tknucera.social_media_links.youtube}
    }
}

lib.pageMediaImage = FILES
lib.pageMediaImage {
   references {
       table = pages
       uid.data = uid
       fieldName = media
   }
   begin = 0
   maxItems = 1
   renderObj = TEXT
   renderObj {
       data = file:current:publicUrl
       wrap = |
   }
}

lib.pageMediaImageUid = FILES
lib.pageMediaImageUid {
   references {
       table = pages
       uid.data = uid
       fieldName = media
   }
   begin = 0
   maxItems = 1
   renderObj = TEXT
   renderObj {
       data = file:current:uid
       wrap = |
   }
}

lib.contentCategories = COA
lib.contentCategories {
    10 = CONTENT
    10 {
         dataWrap = {field:recordUid} |
         table = sys_category
         select {
             pidInList = {$tknucera.pids.categories}
             selectFields = sys_category.title
             join = sys_category_record_mm ON sys_category_record_mm.uid_local = sys_category.uid
             where.data = field:recordUid
             where.intval = 1
             max = 100
             where.wrap = sys_category_record_mm.uid_foreign=|
             orderBy = sys_category_record_mm.sorting_foreign
             languageField = 0 # disable translation handling of sys_category
        }
        renderObj = TEXT
        renderObj.field = title
        renderObj.noTrimWrap = ||,|

        # remove last space because of noTrimWrap above
        stdWrap.trim = 1
        # remove last comma because of noTrimWrap above
        stdWrap.substring = 0, -1
    }
}

lib.parseFunc_RTE {
  externalBlocks {
    table {
      stdWrap {
        HTMLparser {
          tags {
            table {
              fixAttrib {
                class {
                  default = table-plain
                  always = 0
                  list = table, table-plain
                }
              }
            }
          }
        }
      }
    }
  }
}


