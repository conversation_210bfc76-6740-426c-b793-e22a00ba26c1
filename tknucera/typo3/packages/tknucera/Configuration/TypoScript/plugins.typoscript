plugin.tx_tknucera.view {
    templateRootPaths.default = EXT:tknucera/Resources/Private/Templates/
    partialRootPaths.default = EXT:tknucera/Resources/Private/Partials/
    layoutRootPaths.default = EXT:tknucera/Resources/Private/Layouts/
}

plugin.tx_tknucera {
    view {
        templateRootPaths.0 = {$plugin.tx_tknucera.view.templateRootPaths.default}
        partialRootPaths.0  = {$plugin.tx_tknucera.view.partialRootPaths.default}
        layoutRootPaths.0   = {$plugin.tx_tknucera.view.layoutRootPaths.default}
    }
}

plugin.tx_typo3encore {
    settings {
        builds {
            _default = EXT:tknucera/Resources/Public/Assets
        }

        entrypointJsonPath = EXT:tknucera/Resources/Public/Assets/entrypoints.json
        manifestJsonPath = EXT:tknucera/Resources/Public/Assets/manifest.json

        # Throw an exception if the entrypoints.json file is missing or an entry is missing from the data
        strictMode = 1

        preload {
            # preload all rendered script and link tags automatically via the http2 Link header
            enable = 1
            crossorigin =
        }
    }
}

