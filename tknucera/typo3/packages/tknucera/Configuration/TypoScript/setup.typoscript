@import 'EXT:tknucera/Configuration/TypoScript/development.typoscript';

plugin.tx_tknucera {
    view {
        templateRootPaths.0 = {$plugin.tx_tknucera.view.templateRootPaths.default}
        partialRootPaths.0  = {$plugin.tx_tknucera.view.partialRootPaths.default}
        layoutRootPaths.0   = {$plugin.tx_tknucera.view.layoutRootPaths.default}
    }
}

lib.contentElement {
    templateRootPaths {
        200 = EXT:tknucera/Resources/Private/fluid_styled_content/Templates/
    }
    layoutRootPaths {
        200 = EXT:tknucera/Resources/Private/fluid_styled_content/Layouts/
    }
}

# config
<INCLUDE_TYPOSCRIPT: source="FILE:EXT:tknucera/Configuration/TypoScript/config.typoscript">

plugin.tx_typo3encore {
    settings {
        builds {
            _default = EXT:tknucera/Resources/Public/Assets
        }

        entrypointJsonPath = EXT:tknucera/Resources/Public/Assets/entrypoints.json
        manifestJsonPath = EXT:tknucera/Resources/Public/Assets/manifest.json

        # Throw an exception if the entrypoints.json file is missing or an entry is missing from the data
        strictMode = 1

        preload {
            # preload all rendered script and link tags automatically via the http2 Link header
            enable = 1
            crossorigin =
        }
    }
}

# Override ns_sharethis templates
plugin.tx_nssharethis {
  view {
    templateRootPaths.10 = EXT:tknucera/Resources/Private/Extensions/nitsan/ns-sharethis/
    layoutRootPaths.10 = EXT:tknucera/Resources/Private/Extensions/nitsan/ns-sharethis/Layouts/
    partialRootPaths.10 = EXT:tknucera/Resources/Private/Partials/
  }
}

# page
<INCLUDE_TYPOSCRIPT: source="FILE:EXT:tknucera/Configuration/TypoScript/page.typoscript">

