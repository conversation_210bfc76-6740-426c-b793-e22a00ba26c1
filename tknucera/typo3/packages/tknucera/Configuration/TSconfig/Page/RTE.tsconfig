RTE.default.preset = default

RTE.default.proc.exitHTMLparser_db = 1
# we cannot define allowTags on RTE presets unfortunately,
# only on single field names
# https://docs.typo3.org/c/typo3/cms-rte-ckeditor/master/en-us/Configuration/ConfigureTypo3.html
RTE.default.proc.exitHTMLparser_db.allowTags = a,br,em,li,ol,strong,sub,sup,u,ul,h1,h2,h3,h4,h5,h6
RTE.default.proc.exitHTMLparser_db.tags {
    // em.fixAttrib.class.set = italic
    // strong.fixAttrib.class.set = font-bold
    // u.fixAttrib.class.set = underline
    // ol.fixAttrib.class.set = list-decimal pl-4 mb-4
    // ul.fixAttrib.class.set = list-disc pl-4 mb-4
    // p.fixAttrib.class.set = mb-4
    // a.fixAttrib.class.set = underline hover:text-twilight-blue
}
