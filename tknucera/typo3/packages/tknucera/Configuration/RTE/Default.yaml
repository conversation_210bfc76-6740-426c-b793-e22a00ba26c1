# Dies steht in der Default.yaml.

imports:
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Processing.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml" }

editor:
  config:
    debug: false
    contentsCss: "EXT:tknucera/Resources/Public/Styles/rte.css"
    bodyClass: "color-text font-base stack"
    autoGrow_bottomSpace: 50
    autoGrow_maxHeight: 900
    autoGrow_onStartup: 'true'
    pasteFromWordPromptCleanup: true

    stylesSet:
      - { name: "H1", element: "h1", attributes: { "class": "h1" } }
      - { name: "H1 Pinker HG", element: "h1", attributes: { "class": "h1 highlightPositive highlightHeadline" } }
      - { name: "H1 Weißer HG", element: "h1", attributes: { "class": "h1 highlightNegative highlightHeadline" } }
      - { name: "H2", element: "h2", attributes: { "class": "h2" } }
      - { name: "H2 Pinker HG", element: "h2", attributes: { "class": "h2 highlightPositive highlightHeadline" } }
      - { name: "H2 Weißer HG", element: "h2", attributes: { "class": "h2 highlightNegative highlightHeadline" } }
      - { name: "H3", element: "h3", attributes: { "class": "h3" } }
      - { name: "Big", element: "p", attributes: { "class": "big" } }
      - { name: "Small", element: "p", attributes: { "class": "small" } }
      - { name: "Extrasmall", element: "p", attributes: { "class": "extrasmall" } }
      - { name: "Button", element: "a", attributes: { "class": "button" } }
      - { name: "Button Weiß", element: "a", attributes: { "class": "button-white" } }
      - { name: "Button Outline", element: "a", attributes: { "class": "button-dark" } }
      - { name: "Chevron-Button", element: "a", attributes: { "class": "button button-chevron" } }
      - { name: "Pfeil-Button", element: "a", attributes: { "class": "button button-arrow" } }
      - { name: "Chevron-Button Weiß", element: "a", attributes: { "class": "button-white button-chevron-pink" } }
      - { name: "Pfeil-Button Weiß", element: "a", attributes: { "class": "button-white button-arrow-pink" } }
      - { name: "Chevron-Button Outline", element: "a", attributes: { "class": "button-dark button-chevron-black" } }
      - { name: "Pfeil-Button Outline", element: "a", attributes: { "class": "button-dark button-arrow-black" } }
      - { name: "Link", element: "a", attributes: { "class": "link-dark" } }
      - { name: "Link Weiß", element: "a", attributes: { "class": "link-white" } }
      - { name: "Chevron-Link", element: "a", attributes: { "class": "link link-chevron" } }
      - { name: "Chevron-Link Weiß", element: "a", attributes: { "class": "link link-chevron-white" } }
      - { name: "Pfeil-Link", element: "a", attributes: { "class": "link link-arrow" } }
      - { name: "Pfeil-Link Weiß", element: "a", attributes: { "class": "link link-arrow-white" } }
      - { name: "Tabelle", element: "table", attributes: { "class": "table" } }
      - { name: "Tabelle ohne Layout", element: "table", attributes: { "class": "table-plain" } }

    table:
      defaultHeadings: { rows: 1 }
      contentToolbar:
        - tableColumn
        - tableRow
        - toggleTableCaption

    toolbarGroups:
      - { name: styles, groups: [ format, styles ] }
      - { name: clipboard, groups: [ undo, clipboard, cleanup ] }
      - "/"
      - { name: basicstyles, groups: [ basicstyles ] }
      - { name: paragraph, groups: [ list, indent, blocks, align ] }
      - { name: links, groups: [ links ] }
      - { name: editing, groups: [ spellchecker ] }
      - { name: insert, groups: [ insert ] }
      - { name: tools, groups: [ table, specialchar ] }
      - { name: document, groups: [ mode ] }

    format_tags: "p;h1;h2;h3;h4;table,ul"
    extraAllowedContent: "hr"

    justifyClasses:
      - text-left
      - text-center

    extraPlugins:
      - justify
      - autolink
      - wordcount
      - autogrow


    removePlugins:
      - image

    removeButtons:
      - Anchor
      - Blockquote
      - Indent
      - Outdent
      - Underline
      - Strike
      - JustifyBlock
      - JustifyRight
