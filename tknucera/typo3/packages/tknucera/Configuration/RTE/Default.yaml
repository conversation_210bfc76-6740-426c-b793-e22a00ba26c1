# Dies steht in der Default.yaml.

imports:
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Processing.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Base.yaml" }
  - { resource: "EXT:rte_ckeditor/Configuration/RTE/Editor/Plugins.yaml" }

editor:
  config:
    debug: false
    contentsCss: "EXT:tknucera/Resources/Public/Styles/rte.css"
    bodyClass: "color-text font-base stack"
    autoGrow_bottomSpace: 50
    autoGrow_maxHeight: 900
    autoGrow_onStartup: 'true'
    pasteFromWordPromptCleanup: true

    stylesSet:
      - { name: "H1 Blau", element: "h1", attributes: { "class": "h1 text-primaryBlue" } }
      - { name: "H1 Blauer HG", element: "h1", attributes: { "class": "h1 highlightPositive highlightHeadline" } }
      - { name: "H1 Weißer HG", element: "h1", attributes: { "class": "h1 highlightNegative highlightHeadline" } }
      - { name: "H2 Blau", element: "h2", attributes: { "class": "h2 text-primaryBlue" } }
      - { name: "H2 Blauer HG", element: "h2", attributes: { "class": "h2 highlightPositive highlightHeadline" } }
      - { name: "H2 Weißer HG", element: "h2", attributes: { "class": "h2 highlightNegative highlightHeadline" } }
      - { name: "H4 Extrabold", element: "h4", attributes: { "class": "font-corporateBold" } }
      - { name: "Big", element: "p", attributes: { "class": "big" } }
      - { name: "Small", element: "p", attributes: { "class": "small" } }
      - { name: "Extrasmall", element: "p", attributes: { "class": "extrasmall" } }
      - { name: "Button Blau", element: "a", attributes: { "class": "button" } }
      - { name: "Button Blau Medium", element: "a", attributes: { "class": "button-medium" } }
      - { name: "Button Weiß", element: "a", attributes: { "class": "button-white" } }
      - { name: "Button Weiß Medium", element: "a", attributes: { "class": "button-medium-white" } }
      - { name: "Tabelle", element: "table", attributes: { 'class': 'table' } }
      - { name: "Tabelle ohne Layout", element: "table", attributes: { 'class': 'table-plain' } }
      - { name: "Inhaltsverzeichnis", element: "ul", attributes: { 'class': 'tableOfContent' } }

    table:
      defaultHeadings: { rows: 1 }
      contentToolbar:
        - tableColumn
        - tableRow
        - toggleTableCaption

    toolbarGroups:
      - { name: styles, groups: [ format, styles ] }
      - { name: clipboard, groups: [ undo, clipboard, cleanup ] }
      - "/"
      - { name: basicstyles, groups: [ basicstyles ] }
      - { name: paragraph, groups: [ list, indent, blocks, align ] }
      - { name: links, groups: [ links ] }
      - { name: editing, groups: [ spellchecker ] }
      - { name: insert, groups: [ insert ] }
      - { name: tools, groups: [ table, specialchar ] }
      - { name: document, groups: [ mode ] }

    format_tags: "p;h1;h2;h3;h4;table,ul"
    extraAllowedContent: "hr"

    justifyClasses:
      - text-left
      - text-center

    extraPlugins:
      - justify
      - autolink
      - wordcount
      - autogrow
      - table_responsive
      - table_wrap

    removePlugins:
      - image

    removeButtons:
      - Anchor
      - Blockquote
      - Indent
      - Outdent
      - Underline
      - Strike
      - JustifyBlock
      - JustifyRight
