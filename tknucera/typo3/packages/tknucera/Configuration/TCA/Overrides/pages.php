<?php

use TYPO3\CMS\Core\Utility\ExtensionManagementUtility;

defined('TYPO3') || die();

call_user_func(function () {

    $articlePageDoktype = 116;
    $articlePageIcon = 'tx-article-page';

    ExtensionManagementUtility::addTcaSelectItem(
        'pages',
        'doktype',
        [
            'label' => 'LLL:EXT:tknucera/Resources/Private/Language/locallang.xlf:tx_tknucera.label.article_page_type',
            'value' => $articlePageDoktype,
            'group' => 'default',
            'icon' => $articlePageIcon,
        ],
    );

    $GLOBALS['TCA']['pages']['ctrl']['typeicon_classes'][$articlePageDoktype] = $articlePageIcon;



});
