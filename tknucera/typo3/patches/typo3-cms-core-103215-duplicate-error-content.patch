commit 8103f5f78f6ee8d640a32388794f5be11c95428d
Author: <PERSON> <<EMAIL>>
Date:   Mon Apr 8 12:50:34 2024 +0200

    #103215: Fix duplicate error content

diff --git typo3/sysext/core/Classes/Error/PageErrorHandler/PageContentErrorHandler.php typo3/sysext/core/Classes/Error/PageErrorHandler/PageContentErrorHandler.php
index fadf96c1e7..79cc35e99d 100644
--- a/Classes/Error/PageErrorHandler/PageContentErrorHandler.php
+++ b/Classes/Error/PageErrorHandler/PageContentErrorHandler.php
@@ -125,5 +125,8 @@ protected function sendSubRequest(ServerRequestInterface $request, int $pageId,

         $request = $request->withAttribute('originalRequest', $originalRequest);

+        $pageRenderer = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Page\PageRenderer::class);
+        $pageRenderer->setBodyContent('');
+
         return $this->application->handle($request);
     }

