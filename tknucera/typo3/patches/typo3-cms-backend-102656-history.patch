From 37195f94fa2f2ea2d34c02f05c6c7848344389f2 Mon Sep 17 00:00:00 2001
From: <PERSON> <<EMAIL>>
Date: Thu, 04 Apr 2024 15:36:40 +0200
Subject: [PATCH] [BUGFIX] Prevent rendering FlexForm diff of deleted records in history

When the record does not exist anymore, then the FlexForm definition
might also not exist anymore (imagine a removed plugin) and it results
in an Exception.

Resolves: #102656
Releases: main, 12.4
Change-Id: I3eac287a50366ae8ca66416080738634eb65ae97
---

diff --git a/typo3/sysext/backend/Classes/View/ValueFormatter/FlexFormValueFormatter.php b/typo3/sysext/backend/Classes/View/ValueFormatter/FlexFormValueFormatter.php
index 780e0ac..41fe875 100644
--- a/typo3/sysext/backend/Classes/View/ValueFormatter/FlexFormValueFormatter.php
+++ b/typo3/sysext/backend/Classes/View/ValueFormatter/FlexFormValueFormatter.php
@@ -45,7 +45,11 @@
             return '';
         }
 
-        $record = BackendUtility::getRecord($tableName, $uid) ?? [];
+        $record = BackendUtility::getRecord($tableName, $uid);
+        if (is_null($record)) {
+            // Record is already deleted
+            return '';
+        }
 
         // Get FlexForm data and structure
         $flexFormDataArray = GeneralUtility::xml2array($value);
