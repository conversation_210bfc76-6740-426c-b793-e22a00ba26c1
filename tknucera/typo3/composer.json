{"name": "mogic/tknucera-typo3", "description": "tknucera Website TYPO3", "type": "project", "license": "GPL-2.0-or-later", "repositories": [{"type": "path", "url": "packages/*"}, {"type": "vcs", "url": "https://github.com/mogic-le/aus_driver_amazon_s3"}], "config": {"allow-plugins": {"typo3/class-alias-loader": true, "typo3/cms-composer-installers": true, "php-http/discovery": true, "cweagans/composer-patches": true}, "platform": {"php": "8.2"}, "sort-packages": true}, "extra": {"patches": {"typo3/cms-backend": {"#102656: Record history exception": "patches/typo3-cms-backend-102656-history.patch"}, "typo3/cms-core": {"#103215 Duplicate error page content": "patches/typo3-cms-core-103215-duplicate-error-content.patch"}}}, "require": {"andersundsehr/aus-driver-amazon-s3": "dev-mogic-master", "b13/container": "^2.3", "b13/masi": "*", "cweagans/composer-patches": "^1.7", "fluidtypo3/vhs": "*", "fullstackfreelancer/ce-timeline": "^4.0", "helhum/typo3-console": "*", "ichhabrecht/content-defender": "*", "in2code/powermail": "^12.1", "jigal/t3adminer": "^12.0", "kamermans/guzzle-oauth2-subscriber": "^1.0", "mogic/tknucera": "@dev", "netresearch/jsonmapper": "^4.4", "networkteam/sentry-client": "*", "nitsan/ns-sharethis": "^12.0", "php-http/guzzle7-adapter": "^1.0", "phpoffice/phpspreadsheet": "^2.1", "sitegeist/fluid-components": "3.6", "ssch/typo3-encore": "^5.0", "typo3/cms-backend": "^12.4.0", "typo3/cms-belog": "^12.4.0", "typo3/cms-beuser": "^12.4.0", "typo3/cms-core": "^12.4.0", "typo3/cms-dashboard": "^12.4.0", "typo3/cms-extbase": "^12.4.0", "typo3/cms-extensionmanager": "^12.4.0", "typo3/cms-filelist": "^12.4.0", "typo3/cms-fluid": "^12.4.0", "typo3/cms-fluid-styled-content": "^12.4.0", "typo3/cms-frontend": "^12.4.0", "typo3/cms-impexp": "^12.4", "typo3/cms-info": "^12.4.0", "typo3/cms-install": "^12.4.0", "typo3/cms-linkvalidator": "^12.4.0", "typo3/cms-lowlevel": "^12.4", "typo3/cms-reactions": "^12.4.0", "typo3/cms-redirects": "^12.4.0", "typo3/cms-rte-ckeditor": "^12.4.0", "typo3/cms-scheduler": "^12.4", "typo3/cms-seo": "^12.4.0", "typo3/cms-setup": "^12.4.0", "typo3/cms-sys-note": "^12.4", "typo3/cms-t3editor": "^12.4.0", "typo3/cms-tstemplate": "^12.4.0", "typo3/cms-viewpage": "^12.4.0", "typo3/cms-webhooks": "^12.4.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "^3.46", "mogic/mogic-phpcs": "dev-master", "squizlabs/php_codesniffer": "^3.8"}}