<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
  <file source-language="en" datatype="plaintext" original="EXT:powermail/Resources/Private/Language/locallang.xlf" date="2014-05-02T12:00:00Z" product-name="powermail" target-language="de">
    <header/>
    <body>
      <trans-unit id="pleaseChoose" resname="pleaseChoose" approved="yes">
        <source>Please choose...</source>
        <target state="final">Bitte wählen...</target>
      </trans-unit>
      <trans-unit id="back" resname="back" approved="yes">
        <source>Go back</source>
        <target state="final">Zur letzten Ansicht</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.senderName" resname="\In2code\Powermail\Domain\Model\Mail.senderName" approved="yes">
        <source>Senders Name</source>
        <target state="final">Absender Name</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.senderMail" resname="\In2code\Powermail\Domain\Model\Mail.senderMail" approved="yes">
        <source>Senders Email</source>
        <target state="final">Absender E-Mail</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.subject" resname="\In2code\Powermail\Domain\Model\Mail.subject" approved="yes">
        <source>Subject</source>
        <target state="final">Betreff</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.receiverMail" resname="\In2code\Powermail\Domain\Model\Mail.receiverMail" approved="yes">
        <source>Receivers Email</source>
        <target state="final">Empfänger E-Mail</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.body" resname="\In2code\Powermail\Domain\Model\Mail.body" approved="yes">
        <source>Email Text</source>
        <target state="final">E-Mail Text</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.feuser" resname="\In2code\Powermail\Domain\Model\Mail.feuser" approved="yes">
        <source>Frontend User</source>
        <target state="final">Frontend Benutzer</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.senderIp" resname="\In2code\Powermail\Domain\Model\Mail.senderIp" approved="yes">
        <source>Senders IP-Address</source>
        <target state="final">IP-Adresse des Absenders</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.userAgent" resname="\In2code\Powermail\Domain\Model\Mail.userAgent" approved="yes">
        <source>Senders Browser-Signature</source>
        <target state="final">Browser-Kennung des Absenders</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.spamFactor" resname="\In2code\Powermail\Domain\Model\Mail.spamFactor" approved="yes">
        <source>Spamfactor</source>
        <target state="final">Spamfaktor</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.time" resname="\In2code\Powermail\Domain\Model\Mail.time" approved="yes">
        <source>Time for form submit</source>
        <target state="final">Zeit zum Ausfüllen des Formulars</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.crdate" resname="\In2code\Powermail\Domain\Model\Mail.crdate" approved="yes">
        <source>Date</source>
        <target state="final">Datum</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.date" resname="\In2code\Powermail\Domain\Model\Mail.date" approved="yes">
        <source>Date</source>
        <target state="final">Datum</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.datetime" resname="\In2code\Powermail\Domain\Model\Mail.datetime" approved="yes">
        <source>Time</source>
        <target state="final">Uhrzeit</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingRefererDomain" resname="\In2code\Powermail\Domain\Model\Mail.marketingRefererDomain" approved="yes">
        <source>Referer Domain</source>
        <target state="final">Referer Domain</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingReferer" resname="\In2code\Powermail\Domain\Model\Mail.marketingReferer" approved="yes">
        <source>Referer URI</source>
        <target state="final">Referer URL</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingCountry" resname="\In2code\Powermail\Domain\Model\Mail.marketingCountry" approved="yes">
        <source>Visitors Country</source>
        <target state="final">Land des Besuchers</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingMobileDevice" resname="\In2code\Powermail\Domain\Model\Mail.marketingMobileDevice" approved="yes">
        <source>Visitor uses a Mobile Device</source>
        <target state="final">Besucher benutzt ein mobiles Endgerät</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingFrontendLanguage" resname="\In2code\Powermail\Domain\Model\Mail.marketingFrontendLanguage" approved="yes">
        <source>Website Language</source>
        <target state="final">Webseitensprache</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingBrowserLanguage" resname="\In2code\Powermail\Domain\Model\Mail.marketingBrowserLanguage" approved="yes">
        <source>Browser Language</source>
        <target state="final">Browser Sprache</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.marketingPageFunnel" resname="\In2code\Powermail\Domain\Model\Mail.marketingPageFunnel" approved="yes">
        <source>Page Funnel</source>
        <target state="final">Diese Seitenaufrufe führten zu dieser Mail (pagefunnel)</target>
      </trans-unit>
      <trans-unit id="\In2code\Powermail\Domain\Model\Mail.hidden" resname="\In2code\Powermail\Domain\Model\Mail.hidden" approved="yes">
        <source>Disabled</source>
        <target state="final">Deaktiviert</target>
      </trans-unit>
      <trans-unit id="note_html" resname="note_html" approved="yes">
        <source>For security reasons, it is not allowed to show html tags by default. You can enable it with TypoScript (see powermail manual), but be aware, that editors can also add HTML and XSS.</source>
        <target state="final">Aus Sicherheitsgründen ist es nicht erlaubt, in der Standardkonfiguration HTML anzuzeigen. Dies kann jedoch über TypoScript aktiviert werden (siehe powermail Handbuch). Bitte denken Sie jedoch daran, dass auch Redakteure HTML und XSS verwenden können.</target>
      </trans-unit>
      <trans-unit id="mail_created" resname="mail_created" approved="yes">
        <source>Mail successfully sent!</source>
        <target state="final">Mail wurde erfolgreich versandt!</target>
      </trans-unit>
      <trans-unit id="mail_created_failure" resname="mail_created_failure" approved="yes">
        <source>Failure, mail could not be sent!</source>
        <target state="final">Fehler, E-Mail konnte nicht versandt werden!</target>
      </trans-unit>
      <trans-unit id="confirmation_message" resname="confirmation_message" approved="yes">
        <source>Are these values correct?</source>
        <target state="final">Sind diese Eingaben korrekt?</target>
      </trans-unit>
      <trans-unit id="confirmation_next" resname="confirmation_next" approved="yes">
        <source>Next</source>
        <target state="final">Weiter</target>
      </trans-unit>
      <trans-unit id="confirmation_prev" resname="confirmation_prev" approved="yes">
        <source>Previous</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="optin_seeMail" resname="optin_seeMail" approved="yes">
        <source>Please have a look into your mail account and confirm this action.</source>
        <target state="final">Bitte überprüfen Sie Ihr E-Mail-Postfach und bestätigen Sie diese Aktion.</target>
      </trans-unit>
      <trans-unit id="optin_subject" resname="optin_subject" approved="yes">
        <source>Please confirm your Email</source>
        <target state="final">Bitte bestätigen Sie Ihre E-Mail</target>
      </trans-unit>
      <trans-unit id="optin_mail_entries" resname="optin_mail_entries" approved="yes">
        <source>These are your given entries:</source>
        <target state="final">Ihre Eingaben:</target>
      </trans-unit>
      <trans-unit id="optin_mail_link" resname="optin_mail_link" approved="yes">
        <source>Please confirm with a click:</source>
        <target state="final">Bitte bestätigen Sie Ihre E-Mail:</target>
      </trans-unit>
      <trans-unit id="optin_mail_confirm_failed" resname="optin_mail_confirm_failed" approved="yes">
        <source>The given Link is not correct</source>
        <target state="final">Der eingegebene Link ist ungültig</target>
      </trans-unit>
      <trans-unit id="optin_mail_confirm_done" resname="optin_mail_confirm_done" approved="yes">
        <source>The mail was already confirmed</source>
        <target state="final">Die Nachricht wurde bereits bestätigt</target>
      </trans-unit>
      <trans-unit id="disclaimer_mail_deleted" resname="disclaimer_mail_deleted" approved="yes">
        <source>Mail was successfully deleted</source>
        <target state="final">Die Nachricht wurde erfolgreich gelöscht</target>
      </trans-unit>
      <trans-unit id="disclaimer_mailmessage" resname="disclaimer_mailmessage" approved="yes">
        <source>Was this email a mistake and you want to delete your request?</source>
        <target state="final">Haben Sie diese E-Mail nicht angefordert und möchten Ihre Daten löschen lassen?</target>
      </trans-unit>
      <trans-unit id="disclaimer_mailmessage_link" resname="disclaimer_mailmessage_link" approved="yes">
        <source>Delete all email data.</source>
        <target state="final">E-Mail Daten vollständig löschen.</target>
      </trans-unit>
      <trans-unit id="disclaimed_subject" resname="disclaimed_subject" approved="yes">
        <source>Email was just disclaimed</source>
        <target state="final">Eine E-Mail wurde vom Benutzer zurückgezogen</target>
      </trans-unit>
      <trans-unit id="disclaimed_entries" resname="disclaimed_entries" approved="yes">
        <source>This data was completely disclaimed:</source>
        <target state="final">Diese Angaben wurden vollständig zurückgezogen:</target>
      </trans-unit>
      <trans-unit id="error_mail_not_created" resname="error_mail_not_created" approved="yes">
        <source>Error, Email could not be sent correctly!</source>
        <target state="final">Fehler, die Nachricht konnte leider nicht korrekt versendet werden!</target>
      </trans-unit>
      <trans-unit id="error_no_typoscript" resname="error_no_typoscript" approved="yes">
        <source>TypoScript settings are missing. Did you include the related static templates?</source>
        <target state="final">TypoScript benötigt. Sind Sie sicher, dass Sie die erforderlichen Static Templates eingebunden haben?</target>
      </trans-unit>
      <trans-unit id="error_no_sender_name" resname="error_no_sender_name" approved="yes">
        <source>No Sendername</source>
        <target state="final">Kein Absendername</target>
      </trans-unit>
      <trans-unit id="error_no_sender_email" resname="error_no_sender_email" approved="yes">
        <source>noreply</source>
        <target state="final">nichtantworten</target>
      </trans-unit>
      <trans-unit id="error_no_form" resname="error_no_form" approved="yes">
        <source>No form to show</source>
        <target state="final">Kein Formular zur Anzeige</target>
      </trans-unit>
      <trans-unit id="error_mail_not_deleted" resname="error_mail_not_deleted" approved="yes">
        <source>Mail could not be deleted. Wrong link or mail was probably already removed from the system.</source>
        <target state="final">Die Nachricht konnte nicht gelöscht werden. Fehlerhafter Link oder Nachricht wurde bereits zuvor gelöscht.</target>
      </trans-unit>
      <trans-unit id="error_wrong_captcha" resname="error_wrong_captcha" approved="yes">
        <source>Wrong Captcha value, please try again!</source>
        <target state="final">Falschen Code eingetragen, bitte erneut versuchen!</target>
      </trans-unit>
      <trans-unit id="error_mail_not_deleted" resname="error_mail_not_deleted" approved="yes">
        <source>Mail could not be deleted. Wrong link or mail was probably already removed from the system.</source>
        <target state="final">Die Nachricht konnte nicht gelöscht werden. Fehlerhafter Link oder Nachricht wurde bereits zuvor gelöscht.</target>
      </trans-unit>
      <trans-unit id="validationerror_mandatory" resname="validationerror_mandatory" approved="yes">
        <source>This field must be filled!</source>
        <target state="final">Dieses Feld muss ausgefüllt werden!</target>
      </trans-unit>
      <trans-unit id="validationerror_mandatory_multi" resname="validationerror_mandatory_multi" approved="yes">
        <source>One of these fields must be filled!</source>
        <target state="final">Eines dieser Felder muss ausgefüllt werden!</target>
      </trans-unit>
      <trans-unit id="validationerror_captcha" resname="validationerror_captcha" approved="yes">
        <source>Wrong captcha code entered - please try again!</source>
        <target state="final">Falscher Captcha Code eingetragen, bitte erneut versuchen!</target>
      </trans-unit>
      <trans-unit id="validationerror_password" resname="validationerror_password" approved="yes">
        <source>Both Password-Fields are not equal!</source>
        <target state="final">Die beiden Passwort-Felder enthalten nicht den gleichen Wert!</target>
      </trans-unit>
      <trans-unit id="validationerror_unique" resname="validationerror_unique" approved="yes">
        <source>This value is already in use - please use a new one!</source>
        <target state="final">Der Wert wurde bereits verwendet - bitte versuchen Sie einen anderen!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation" resname="validationerror_validation" approved="yes">
        <source>Your input is not allowed!</source>
        <target state="final">Ihre Eingabe ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.1" resname="validationerror_validation.1" approved="yes">
        <source>This is not a valid email address!</source>
        <target state="final">Keine gültige E-Mail-Adresse!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.2" resname="validationerror_validation.2" approved="yes">
        <source>This is not a valid URL!</source>
        <target state="final">Keine gültige URL!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.3" resname="validationerror_validation.3" approved="yes">
        <source>This is not a valid Phone Number!</source>
        <target state="final">Keine gültige Telefonnummer!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.4" resname="validationerror_validation.4" approved="yes">
        <source>Please insert numbers only!</source>
        <target state="final">Bitte nur Nummern eintragen!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.5" resname="validationerror_validation.5" approved="yes">
        <source>Please insert letters only!</source>
        <target state="final">Bitte nur Buchstaben eintragen!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.6" resname="validationerror_validation.6" approved="yes">
        <source>Please increase number!</source>
        <target state="final">Bitte höhere Zahl eintragen!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.7" resname="validationerror_validation.7" approved="yes">
        <source>Please decrease number!</source>
        <target state="final">Bitte niedrigere Zahl eintragen!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.8" resname="validationerror_validation.8" approved="yes">
        <source>Number too high or too low!</source>
        <target state="final">Zahl zu groß oder zu klein!</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.9" resname="validationerror_validation.9" approved="yes">
        <source>Your input is not valid (too many or too less signs)</source>
        <target state="final">Der Wert ist nicht zulässig (zu viele oder zu wenig Zeichen)</target>
      </trans-unit>
      <trans-unit id="validationerror_validation.10" resname="validationerror_validation.10" approved="yes">
        <source>Error in validation!</source>
        <target state="final">Fehler in Validierung!</target>
      </trans-unit>
      <trans-unit id="validationerror_upload_extension" resname="validationerror_upload_extension" approved="yes">
        <source>It's not allowed to upload a file with this extension!</source>
        <target state="final">Der Dateityp ist nicht erlaubt, bitte versuchen Sie einen anderen Typ!</target>
      </trans-unit>
      <trans-unit id="validationerror_upload_size" resname="validationerror_upload_size" approved="yes">
        <source>The file, that you want to upload is too large!</source>
        <target state="final">Die ausgewählte Datei ist zu groß!</target>
      </trans-unit>
      <trans-unit id="validationerror_upload_error" resname="validationerror_upload_error" approved="yes">
        <source>Upload failed, please check permissions on the server!</source>
        <target state="final">Upload fehlgeschlagen, bitte Berechtigungen auf Server überprüfen!</target>
      </trans-unit>
      <trans-unit id="validationerror_spam" resname="validationerror_spam" approved="yes">
        <source>Spam recognized:</source>
        <target state="final">Spam in Nachricht vermutet:</target>
      </trans-unit>
      <trans-unit id="validationerror_spam_details" resname="validationerror_spam_details" approved="yes">
        <source>Spam chance in this message!</source>
        <target state="final">Spam-Wahrscheinlichkeit in dieser Nachricht!</target>
      </trans-unit>
      <trans-unit id="writePasswordAgain" resname="writePasswordAgain" approved="yes">
        <source>Please write again</source>
        <target state="final">Bitte erneut eintragen</target>
      </trans-unit>
      <trans-unit id="datepicker_format_date" resname="datepicker_format_date" approved="yes">
        <source>Y-m-d</source>
        <target state="final">d.m.Y</target>
      </trans-unit>
      <trans-unit id="datepicker_format_datetime" resname="datepicker_format_datetime" approved="yes">
        <source>Y-m-d H:i</source>
        <target state="final">d.m.Y H:i</target>
      </trans-unit>
      <trans-unit id="datepicker_format_time" resname="datepicker_format_time" approved="yes">
        <source>H:i</source>
        <target state="final">H:i</target>
      </trans-unit>
      <trans-unit id="datepicker_day_so" resname="datepicker_day_so" approved="yes">
        <source>So</source>
        <target state="final">So</target>
      </trans-unit>
      <trans-unit id="datepicker_day_mo" resname="datepicker_day_mo" approved="yes">
        <source>Mo</source>
        <target state="final">Mo</target>
      </trans-unit>
      <trans-unit id="datepicker_day_tu" resname="datepicker_day_tu" approved="yes">
        <source>Tu</source>
        <target state="final">Di</target>
      </trans-unit>
      <trans-unit id="datepicker_day_we" resname="datepicker_day_we" approved="yes">
        <source>We</source>
        <target state="final">Mi</target>
      </trans-unit>
      <trans-unit id="datepicker_day_th" resname="datepicker_day_th" approved="yes">
        <source>Th</source>
        <target state="final">Do</target>
      </trans-unit>
      <trans-unit id="datepicker_day_fr" resname="datepicker_day_fr" approved="yes">
        <source>Fr</source>
        <target state="final">Fr</target>
      </trans-unit>
      <trans-unit id="datepicker_day_sa" resname="datepicker_day_sa" approved="yes">
        <source>Sa</source>
        <target state="final">Sa</target>
      </trans-unit>
      <trans-unit id="datepicker_month_jan" resname="datepicker_month_jan" approved="yes">
        <source>January</source>
        <target state="final">Januar</target>
      </trans-unit>
      <trans-unit id="datepicker_month_feb" resname="datepicker_month_feb" approved="yes">
        <source>February</source>
        <target state="final">Februar</target>
      </trans-unit>
      <trans-unit id="datepicker_month_mar" resname="datepicker_month_mar" approved="yes">
        <source>March</source>
        <target state="final">März</target>
      </trans-unit>
      <trans-unit id="datepicker_month_apr" resname="datepicker_month_apr" approved="yes">
        <source>April</source>
        <target state="final">April</target>
      </trans-unit>
      <trans-unit id="datepicker_month_may" resname="datepicker_month_may" approved="yes">
        <source>May</source>
        <target state="final">Mai</target>
      </trans-unit>
      <trans-unit id="datepicker_month_jun" resname="datepicker_month_jun" approved="yes">
        <source>June</source>
        <target state="final">Juni</target>
      </trans-unit>
      <trans-unit id="datepicker_month_jul" resname="datepicker_month_jul" approved="yes">
        <source>July</source>
        <target state="final">Juli</target>
      </trans-unit>
      <trans-unit id="datepicker_month_aug" resname="datepicker_month_aug" approved="yes">
        <source>August</source>
        <target state="final">August</target>
      </trans-unit>
      <trans-unit id="datepicker_month_sep" resname="datepicker_month_sep" approved="yes">
        <source>September</source>
        <target state="final">September</target>
      </trans-unit>
      <trans-unit id="datepicker_month_oct" resname="datepicker_month_oct" approved="yes">
        <source>October</source>
        <target state="final">Oktober</target>
      </trans-unit>
      <trans-unit id="datepicker_month_nov" resname="datepicker_month_nov" approved="yes">
        <source>November</source>
        <target state="final">November</target>
      </trans-unit>
      <trans-unit id="datepicker_month_dec" resname="datepicker_month_dec" approved="yes">
        <source>December</source>
        <target state="final">Dezember</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckCheckboxes" resname="JsValidationCheckCheckboxes" approved="yes">
        <source>Please check min. 1</source>
        <target state="final">Bitte eine Checkbox anhaken</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckInteger" resname="JsValidationCheckInteger" approved="yes">
        <source>Not a valid number</source>
        <target state="final">Keine gültige Nummer</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckRequired" resname="JsValidationCheckRequired" approved="yes">
        <source>This field is required</source>
        <target state="final">Dies ist ein Pflichtfeld</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckRequiredOption" resname="JsValidationCheckRequiredOption" approved="yes">
        <source>Please select an option</source>
        <target state="final">Bitte eine Option wählen</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckEmail" resname="JsValidationCheckEmail" approved="yes">
        <source>Invalid Email</source>
        <target state="final">Keine gültige E-Mail</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckUrl" resname="JsValidationCheckUrl" approved="yes">
        <source>Invalid URL</source>
        <target state="final">Keine gültige URL</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckPhone" resname="JsValidationCheckPhone" approved="yes">
        <source>Invalid Phone No</source>
        <target state="final">Keine gültige Telefonnummer</target>
      </trans-unit>
      <trans-unit id="JsValidationCheckLetters" resname="JsValidationCheckLetters" approved="yes">
        <source>Only Letters allowed</source>
        <target state="final">Ungültige Zeichen</target>
      </trans-unit>
      <trans-unit id="BackendSelectionList" resname="BackendSelectionList" approved="yes">
        <source>Mail List</source>
        <target state="final">E-Mail Auflistung</target>
      </trans-unit>
      <trans-unit id="BackendSelectionReporting" resname="BackendSelectionReporting" approved="yes">
        <source>Reporting</source>
        <target state="final">Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendSelectionReportingForm" resname="BackendSelectionReportingForm" approved="yes">
        <source>Reporting (Form)</source>
        <target state="final">Auswertung (Formular)</target>
      </trans-unit>
      <trans-unit id="BackendSelectionReportingMarketing" resname="BackendSelectionReportingMarketing" approved="yes">
        <source>Reporting (Marketing)</source>
        <target state="final">Auswertung (Marketing)</target>
      </trans-unit>
      <trans-unit id="BackendSelectionTools" resname="BackendSelectionTools" approved="yes">
        <source>Tools</source>
        <target state="final">Werkzeuge</target>
      </trans-unit>
      <trans-unit id="BackendSelectionOverview" resname="BackendSelectionOverview" approved="yes">
        <source>Form Overview</source>
        <target state="final">Formular-Übersicht</target>
      </trans-unit>
      <trans-unit id="BackendSelectionCheck" resname="BackendSelectionCheck" approved="yes">
        <source>Function Check</source>
        <target state="final">Funktions-Check</target>
      </trans-unit>
      <trans-unit id="BackendSelectionConverterTitle" resname="BackendSelectionConverterTitle" approved="yes">
        <source>Form Converter</source>
        <target state="final">Formular-Konverter</target>
      </trans-unit>
      <trans-unit id="BackendSelectionConverterTitle2" resname="BackendSelectionConverterTitle2" approved="yes">
        <source>Converts all Powermail 1.x to 2.x Forms</source>
        <target state="final">Konvertiert alle alten Powermail 1.x zu 2.x Formulare</target>
      </trans-unit>
      <trans-unit id="BackendSelectionConverterTitle3" resname="BackendSelectionConverterTitle3" approved="yes">
        <source>Old records (from Tables tx_powermail_fieldsets, tx_powermail_fields and tt_content) will be flagged with deleted=1. New Records will be generated (Tables tx_powermail_domain_model_form, tx_powermail_domain_model_page, tx_powermail_domain_model_field, tt_content)</source>
        <target state="final">Alte Datensätze (aus Tabellen tx_powermail_fieldsets, tx_powermail_fields und tt_content) werden mit dem Flag deleted=1 versehen. Neue Datensätze werden angelegt (Tabellen tx_powermail_domain_model_form, tx_powermail_domain_model_page, tx_powermail_domain_model_field, tt_content)</target>
      </trans-unit>
      <trans-unit id="BackendSelectionConverterTitle4" resname="BackendSelectionConverterTitle4" approved="yes">
        <source>Attention: Please backup your database before converting. This step is irreversible.</source>
        <target state="final">Achtung: Wir empfehlen dringend, ein Backup vor der Konvertierung zu erstellen. Eine fehlgeschlagene Konvertierung kann in der Regel nicht rückgängig gemacht werden.</target>
      </trans-unit>
      <trans-unit id="BackendListMultiselectLabel" resname="BackendListMultiselectLabel" approved="yes">
        <source>record(s) selected:</source>
        <target state="final">Mail(s) ausgewählt:</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormSave" resname="BackendConverterFormSave" approved="yes">
        <source>Save new forms on page with PID</source>
        <target state="final">Neue Formulare auf Seite mit PID speichern</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDefaultReceiverName" resname="BackendConverterFormDefaultReceiverName" approved="yes">
        <source>Default Receiver Name</source>
        <target state="final">Standard Name des Empfängers</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDefaultSenderNameConfirmationMail" resname="BackendConverterFormDefaultSenderNameConfirmationMail" approved="yes">
        <source>Default Sender Name for Confirmation Mail</source>
        <target state="final">Standard Sender-Name für Bestätigungs-E-Mail</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDefaultSenderMailConfirmationMail" resname="BackendConverterFormDefaultSenderMailConfirmationMail" approved="yes">
        <source>Default Sender Email for Confirmation Mail</source>
        <target state="final">Standard Sender-E-Mail für Bestätigungs-E-Mail</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDefaultParseFunc" resname="BackendConverterFormDefaultParseFunc" approved="yes">
        <source>ParseFunc Path in TypoScript (without prefix lib.).</source>
        <target state="final">ParseFunc Pfad in TypoScript (ohne Präfix lib.).</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDefaultParseFuncTroubleText" resname="BackendConverterFormDefaultParseFuncTroubleText" approved="yes">
        <source>If you have trouble with form converting, you can leave this value empty.</source>
        <target state="final">Wenn beim Konvertieren Probleme auftreten, kann dieses Feld komplett leer gelassen werden.</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormHidden" resname="BackendConverterFormHidden" approved="yes">
        <source>Ignore Old Hidden Forms</source>
        <target state="final">Versteckte Formulare nicht beachten</target>
      </trans-unit>
      <trans-unit id="BackendConverterFormDryrun" resname="BackendConverterFormDryrun" approved="yes">
        <source>Do only a Test Run (no records will be generated or deleted)</source>
        <target state="final">Testlauf (es werden keine Datensätze gelöscht oder erstellt).</target>
      </trans-unit>
      <trans-unit id="BackendConverterOldForms" resname="BackendConverterOldForms" approved="yes">
        <source>Old Forms (tt_content)</source>
        <target state="final">Alte Formulare (tt_content)</target>
      </trans-unit>
      <trans-unit id="BackendConverterLanguage" resname="BackendConverterLanguage" approved="yes">
        <source>Language</source>
        <target state="final">Sprache</target>
      </trans-unit>
      <trans-unit id="BackendConverterTitle" resname="BackendConverterTitle" approved="yes">
        <source>Title</source>
        <target state="final">Bezeichnung</target>
      </trans-unit>
      <trans-unit id="BackendConverterRecipient" resname="BackendConverterRecipient" approved="yes">
        <source>Recipient</source>
        <target state="final">Empfänger</target>
      </trans-unit>
      <trans-unit id="BackendConverterSubject" resname="BackendConverterSubject" approved="yes">
        <source>Subject</source>
        <target state="final">Betreff</target>
      </trans-unit>
      <trans-unit id="BackendConverterFieldsetAmount" resname="BackendConverterFieldsetAmount" approved="yes">
        <source>Number of Fieldsets</source>
        <target state="final">Anzahl Fieldsets</target>
      </trans-unit>
      <trans-unit id="BackendConverterConfirmationPage" resname="BackendConverterConfirmationPage" approved="yes">
        <source>Confirmation Page</source>
        <target state="final">Bestätigungsseite</target>
      </trans-unit>
      <trans-unit id="BackendConverterMultiple" resname="BackendConverterMultiple" approved="yes">
        <source>Multiple</source>
        <target state="final">Mehrschrittformular</target>
      </trans-unit>
      <trans-unit id="BackendConverterNoOldForms" resname="BackendConverterNoOldForms" approved="yes">
        <source>No old Forms found</source>
        <target state="final">Keine alten Formulare gefunden</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateTitle2" resname="BackendConverterUpdateTitle2" approved="yes">
        <source>Overview: Converted Forms</source>
        <target state="final">Übersicht: Konvertierte Formulare</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateMessageOk" resname="BackendConverterUpdateMessageOk" approved="yes">
        <source>Form(s) successfully generated!</source>
        <target state="final">Formular(e) erfolgreich erstellt!</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateMessageNote" resname="BackendConverterUpdateMessageNote" approved="yes">
        <source>Form(s) could be migrated</source>
        <target state="final">Formular(e) können erzeugt werden</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFormUidAutogenerated" resname="BackendConverterUpdateFormUidAutogenerated" approved="yes">
        <source>[auto]</source>
        <target state="final">[auto]</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFormTitle" resname="BackendConverterUpdateFormTitle" approved="yes">
        <source>Title</source>
        <target state="final">Bezeichnung</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFormHidden" resname="BackendConverterUpdateFormHidden" approved="yes">
        <source>Hidden</source>
        <target state="final">Versteckt</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFormPageAmount" resname="BackendConverterUpdateFormPageAmount" approved="yes">
        <source>Page Amount</source>
        <target state="final">Anzahl Seiten</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFormPages" resname="BackendConverterUpdateFormPages" approved="yes">
        <source>Pages</source>
        <target state="final">Seiten</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageUid" resname="BackendConverterUpdatePageUid" approved="yes">
        <source>Uid</source>
        <target state="final">Uid</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageUidAutogenerated" resname="BackendConverterUpdatePageUidAutogenerated" approved="yes">
        <source>[auto]</source>
        <target state="final">[auto]</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageTitle" resname="BackendConverterUpdatePageTitle" approved="yes">
        <source>Title</source>
        <target state="final">Bezeichnung</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageLayout" resname="BackendConverterUpdatePageLayout" approved="yes">
        <source>Layout</source>
        <target state="final">Layout</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageFieldAmount" resname="BackendConverterUpdatePageFieldAmount" approved="yes">
        <source>Field Amount</source>
        <target state="final">Anzahl Felder</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdatePageFields" resname="BackendConverterUpdatePageFields" approved="yes">
        <source>Fields</source>
        <target state="final">Felder</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFieldUidAutogenerated" resname="BackendConverterUpdateFieldUidAutogenerated" approved="yes">
        <source>[auto]</source>
        <target state="final">[auto]</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFieldTitle" resname="BackendConverterUpdateFieldTitle" approved="yes">
        <source>Title</source>
        <target state="final">Bezeichnung</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateFieldProperties" resname="BackendConverterUpdateFieldProperties" approved="yes">
        <source>Properties</source>
        <target state="final">Eigenschaften</target>
      </trans-unit>
      <trans-unit id="BackendConverterUpdateNoFormsConverted" resname="BackendConverterUpdateNoFormsConverted" approved="yes">
        <source>No Forms converted</source>
        <target state="final">Keine Formulare konvertiert</target>
      </trans-unit>
      <trans-unit id="BackendCheckTitle" resname="BackendCheckTitle" approved="yes">
        <source>Powermail Check</source>
        <target state="final">Powermail Funktionstests</target>
      </trans-unit>
      <trans-unit id="BackendCheckTryToFix" resname="BackendCheckTryToFix" approved="yes">
        <source>Solve problem</source>
        <target state="final">Problem beheben</target>
      </trans-unit>
      <trans-unit id="BackendCheckSessionTitle" resname="BackendCheckSessionTitle" approved="yes">
        <source>Frontend Session Check</source>
        <target state="final">Frontend Session Check</target>
      </trans-unit>
      <trans-unit id="BackendCheckSessionCorrect" resname="BackendCheckSessionCorrect" approved="yes">
        <source>Frontend Sessions are working properly by using $GLOBALS['TSFE']-&gt;fe_user-&gt;getKey() and -&gt;setKey()</source>
        <target state="final">Frontend Sessions funktionieren einwandfrei - Nutzung von $GLOBALS['TSFE']-&gt;fe_user-&gt;getKey() und -&gt;setKey()</target>
      </trans-unit>
      <trans-unit id="BackendCheckSessionFailed" resname="BackendCheckSessionFailed" approved="yes">
        <source>Frontend Sessions are not working properly by using $GLOBALS['TSFE']-&gt;fe_user-&gt;getKey() and -&gt;setKey() - please ask your Administrator</source>
        <target state="final">Frontend Sessions funktionieren nicht richtig - Nutzung von $GLOBALS['TSFE']-&gt;fe_user-&gt;getKey() und -&gt;setKey() - bitte Administrator fragen</target>
      </trans-unit>
      <trans-unit id="BackendCheckExtMngConfigTitle" resname="BackendCheckExtMngConfigTitle" approved="yes">
        <source>Extension Manager Updated</source>
        <target state="final">Aktualisierter Extension Manager</target>
      </trans-unit>
      <trans-unit id="BackendCheckExtMngConfigCorrect" resname="BackendCheckExtMngConfigCorrect" approved="yes">
        <source>Extension is correct installed - all settings available from localconf.php</source>
        <target state="final">Extension wurde korrekt installiert - alle Einstellungen aus der localconf.php sind lesbar</target>
      </trans-unit>
      <trans-unit id="BackendCheckExtMngConfigFailed" resname="BackendCheckExtMngConfigFailed" approved="yes">
        <source>Extension was not correct installed - please click the update button in the Extension Manager</source>
        <target state="final">Extension wurde nicht korrekt installiert - bitte drücken Sie den Update Button im Extension Manager</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionTitle" resname="BackendCheckVersionTitle" approved="yes">
        <source>Version</source>
        <target state="final">Version</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionLastUpdate" resname="BackendCheckVersionLastUpdate" approved="yes">
        <source>Last Extension Manager update</source>
        <target state="final">Letzte Aktualisierung des Extension Managers</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionNoInformation" resname="BackendCheckVersionNoInformation" approved="yes">
        <source>It could not be determined whether this version is secure or unsecure. Please update the extensions in the Extension Manager.</source>
        <target state="final">Es konnte nicht festgestellt werden, ob die installierte Version von powermail sicher oder unsicher ist. Bitte die Daten des Extension Managers aktualisieren um weitere Informationen zu erhalten.</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionUpToDate" resname="BackendCheckVersionUpToDate" approved="yes">
        <source>The installed version of powermail is up-to-date</source>
        <target state="final">Die installierte Version von powermail ist aktuell</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionSecurity" resname="BackendCheckVersionSecurity" approved="yes">
        <source>The installed version of powermail contains a known security bug. Please update your powermail extension as soon as possible.</source>
        <target state="final">Die installierte Version von powermail beinhaltet eine bekannte Sicherheitslücke. Bitte aktualisieren Sie powermail zeitnahe.</target>
      </trans-unit>
      <trans-unit id="BackendCheckVersionUpgrade" resname="BackendCheckVersionUpgrade" approved="yes">
        <source>A newer version of powermail is available</source>
        <target state="final">Eine neuere Version von powermail steht zum Download bereit</target>
      </trans-unit>
      <trans-unit id="BackendCheckT3VersionTitle" resname="BackendCheckT3VersionTitle" approved="yes">
        <source>TYPO3 Version</source>
        <target state="final">TYPO3 Version</target>
      </trans-unit>
      <trans-unit id="BackendCheckT3VersionCorrect" resname="BackendCheckT3VersionCorrect" approved="yes">
        <source>The used TYPO3 version is correct for this Powermail version</source>
        <target state="final">Die eingesetzte TYPO3-Version funktioniert mit der eingesetzten Powermail Version</target>
      </trans-unit>
      <trans-unit id="BackendCheckT3VersionFailed" resname="BackendCheckT3VersionFailed" approved="yes">
        <source>Please check the TYPO3 version - maybe Powermail works not properly in this version</source>
        <target state="final">Powermail meldet einen Versionskonflikt mit dieser TYPO3-Version. Eventuell kann es zu Fehlern kommen</target>
      </trans-unit>
      <trans-unit id="BackendCheckUploadsFolderTitle" resname="BackendCheckUploadsFolderTitle" approved="yes">
        <source>Upload Folder</source>
        <target state="final">Upload Verzeichnis</target>
      </trans-unit>
      <trans-unit id="BackendCheckUploadsFolderCorrect" resname="BackendCheckUploadsFolderCorrect" approved="yes">
        <source>Default Upload Folder exists (uploads/tx_powermail/)</source>
        <target state="final">Standard Upload Verzeichnis existiert (uploads/tx_powermail/)</target>
      </trans-unit>
      <trans-unit id="BackendCheckUploadsFolderFailed" resname="BackendCheckUploadsFolderFailed" approved="yes">
        <source>Default Upload Folder doesn't exist (uploads/tx_powermail/)</source>
        <target state="final">Standard Upload Verzeichnis existiert nicht (uploads/tx_powermail/)</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedFormsTitle" resname="BackendCheckWrongLocalizedFormsTitle" approved="yes">
        <source>Localized Forms</source>
        <target state="final">Lokalisierte Formulare</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedFormsCorrect" resname="BackendCheckWrongLocalizedFormsCorrect" approved="yes">
        <source>No failures in forms found on this system</source>
        <target state="final">Keine Probleme auf diesem System gefunden</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedFormsFailed" resname="BackendCheckWrongLocalizedFormsFailed" approved="yes">
        <source>There are localized forms with empty tx_powermail_domain_model_form.pages fields which can lead to problems in rendering localized Forms in Frontend</source>
        <target state="final">Es gibt lokalisierte Formulare mit leeren tx_powermail_domain_model_form.pages Feldern, welche Probleme in der Frontend-Darstellung verursachen können</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedPagesTitle" resname="BackendCheckWrongLocalizedPagesTitle" approved="yes">
        <source>Localized Pages</source>
        <target state="final">Lokalisierte Seiten</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedPagesCorrect" resname="BackendCheckWrongLocalizedPagesCorrect" approved="yes">
        <source>No failures in pages found on this system</source>
        <target state="final">Keine Probleme auf diesem System gefunden</target>
      </trans-unit>
      <trans-unit id="BackendCheckWrongLocalizedPagesFailed" resname="BackendCheckWrongLocalizedPagesFailed" approved="yes">
        <source>There are localized pages with empty tx_powermail_domain_model_page.forms fields which can lead to problems in managing localized pages</source>
        <target state="final">Es gibt lokalisierte Seiten mit leeren tx_powermail_domain_model_page.forms Feldern, was Probleme bei der Bearbeitung verursachen kann</target>
      </trans-unit>
      <trans-unit id="BackendCheckBackupBefore" resname="BackendCheckBackupBefore" approved="yes">
        <source>Note: Please backup all powermail tables before you choose this option!</source>
        <target state="final">Hinweis: Bitte unbedingt alle powermail Tabellen zuvor sichern!</target>
      </trans-unit>
      <trans-unit id="BackendCheckDevelopmentContextTitle" resname="BackendCheckDevelopmentContextTitle" approved="yes">
        <source>Development Context</source>
        <target state="final">Development Context</target>
      </trans-unit>
      <trans-unit id="BackendCheckDevelopmentContextActive" resname="BackendCheckDevelopmentContextActive" approved="yes">
        <source>This TYPO3 instance is running in development context. All mails will be sent to %s only.</source>
        <target state="final">Diese TYPO3-Instanz läuft aktuell im Development Context. Alle E-Mails werden an %s versendet.</target>
      </trans-unit>
      <trans-unit id="BackendCheckDevelopmentContextAvailable" resname="BackendCheckDevelopmentContextAvailable" approved="yes">
        <source>This TYPO3 instance is running in development context. You can use %s in the AdditionalConfiguration.php to enforce powermail to send testmails to this email-address only.</source>
        <target state="final">Diese TYPO3-Instanz läuft aktuell im Development Context. Mit %s in der AdditionalConfiguration.php kann man powermail anweisen, nur noch an diese Formulare zu senden.</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTitle" resname="BackendOverviewTitle" approved="yes">
        <source>Form Overview</source>
        <target state="final">Formular Übersicht</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTitle2" resname="BackendOverviewTitle2" approved="yes">
        <source>All Forms within page</source>
        <target state="final">Alle Formulare der Seite</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTitle3" resname="BackendOverviewTitle3" approved="yes">
        <source>(and its subpages)</source>
        <target state="final">(und deren Unterseiten)</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTitle4" resname="BackendOverviewTitle4" approved="yes">
        <source>Show All Forms</source>
        <target state="final">Zeige Alle Formulare</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTitle5" resname="BackendOverviewTitle5" approved="yes">
        <source>All Forms in this CMS</source>
        <target state="final">Alle Formulare im CMS</target>
      </trans-unit>
      <trans-unit id="BackendOverviewTable" resname="BackendOverviewTable" approved="yes">
        <source>Forms</source>
        <target state="final">Formulare</target>
      </trans-unit>
      <trans-unit id="BackendOverviewCol1" resname="BackendOverviewCol1" approved="yes">
        <source>Form Title</source>
        <target state="final">Formulartitel</target>
      </trans-unit>
      <trans-unit id="BackendOverviewCol2" resname="BackendOverviewCol2" approved="yes">
        <source>Stored on Page</source>
        <target state="final">Gespeichert auf Seite</target>
      </trans-unit>
      <trans-unit id="BackendOverviewCol3" resname="BackendOverviewCol3" approved="yes">
        <source>Used on Page</source>
        <target state="final">Verwendet auf Seite</target>
      </trans-unit>
      <trans-unit id="BackendOverviewCol4" resname="BackendOverviewCol4" approved="yes">
        <source>Powermail Pages</source>
        <target state="final">Powermail Seiten</target>
      </trans-unit>
      <trans-unit id="BackendOverviewCol5" resname="BackendOverviewCol5" approved="yes">
        <source>Powermail Fields</source>
        <target state="final">Powermail Felder</target>
      </trans-unit>
      <trans-unit id="BackendOverviewPages" resname="BackendOverviewPages" approved="yes">
        <source>Page(s)</source>
        <target state="final">Seite(n)</target>
      </trans-unit>
      <trans-unit id="BackendOverviewFields" resname="BackendOverviewFields" approved="yes">
        <source>Field(s)</source>
        <target state="final">Feld(er)</target>
      </trans-unit>
      <trans-unit id="BackendOverviewNoForms" resname="BackendOverviewNoForms" approved="yes">
        <source>No Forms found</source>
        <target state="final">Keine Formulare zum Anzeigen vorhanden</target>
      </trans-unit>
      <trans-unit id="BackendFunctionTitle" resname="BackendFunctionTitle" approved="yes">
        <source>Function-Test Section</source>
        <target state="final">Bereich der Funktionstests</target>
      </trans-unit>
      <trans-unit id="BackendEmailTitle" resname="BackendEmailTitle" approved="yes">
        <source>Test Email Sending</source>
        <target state="final">E-Mail Versand testen</target>
      </trans-unit>
      <trans-unit id="BackendEmailFormTitle" resname="BackendEmailFormTitle" approved="yes">
        <source>Test email generation with powermail</source>
        <target state="final">Powermail E-Mail Versand</target>
      </trans-unit>
      <trans-unit id="BackendEmailFormReceiver" resname="BackendEmailFormReceiver" approved="yes">
        <source>Receiver Email</source>
        <target state="final">Empfänger E-Mail</target>
      </trans-unit>
      <trans-unit id="BackendCheckEmailTitle" resname="BackendCheckEmailTitle" approved="yes">
        <source>Email Sending Status</source>
        <target state="final">E-Mail Versand Status</target>
      </trans-unit>
      <trans-unit id="BackendCheckEmailCorrect" resname="BackendCheckEmailCorrect" approved="yes">
        <source>Email was sent without problems (mail transmitted to mailserver)</source>
        <target state="final">E-Mail erfolgreich versandt (Mail an Mailserver übergeben)</target>
      </trans-unit>
      <trans-unit id="BackendCheckEmailFailed" resname="BackendCheckEmailFailed" approved="yes">
        <source>Email could not be sent properly (ask your Adminstrator)</source>
        <target state="final">E-Mail konnte nicht erfolgreich versandt werden (bitte an Administrator wenden)</target>
      </trans-unit>
      <trans-unit id="BackendCheckStaticTemplateTitle" resname="BackendCheckStaticTemplateTitle" approved="yes">
        <source>TypoScript Static Template</source>
        <target state="final">TypoScript Static Template</target>
      </trans-unit>
      <trans-unit id="BackendCheckStaticTemplateCorrect" resname="BackendCheckStaticTemplateCorrect" approved="yes">
        <source>Static TypoScript Template is available on current page.</source>
        <target state="final">Statisches TypoScript Template ist auf der aktuellen Seite verfügbar.</target>
      </trans-unit>
      <trans-unit id="BackendCheckStaticTemplateFailed" resname="BackendCheckStaticTemplateFailed" approved="yes">
        <source>Static TypoScript Template is not available on current page.</source>
        <target state="final">Statisches TypoScript Template ist auf der aktuellen Seite nicht verfügbar.</target>
      </trans-unit>
      <trans-unit id="BackendCheckComposerModeTitle" resname="BackendCheckComposerModeTitle" approved="yes">
        <source>Composer Mode</source>
        <target state="final">Composer Modus</target>
      </trans-unit>
      <trans-unit id="BackendCheckComposerModeComposer" resname="BackendCheckComposerModeComposer" approved="yes">
        <source>Your TYPO3 is running in Composer Mode</source>
        <target state="final">TYPO3 läuft im Composer Mode</target>
      </trans-unit>
      <trans-unit id="BackendCheckComposerModeClassic" resname="BackendCheckComposerModeClassic" approved="yes">
        <source>Your TYPO3 is running in Classic Mode</source>
        <target state="final">TYPO3 läuft im Classic Mode</target>
      </trans-unit>
      <trans-unit id="BackendListTitle" resname="BackendListTitle" approved="yes">
        <source>Mail Listings</source>
        <target state="final">E-Mail Auflistung</target>
      </trans-unit>
      <trans-unit id="BackendListMails" resname="BackendListMails" approved="yes">
        <source>Mails</source>
        <target state="final">Mails</target>
      </trans-unit>
      <trans-unit id="BackendListMailDelete" resname="BackendListMailDelete" approved="yes">
        <source>Delete Mail</source>
        <target state="final">Mail löschen</target>
      </trans-unit>
      <trans-unit id="BackendListMailDeleteConfirmation" resname="BackendListMailDeleteConfirmation" approved="yes">
        <source>Really Delete This Mail?</source>
        <target state="final">Diese Mail wirklich löschen?</target>
      </trans-unit>
      <trans-unit id="BackendListHelpDetails" resname="BackendListHelpDetails" approved="yes">
        <source>Show Mail Details</source>
        <target state="final">Zeige Details dieser Mail</target>
      </trans-unit>
      <trans-unit id="BackendListHelpEdit" resname="BackendListHelpEdit" approved="yes">
        <source>Edit this Mail</source>
        <target state="final">Diese Mail bearbeiten</target>
      </trans-unit>
      <trans-unit id="BackendListHelpUnhide" resname="BackendListHelpUnhide" approved="yes">
        <source>Activate this Mail</source>
        <target state="final">Diese Mail aktivieren</target>
      </trans-unit>
      <trans-unit id="BackendListHelpHide" resname="BackendListHelpHide" approved="yes">
        <source>Deactivate this Mail</source>
        <target state="final">Diese Mail deaktivieren</target>
      </trans-unit>
      <trans-unit id="BackendListNoMails" resname="BackendListNoMails" approved="yes">
        <source>No Mails to show</source>
        <target state="final">Keine Mails gefunden.</target>
      </trans-unit>
      <trans-unit id="BackendListNoMailsSub" resname="BackendListNoMailsSub" approved="yes">
        <source>Please choose another page or modify your filter settings</source>
        <target state="final">Bitte wählen Sie eine andere Seite oder passen Sie die Filtereinstellungen an.</target>
      </trans-unit>
      <trans-unit id="BackendListFilterFulltextSearch" resname="BackendListFilterFulltextSearch" approved="yes">
        <source>Fulltext Search:</source>
        <target state="final">Volltextsuche:</target>
      </trans-unit>
      <trans-unit id="BackendListFilterFulltextSearchFormFilter" resname="BackendListFilterFulltextSearchFormFilter" approved="yes">
        <source>Reduce to one form:</source>
        <target state="final">Auf ein Formular begrenzen:</target>
      </trans-unit>
      <trans-unit id="BackendListFilterFulltextSearchSubmit" resname="BackendListFilterFulltextSearchSubmit" approved="yes">
        <source>Filter List</source>
        <target state="final">Filtern</target>
      </trans-unit>
      <trans-unit id="BackendListFilterExtendedSearch" resname="BackendListFilterExtendedSearch" approved="yes">
        <source>Extended Search</source>
        <target state="final">Erweiterte Suche</target>
      </trans-unit>
      <trans-unit id="BackendListFilterStart" resname="BackendListFilterStart" approved="yes">
        <source>Start</source>
        <target state="final">Von</target>
      </trans-unit>
      <trans-unit id="BackendListFilterEnd" resname="BackendListFilterEnd" approved="yes">
        <source>Stop</source>
        <target state="final">Bis</target>
      </trans-unit>
      <trans-unit id="BackendListFilterName" resname="BackendListFilterName" approved="yes">
        <source>Sender Name</source>
        <target state="final">Absender Name</target>
      </trans-unit>
      <trans-unit id="BackendListFilterMail" resname="BackendListFilterMail" approved="yes">
        <source>Sender Email</source>
        <target state="final">Absender E-Mail</target>
      </trans-unit>
      <trans-unit id="BackendListFilterSubject" resname="BackendListFilterSubject" approved="yes">
        <source>Subject</source>
        <target state="final">Betreff</target>
      </trans-unit>
      <trans-unit id="BackendListFilterHidden" resname="BackendListFilterHidden" approved="yes">
        <source>Deactivated Mails</source>
        <target state="final">Deaktivierte E-Mails</target>
      </trans-unit>
      <trans-unit id="BackendListFilterDefaultFields" resname="BackendListFilterDefaultFields" approved="yes">
        <source>Default Fields</source>
        <target state="final">Standard Felder</target>
      </trans-unit>
      <trans-unit id="BackendListFilterAddField" resname="BackendListFilterAddField" approved="yes">
        <source>Additional Fields</source>
        <target state="final">Weitere Felder</target>
      </trans-unit>
      <trans-unit id="BackendListFilterClean" resname="BackendListFilterClean" approved="yes">
        <source>Delete Filters</source>
        <target state="final">Filter löschen</target>
      </trans-unit>
      <trans-unit id="BackendListFilterCleanLink" resname="BackendListFilterCleanLink" approved="yes">
        <source>Show all Mails</source>
        <target state="final">Zeige alle Mails</target>
      </trans-unit>
      <trans-unit id="BackendListFilterShowHidden" resname="BackendListFilterShowHidden" approved="yes">
        <source>Show only deactivated Mails</source>
        <target state="final">Zeige nur deaktivierte Mails</target>
      </trans-unit>
      <trans-unit id="BackendListFilterShowNonHidden" resname="BackendListFilterShowNonHidden" approved="yes">
        <source>Show only activated Mails</source>
        <target state="final">Zeige nur aktivierte Mails</target>
      </trans-unit>
      <trans-unit id="BackendListFilterExtendedExport" resname="BackendListFilterExtendedExport" approved="yes">
        <source>Extended Export Settings</source>
        <target state="final">Erweiterte Export Einstellungen</target>
      </trans-unit>
      <trans-unit id="BackendListFilterExtendedExportLeftLabel" resname="BackendListFilterExtendedExportLeftLabel" approved="yes">
        <source>Columns in Export file</source>
        <target state="final">Spalten in Exportdatei</target>
      </trans-unit>
      <trans-unit id="BackendListFilterExtendedExportRightLabel" resname="BackendListFilterExtendedExportRightLabel" approved="yes">
        <source>Available Columns</source>
        <target state="final">Weitere verfügbare Spalten</target>
      </trans-unit>
      <trans-unit id="BackendReportingTitle" resname="BackendReportingTitle" approved="yes">
        <source>Mail Reporting</source>
        <target state="final">E-Mail Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendReportingForm" resname="BackendReportingForm" approved="yes">
        <source>Form Reporting</source>
        <target state="final">Formular Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendReportingFormTitle" resname="BackendReportingFormTitle" approved="yes">
        <source>Form Reporting</source>
        <target state="final">Formular Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendReportingFormValue" resname="BackendReportingFormValue" approved="yes">
        <source>Value</source>
        <target state="final">Wert</target>
      </trans-unit>
      <trans-unit id="BackendReportingFormSubtitle1" resname="BackendReportingFormSubtitle1" approved="yes">
        <source>Reporting with</source>
        <target state="final">Auswertung über</target>
      </trans-unit>
      <trans-unit id="BackendReportingFormAmount" resname="BackendReportingFormAmount" approved="yes">
        <source>Amount</source>
        <target state="final">Anzahl</target>
      </trans-unit>
      <trans-unit id="BackendReportingMarketing" resname="BackendReportingMarketing" approved="yes">
        <source>Marketing Reporting</source>
        <target state="final">Marketing Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendReportingMarketingTitle" resname="BackendReportingMarketingTitle" approved="yes">
        <source>Marketing Reporting</source>
        <target state="final">Marketing Auswertung</target>
      </trans-unit>
      <trans-unit id="BackendReportingNoValues" resname="BackendReportingNoValues" approved="yes">
        <source>No Values to Show</source>
        <target state="final">Keine Daten zum Anzeigen vorhanden</target>
      </trans-unit>
      <trans-unit id="BackendPluginInformationNoHeader" resname="BackendPluginInformationNoHeader" approved="yes">
        <source>[no header]</source>
        <target state="final">[keine Überschrift]</target>
      </trans-unit>
      <trans-unit id="BackendPluginInformationTitleMailForm" resname="BackendPluginInformationTitleMailForm" approved="yes">
        <source>Mailform configuration</source>
        <target state="final">Formular Konfiguration</target>
      </trans-unit>
      <trans-unit id="BackendPluginInformationTitleLastMails" resname="BackendPluginInformationTitleLastMails" approved="yes">
        <source>Last mails from this form</source>
        <target state="final">Neueste Mails dieses Formulares</target>
      </trans-unit>
      <trans-unit id="BackendPluginInformationTitlePi2" resname="BackendPluginInformationTitlePi2" approved="yes">
        <source>Show and manage mails in Frontend</source>
        <target state="final">Zeige und verwalte Mails im Frontend</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendNoMails" resname="PowermailFrontendNoMails" approved="yes">
        <source>No Mails found.</source>
        <target state="final">Keine Mails gefunden.</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendFilterAll" resname="PowermailFrontendFilterAll" approved="yes">
        <source>All</source>
        <target state="final">Alle</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendFilterSubmit" resname="PowermailFrontendFilterSubmit" approved="yes">
        <source>Filter Now</source>
        <target state="final">Jetzt Filtern</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendNoMailsDetail" resname="PowermailFrontendNoMailsDetail" approved="yes">
        <source>Please edit your filter settings.</source>
        <target state="final">Bitte passen Sie Ihre Filtereinstellungen an.</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendDetailView" resname="PowermailFrontendDetailView" approved="yes">
        <source>Details</source>
        <target state="final">Details</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditView" resname="PowermailFrontendEditView" approved="yes">
        <source>Edit</source>
        <target state="final">Bearbeiten</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendDeleteView" resname="PowermailFrontendDeleteView" approved="yes">
        <source>Delete</source>
        <target state="final">Löschen</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditSubmit" resname="PowermailFrontendEditSubmit" approved="yes">
        <source>Save Changes</source>
        <target state="final">Änderungen Speichern</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditConfirm" resname="PowermailFrontendEditConfirm" approved="yes">
        <source>Changes successfully saved</source>
        <target state="final">Änderungen erfolgreich gespeichert</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditNoMail" resname="PowermailFrontendEditNoMail" approved="yes">
        <source>Missing required Mail parameter</source>
        <target state="final">Mail Parameter fehlt</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditFailed" resname="PowermailFrontendEditFailed" approved="yes">
        <source>You are not allowed to make changes</source>
        <target state="final">Sie haben nicht die benötigte Berechtigung um Änderungen durchzuführen</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendEditSuccessful" resname="PowermailFrontendEditSuccessful" approved="yes">
        <source>Changes successfully saved</source>
        <target state="final">Änderungen erfolgreich gespeichert</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendDeleteFailed" resname="PowermailFrontendDeleteFailed" approved="yes">
        <source>Record could not be removed</source>
        <target state="final">Datensatz konnte nicht gelöscht werden</target>
      </trans-unit>
      <trans-unit id="PowermailFrontendDeleteSuccessful" resname="PowermailFrontendDeleteSuccessful" approved="yes">
        <source>Record successfully removed</source>
        <target state="final">Datensatz erfolgreich gelöscht</target>
      </trans-unit>
      <trans-unit id="ExtensionManagerConvertingScriptSuccess" resname="ExtensionManagerConvertingScriptSuccess" approved="yes">
        <source>All tables successfully converted</source>
        <target state="final">Alle Tabellen erfolgreich migriert!</target>
      </trans-unit>
      <trans-unit id="ExtensionManagerConvertingScriptStopAlreadyExist" resname="ExtensionManagerConvertingScriptStopAlreadyExist" approved="yes">
        <source>Failure! Tables could not be converted because there is already one or more new tables (tx_powermail_domain_model_mail, tx_powermail_domain_model_answer, tx_powermail_domain_model_form, tx_powermail_domain_model_page, tx_powermail_domain_model_field) and they are not empty</source>
        <target state="final">Fehler! Tabellen konnten nicht migriert werden, weil eine oder mehrere neue Tabellen bereits existieren (tx_powermail_domain_model_mail, tx_powermail_domain_model_answer, tx_powermail_domain_model_form, tx_powermail_domain_model_page, tx_powermail_domain_model_field) und nicht leer sind</target>
      </trans-unit>
      <trans-unit id="ExtensionManagerConvertingScriptStopNoTablesFound" resname="ExtensionManagerConvertingScriptStopNoTablesFound" approved="yes">
        <source>Failure! Tables could not be converted because there is one or more old tables missing (tx_powermail_domain_model_mails, tx_powermail_domain_model_answers, tx_powermail_domain_model_forms, tx_powermail_domain_model_pages, tx_powermail_domain_model_fields)</source>
        <target state="final">Fehler! Tabellen konnten nicht migriert werden, weil eine oder mehrere alte Tabellen nicht mehr existieren (tx_powermail_domain_model_mails, tx_powermail_domain_model_answers, tx_powermail_domain_model_forms, tx_powermail_domain_model_pages, tx_powermail_domain_model_fields)</target>
      </trans-unit>
      <trans-unit id="Back" resname="Back" approved="yes">
        <source>Back</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="Clock" resname="Clock" approved="yes">
        <source>o'clock</source>
        <target state="final">Uhr</target>
      </trans-unit>
      <trans-unit id="ClearFilter" resname="ClearFilter" approved="yes">
        <source>Delete Filter</source>
        <target state="final">Filter löschen</target>
      </trans-unit>
      <trans-unit id="Emails" resname="Emails" approved="yes">
        <source>Emails</source>
        <target state="final">E-Mails</target>
      </trans-unit>
      <trans-unit id="FormField" resname="FormField" approved="yes">
        <source>Form field</source>
        <target state="final">Formularfeld</target>
      </trans-unit>
      <trans-unit id="MarketingInformation" resname="MarketingInformation" approved="yes">
        <source>Marketing Information</source>
        <target state="final">Marketing Informationen</target>
      </trans-unit>
      <trans-unit id="MarketingInformationCountryDisabled" resname="MarketingInformationCountryDisabled" approved="yes">
        <source>Country From Ip is disabled.</source>
        <target state="final">Länderermittlung von IP Adresse ist deaktiviert.</target>
      </trans-unit>
      <trans-unit id="honeypodLabel" resname="honeypodLabel" approved="yes">
        <source>Don't fill this field!</source>
        <target state="final">Bitte dieses Feld NICHT ausfüllen!</target>
      </trans-unit>
      <trans-unit id="deleteAllFiles" resname="deleteAllFiles" approved="yes">
        <source>Delete All Files</source>
        <target state="final">Alle Dateien löschen</target>
      </trans-unit>
      <trans-unit id="xlsInfoModalTitle" resname="" approved="yes">
        <source>Information</source>
        <target state="final">Information</target>
      </trans-unit>
      <trans-unit id="xlsInfoModalText" resname="" approved="yes">
        <source>You can use CSV Export in Excel also, if you want to export xls files the TYPO3 Extension base_excel must be installed. Further information: https://github.com/franzholz/base_excel</source>
        <target state="final">Sie können CSV Export auch in Excel verwenden, wenn Sie xls-Dateien exportieren wollen, muss die TYPO3 Extension base_excel installiert sein. Weitere Informationen: https://github.com/franzholz/base_excel</target>
      </trans-unit>
      <trans-unit id="xlsInfoModalCancel" resname="" approved="yes">
        <source>Close</source>
        <target state="final">Schließen</target>
      </trans-unit>
    </body>
  </file>
</xliff>
