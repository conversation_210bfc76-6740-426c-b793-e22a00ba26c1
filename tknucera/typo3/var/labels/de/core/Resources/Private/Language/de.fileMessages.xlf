<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/fileMessages.xlf" date="2015-11-10T06:53:10Z" product-name="core" target-language="de">
    <header/>
    <body>
      <trans-unit id="FileUtility.CannotCopyFolderIntoTargetFolderBecauseTheTargetFolderIsAlreadyWithinTheFolderToBeCopied" resname="FileUtility.CannotCopyFolderIntoTargetFolderBecauseTheTargetFolderIsAlreadyWithinTheFolderToBeCopied" approved="yes">
        <source>Cannot copy folder "%s" into target folder "%s", because there is already a folder or file with that name in the target folder!</source>
        <target state="final">Ordner "%s" kann nicht in den Zielordner "%s" kopiert werden, da bereits ein Ordner oder eine Datei mit diesem Namen im Zielordner vorhanden ist!</target>
      </trans-unit>
      <trans-unit id="FileUtility.CannotMoveFolderIntoTargetFolderBecauseTheTargetFolderIsAlreadyWithinTheFolderToBeMoved" resname="FileUtility.CannotMoveFolderIntoTargetFolderBecauseTheTargetFolderIsAlreadyWithinTheFolderToBeMoved" approved="yes">
        <source>Cannot move folder "%s" into target folder "%s", because the target folder is already within the folder to be moved!</source>
        <target state="final">Ordner "%s" kann nicht in Zielordner "%s" verschoben werden, weil der Zielordner sich bereits in dem zu verschiebenden Ordner befindet!</target>
      </trans-unit>
      <trans-unit id="FileUtility.CouldNotAccessAllNecessaryResources" resname="FileUtility.CouldNotAccessAllNecessaryResources" approved="yes">
        <source>Could not access all necessary resources. Source file or destination maybe was not within your mountpoints? T="%s", D="%s"</source>
        <target state="final">Kein Zugriff auf alle notwendigen Ressourcen. Liegen Quell- und Zieldatei innerhalb Ihrer Freigabepunkte? Z="%s", Q="%s"</target>
      </trans-unit>
      <trans-unit id="FileUtility.CouldNotDeleteDirectory" resname="FileUtility.CouldNotDeleteDirectory" approved="yes">
        <source>Could not delete directory! Is directory "%s" empty? (You are not allowed to delete directories recursively).</source>
        <target state="final">Verzeichnis konnte nicht gelöscht werden! Ist das Verzeichnis "%s" leer? (Sie haben keine Berechtigung, Verzeichnisse rekursiv zu löschen.)</target>
      </trans-unit>
      <trans-unit id="FileUtility.CouldNotDeleteDirectory1" resname="FileUtility.CouldNotDeleteDirectory1" approved="yes">
        <source>Could not delete directory "%s"! There might be a problem with write permissions.</source>
        <target state="final">Verzeichnis "%s" konnte nicht gelöscht werden! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.CouldNotDeleteFile" resname="FileUtility.CouldNotDeleteFile" approved="yes">
        <source>Could not delete file "%s". There might be a problem with write permissions.</source>
        <target state="final">Datei "%s" konnte nicht gelöscht werden. Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DestinationExistedAlready" resname="FileUtility.DestinationExistedAlready" approved="yes">
        <source>Destination "%s" already exists!</source>
        <target state="final">Ziel "%s" existiert bereits!</target>
      </trans-unit>
      <trans-unit id="FileUtility.DestinationPathWasNotWithinYourMountpoints" resname="FileUtility.DestinationPathWasNotWithinYourMountpoints" approved="yes">
        <source>Destination path "%s" was not within your mountpoints!</source>
        <target state="final">Zielpfad "%s" liegt nicht innerhalb Ihrer Freigabepunkte!</target>
      </trans-unit>
      <trans-unit id="FileUtility.DestinationWasNotADirectory" resname="FileUtility.DestinationWasNotADirectory" approved="yes">
        <source>Destination "%s" was not a directory.</source>
        <target state="final">Ziel "%s" war kein Verzeichnis.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryCopiedTo" resname="FileUtility.DirectoryCopiedTo" approved="yes">
        <source>Directory "%s" copied to "%s".</source>
        <target state="final">Verzeichnis "%s" wurde nach "%s" kopiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryCreatedIn" resname="FileUtility.DirectoryCreatedIn" approved="yes">
        <source>Directory "%s" created in "%s".</source>
        <target state="final">Verzeichnis "%s" wurde in "%s" erstellt.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryMovedTo" resname="FileUtility.DirectoryMovedTo" approved="yes">
        <source>Directory "%s" moved to "%s".</source>
        <target state="final">Verzeichnis "%s" wurde nach "%s" verschoben.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryNotCreated" resname="FileUtility.DirectoryNotCreated" approved="yes">
        <source>Directory "%s" not created. There might be a problem with write permissions in "%s"?</source>
        <target state="final">Verzeichnis "%s" wurde nicht erstellt. Möglicherweise gibt es ein Problem mit den Schreibberechtigungen in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryRenamedFromTo" resname="FileUtility.DirectoryRenamedFromTo" approved="yes">
        <source>Directory renamed from "%s" to "%s".</source>
        <target state="final">Verzeichnis wurde von "%s" nach "%s" umbenannt.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryRenamedFromToCharReplaced" resname="FileUtility.DirectoryRenamedFromToCharReplaced" approved="yes">
        <source>Directory renamed from "%s" to "%s". Characters in the new directory name have been replaced.</source>
        <target state="final">Verzeichnis wurde umbenannt von "%s" nach "%s". Einige Zeichen im neuen Verzeichnisnamen wurden ersetzt.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryRenamedSameName" resname="FileUtility.DirectoryRenamedSameName" approved="yes">
        <source>Directory "%s" was not renamed. (Same name as before)</source>
        <target state="final">Verzeichnis "%s" wurde nicht umbenannt. (Selber Name wie zuvor.)</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryWasNotCopiedTo" resname="FileUtility.DirectoryWasNotCopiedTo" approved="yes">
        <source>Directory "%s" WAS NOT copied to "%s"! There might be a problem with write permissions</source>
        <target state="final">Verzeichnis "%s" wurde NICHT nach "%s" kopiert! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryWasNotMovedTo" resname="FileUtility.DirectoryWasNotMovedTo" approved="yes">
        <source>Directory "%s" WAS NOT moved to "%s"! There might be a problem with write permissions</source>
        <target state="final">Verzeichnis "%s" wurde NICHT nach "%s" verschoben! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.DirectoryWasNotRenamed" resname="FileUtility.DirectoryWasNotRenamed" approved="yes">
        <source>Directory "%s" was not renamed! There might be a problem with write permissions in "%s"?</source>
        <target state="final">Verzeichnis "%s" wurde nicht umbenannt! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.ExtensionOfFileNameIsNotAllowedIn" resname="FileUtility.ExtensionOfFileNameIsNotAllowedIn" approved="yes">
        <source>Extension of file name "%s" is not allowed in "%s"!</source>
        <target state="final">Erweiterung von Datei "%s" in "%s" ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="FileUtility.ExtensionOfFileNameOrWasNotAllowed" resname="FileUtility.ExtensionOfFileNameOrWasNotAllowed" approved="yes">
        <source>Extension of file name "%s" or "%s" was not allowed!</source>
        <target state="final">Erweiterung von Datei "%s" oder "%s" ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="FileUtility.ExtensionOfFileWasNotAllowed" resname="FileUtility.ExtensionOfFileWasNotAllowed" approved="yes">
        <source>Extension of file "%s" was not allowed!</source>
        <target state="final">Erweiterung von Datei "%s" ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileAlreadyExistsInFolder" resname="FileUtility.FileAlreadyExistsInFolder" approved="yes">
        <source>File "%s" already exists in folder "%s"!</source>
        <target state="final">Datei "%s" in Ordner "%s" existiert bereits!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileCopiedTo" resname="FileUtility.FileCopiedTo" approved="yes">
        <source>File "%s" copied to "%s".</source>
        <target state="final">Datei "%s" wurde nach "%s" kopiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileCreated" resname="FileUtility.FileCreated" approved="yes">
        <source>File created: "%s".</source>
        <target state="final">Datei erstellt: "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileNameSanitized" resname="FileUtility.FileNameSanitized" approved="yes">
        <source>The file name "%s" is invalid, the file was automatically renamed to "%s".</source>
        <target state="final">Der Dateiname "%s" ist ungültig; die Datei wurde automatisch umbenannt zu "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileExistedAlreadyIn" resname="FileUtility.FileExistedAlreadyIn" approved="yes">
        <source>File existed already in "%s"!</source>
        <target state="final">Datei existiert bereits in "%s"!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileExtensionIsNotATextfileFormat" resname="FileUtility.FileExtensionIsNotATextfileFormat" approved="yes">
        <source>File extension "%s" is not a textfile format! (%s).</source>
        <target state="final">Dateierweiterung "%s" gehört nicht zu einem Textdateiformat! (%s)</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileExtensionIsNotzip" resname="FileUtility.FileExtensionIsNotzip" approved="yes">
        <source>File extension is not "zip".</source>
        <target state="final">Dateierweiterung ist nicht "zip".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileMovedTo" resname="FileUtility.FileMovedTo" approved="yes">
        <source>File "%s" moved to "%s".</source>
        <target state="final">Datei "%s" wurde nach "%s" verschoben.</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileNameWasNotAllowed" resname="FileUtility.FileNameWasNotAllowed" approved="yes">
        <source>File name "%s" was not allowed!</source>
        <target state="final">Dateiname "%s" ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileOrDestinationWasNotWithinYourMountpoints" resname="FileUtility.FileOrDestinationWasNotWithinYourMountpoints" approved="yes">
        <source>File "%s" or destination "%s" was not within your mountpoints!</source>
        <target state="final">Datei "%s" oder Ziel "%s" liegt nicht innerhalb Ihrer Freigabepunkte!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileOrDirectoryExistedAlready" resname="FileUtility.FileOrDirectoryExistedAlready" approved="yes">
        <source>File or directory "%s" already exists!</source>
        <target state="final">Datei oder Verzeichnis "%s" existiert bereits!</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileRenamedFromTo" resname="FileUtility.FileRenamedFromTo" approved="yes">
        <source>File renamed from "%s" to "%s".</source>
        <target state="final">Datei wurde von "%s" nach "%s" umbenannt.</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileRenamedSameName" resname="FileUtility.FileRenamedSameName" approved="yes">
        <source>File "%s" was not renamed. (Same name as before)</source>
        <target state="final">Datei "%s" wurde nicht umbenannt. (Selber Name wie zuvor.)</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileSavedTo" resname="FileUtility.FileSavedTo" approved="yes">
        <source>File saved to "%s".</source>
        <target state="final">Datei gespeichert unter "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileWasNotCopiedTo" resname="FileUtility.FileWasNotCopiedTo" approved="yes">
        <source>File "%s" WAS NOT copied to "%s"! There might be a problem with write permissions.</source>
        <target state="final">Datei "%s" wurde NICHT nach "%s" kopiert! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileWasNotCreated" resname="FileUtility.FileWasNotCreated" approved="yes">
        <source>File "%s" was not created! There might be a problem with write permissions in "%s"?</source>
        <target state="final">Datei "%s" wurde nicht erstellt! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileWasNotRenamed" resname="FileUtility.FileWasNotRenamed" approved="yes">
        <source>File "%s" was not renamed! There might be a problem with write permissions in "%s"?</source>
        <target state="final">Datei "%s" wurde nicht umbenannt! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileWasNotSaved" resname="FileUtility.FileWasNotSaved" approved="yes">
        <source>File "%s" was not saved! There might be a problem with write permissions.</source>
        <target state="final">Datei "%s" wurde nicht gespeichert! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.FileWasNotSaved2" resname="FileUtility.FileWasNotSaved2" approved="yes">
        <source>File "%s" was not saved! File extension rejected!</source>
        <target state="final">Datei "%s" wurde nicht gespeichert! Dateierweiterung zurückgewiesen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.NoFileWasUploaded" resname="FileUtility.NoFileWasUploaded" approved="yes">
        <source>No file was uploaded!</source>
        <target state="final">Es wurde keine Datei hochgeladen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.NoFileWasUploadedForReplacing" resname="FileUtility.NoFileWasUploadedForReplacing" approved="yes">
        <source>No file was uploaded for replacing!</source>
        <target state="final">Es wurde keine Datei zum Ersetzen hochgeladen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.NoNameForNewFolderGiven" resname="FileUtility.NoNameForNewFolderGiven" approved="yes">
        <source>No name for new folder given!</source>
        <target state="final">Es wurde kein Name für den neuen Ordner angegeben!</target>
      </trans-unit>
      <trans-unit id="FileUtility.NoUniqueFilenameAvailableIn" resname="FileUtility.NoUniqueFilenameAvailableIn" approved="yes">
        <source>No unique filename available in "%s"!</source>
        <target state="final">In "%s" ist kein eindeutiger Dateiname verfügbar!</target>
      </trans-unit>
      <trans-unit id="FileUtility.ReplacingFileTo" resname="FileUtility.ReplacingFileTo" approved="yes">
        <source>Replacing file "%s" to "%s".</source>
        <target state="final">Datei "%s" wird durch "%s" ersetzt.</target>
      </trans-unit>
      <trans-unit id="FileUtility.TargetAlreadyExists" resname="FileUtility.TargetAlreadyExists" approved="yes">
        <source>Target "%s" already exists!</source>
        <target state="final">Ziel "%s" existiert bereits!</target>
      </trans-unit>
      <trans-unit id="FileUtility.TargetWasNotAFile" resname="FileUtility.TargetWasNotAFile" approved="yes">
        <source>Target "%s" was not a file!</source>
        <target state="final">Ziel "%s" ist keine Datei!</target>
      </trans-unit>
      <trans-unit id="FileUtility.TargetWasNotWithinYourMountpoints" resname="FileUtility.TargetWasNotWithinYourMountpoints" approved="yes">
        <source>Target was not within your mountpoints! T="%s".</source>
        <target state="final">Ziel liegt nicht innerhalb Ihrer Freigabepunkte! Z="%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheFileDidNotExist" resname="FileUtility.TheFileDidNotExist" approved="yes">
        <source>The file "%s" did not exist!</source>
        <target state="final">Datei "%s" war nicht vorhanden!</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheFunctionToCopyAFileBetweenStoragesIsNotYetImplemented" resname="FileUtility.TheFunctionToCopyAFileBetweenStoragesIsNotYetImplemented" approved="yes">
        <source>The function to copy a file between storages is not yet implemented.</source>
        <target state="final">Die Funktion, eine Datei zwischen Speichern zu kopieren, ist noch nicht implementiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheFunctionToCopyAFolderBetweenStoragesIsNotYetImplemented" resname="FileUtility.TheFunctionToCopyAFolderBetweenStoragesIsNotYetImplemented" approved="yes">
        <source>The function to copy a folder between storages is not yet implemented.</source>
        <target state="final">Die Funktion, einen Ordner zwischen Speichern zu kopieren, ist noch nicht implementiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheFunctionToMoveAFileBetweenStoragesIsNotYetImplemented" resname="FileUtility.TheFunctionToMoveAFileBetweenStoragesIsNotYetImplemented" approved="yes">
        <source>The function to move a file between storages is not yet implemented.</source>
        <target state="final">Die Funktion, eine Datei zwischen Speichern zu verschieben, ist noch nicht implementiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheFunctionToMoveAFolderBetweenStoragesIsNotYetImplemented" resname="FileUtility.TheFunctionToMoveAFolderBetweenStoragesIsNotYetImplemented" approved="yes">
        <source>The function to move a folder between storages is not yet implemented.</source>
        <target state="final">Die Funktion, einen Ordner zwischen Speichern zu verschieben, ist noch nicht implementiert.</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheUploadHasFailedNoUploadedFileFound" resname="FileUtility.TheUploadHasFailedNoUploadedFileFound" approved="yes">
        <source>The upload has failed, no uploaded file found!</source>
        <target state="final">Das Hochladen ist fehlgeschlagen; es wurde keine hochgeladene Datei gefunden!</target>
      </trans-unit>
      <trans-unit id="FileUtility.TheUploadedFileExceedsTheSize-limit" resname="FileUtility.TheUploadedFileExceedsTheSize-limit" approved="yes">
        <source>The uploaded file "%s" exceeds the size-limit</source>
        <target state="final">Die hochgeladene Datei "%s" überschreitet de Größenbeschränkung.</target>
      </trans-unit>
      <trans-unit id="FileUtility.UnzippingFileIn" resname="FileUtility.UnzippingFileIn" approved="yes">
        <source>Unzipping file "%s" in "%s".</source>
        <target state="final">Entpacken der Datei "%s" in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.UploadedFileCouldNotBeMoved" resname="FileUtility.UploadedFileCouldNotBeMoved" approved="yes">
        <source>Uploaded file could not be moved! There might be a problem with write permissions in "%s"?</source>
        <target state="final">Die hochgeladene Datei konnte nicht verschoben werden! Möglicherweise gibt es ein Problem mit den Schreibberechtigungen in "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.UploadingFileTo" resname="FileUtility.UploadingFileTo" approved="yes">
        <source>Uploading file "%s" to "%s".</source>
        <target state="final">Hochladen der Datei "%s" nach "%s".</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToAccessTheDirectory" resname="FileUtility.YouAreNotAllowedToAccessTheDirectory" approved="yes">
        <source>You are not allowed to access the directory.</source>
        <target state="final">Sie haben keine Berechtigung, auf dieses Verzeichnis zuzugreifen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToAccessTheFile" resname="FileUtility.YouAreNotAllowedToAccessTheFile" approved="yes">
        <source>You are not allowed to access the file.</source>
        <target state="final">Sie haben keine Berechtigung, auf diese Datei zuzugreifen.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToCopyDirectories" resname="FileUtility.YouAreNotAllowedToCopyDirectories" approved="yes">
        <source>You are not allowed to copy directories.</source>
        <target state="final">Sie haben keine Berechtigung, Verzeichnisse zu kopieren.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToCopyFiles" resname="FileUtility.YouAreNotAllowedToCopyFiles" approved="yes">
        <source>You are not allowed to copy files.</source>
        <target state="final">Sie haben keine Berechtigung, Dateien zu kopieren.</target>
      </trans-unit>
      <trans-unit id="FileUtility.InvalidFolderName" resname="FileUtility.InvalidFolderName" approved="yes">
        <source>Invalid folder name "%s"!</source>
        <target state="final">Der Ordnername "%s" ist ungültig!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToCreateDirectories" resname="FileUtility.YouAreNotAllowedToCreateDirectories" approved="yes">
        <source>You are not allowed to create directories!</source>
        <target state="final">Sie haben keine Berechtigung, Verzeichnisse zu erstellen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToCreateFiles" resname="FileUtility.YouAreNotAllowedToCreateFiles" approved="yes">
        <source>You are not allowed to create files!</source>
        <target state="final">Sie haben keine Berechtigung, Dateien zu erstellen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToEditFiles" resname="FileUtility.YouAreNotAllowedToEditFiles" approved="yes">
        <source>You are not allowed to edit files!</source>
        <target state="final">Sie haben keine Berechtigung, Dateien zu bearbeiten!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToMoveDirectories" resname="FileUtility.YouAreNotAllowedToMoveDirectories" approved="yes">
        <source>You are not allowed to move directories.</source>
        <target state="final">Sie haben keine Berechtigung, Verzeichnisse zu verschieben.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToMoveFiles" resname="FileUtility.YouAreNotAllowedToMoveFiles" approved="yes">
        <source>You are not allowed to move files.</source>
        <target state="final">Sie haben keine Berechtigung, Dateien zu verschieben.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToOverride" resname="FileUtility.YouAreNotAllowedToOverride" approved="yes">
        <source>You are not allowed to override "%s"!</source>
        <target state="final">Sie haben keine Berechtigung, "%s" zu überschreiben!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToRenameDirectories" resname="FileUtility.YouAreNotAllowedToRenameDirectories" approved="yes">
        <source>You are not allowed to rename directories!</source>
        <target state="final">Sie haben keine Berechtigung, Verzeichnisse umzubenennen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToRenameFiles" resname="FileUtility.YouAreNotAllowedToRenameFiles" approved="yes">
        <source>You are not allowed to rename files!</source>
        <target state="final">Sie haben keine Berechtigung, Dateien umzubenennen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToUnzipFiles" resname="FileUtility.YouAreNotAllowedToUnzipFiles" approved="yes">
        <source>You are not allowed to unzip files.</source>
        <target state="final">Sie haben keine Berechtigung, Dateien zu entpacken.</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouAreNotAllowedToUploadFiles" resname="FileUtility.YouAreNotAllowedToUploadFiles" approved="yes">
        <source>You are not allowed to upload files!</source>
        <target state="final">Sie haben keine Berechtigung, Dateien hochzuladen!</target>
      </trans-unit>
      <trans-unit id="FileUtility.YouDontHaveFullAccessToTheDestinationDirectory" resname="FileUtility.YouDontHaveFullAccessToTheDestinationDirectory" approved="yes">
        <source>You don't have full access to the destination directory "%s"!</source>
        <target state="final">Sie haben keinen vollen Zugriff auf das Zielverzeichnis "%s"!</target>
      </trans-unit>
    </body>
  </file>
</xliff>
