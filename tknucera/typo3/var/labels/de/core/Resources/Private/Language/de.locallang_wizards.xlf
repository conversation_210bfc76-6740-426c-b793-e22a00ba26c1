<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/locallang_wizards.xlf" date="2011-10-17T20:22:34Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="table_smallFields" resname="table_smallFields" approved="yes">
        <source>Small fields</source>
        <target state="final">Klein<PERSON> Felder</target>
      </trans-unit>
      <trans-unit id="table_addColumn" resname="table_addColumn" approved="yes">
        <source>Add column to the right</source>
        <target state="final">Spalte rechts hinzufügen</target>
      </trans-unit>
      <trans-unit id="table_removeColumn" resname="table_removeColumn" approved="yes">
        <source>Remove column</source>
        <target state="final">Spalte entfernen</target>
      </trans-unit>
      <trans-unit id="table_addRow" resname="table_addRow" approved="yes">
        <source>Add row below</source>
        <target state="final">Zeile unten hinzufügen</target>
      </trans-unit>
      <trans-unit id="table_removeRow" resname="table_removeRow" approved="yes">
        <source>Remove row</source>
        <target state="final">Zeile entfernen</target>
      </trans-unit>
      <trans-unit id="table_left" resname="table_left" approved="yes">
        <source>Move left</source>
        <target state="final">Nach links verschieben</target>
      </trans-unit>
      <trans-unit id="table_right" resname="table_right" approved="yes">
        <source>Move right</source>
        <target state="final">Nach rechts verschieben</target>
      </trans-unit>
      <trans-unit id="table_up" resname="table_up" approved="yes">
        <source>Move up</source>
        <target state="final">Nach oben verschieben</target>
      </trans-unit>
      <trans-unit id="table_down" resname="table_down" approved="yes">
        <source>Move down</source>
        <target state="final">Nach unten verschieben</target>
      </trans-unit>
      <trans-unit id="table_top" resname="table_top" approved="yes">
        <source>Move to top</source>
        <target state="final">An Anfang verschieben</target>
      </trans-unit>
      <trans-unit id="table_bottom" resname="table_bottom" approved="yes">
        <source>Move to bottom</source>
        <target state="final">An Ende verschieben</target>
      </trans-unit>
      <trans-unit id="table_start" resname="table_start" approved="yes">
        <source>Move to first column</source>
        <target state="final">In erste Spalte verschieben</target>
      </trans-unit>
      <trans-unit id="table_end" resname="table_end" approved="yes">
        <source>Move to last column</source>
        <target state="final">In letzten Spalte verschieben</target>
      </trans-unit>
      <trans-unit id="table_setCount" resname="table_setCount" approved="yes">
        <source>Set row and column count</source>
        <target state="final">Anzahl der Zeilen und Spalten festlegen</target>
      </trans-unit>
      <trans-unit id="table_colCount" resname="table_colCount" approved="yes">
        <source>Column count</source>
        <target state="final">Anzahl der Spalten</target>
      </trans-unit>
      <trans-unit id="table_rowCount" resname="table_rowCount" approved="yes">
        <source>Row count</source>
        <target state="final">Anzahl der Zeilen</target>
      </trans-unit>
      <trans-unit id="table_setCountHeadline" resname="table_setCountHeadline" approved="yes">
        <source>Change table size</source>
        <target state="final">Größe der Tabelle ändern</target>
      </trans-unit>
      <trans-unit id="table_buttonApply" resname="table_buttonApply" approved="yes">
        <source>Apply</source>
        <target state="final">Anwenden</target>
      </trans-unit>
      <trans-unit id="table_showCode" resname="table_buttonApply" approved="yes">
        <source>Show table code</source>
        <target state="final">Zeige Quelltext der Tabelle</target>
      </trans-unit>
      <trans-unit id="grid_windowTitle" resname="grid_windowTitle" approved="yes">
        <source>Set cell name and column number</source>
        <target state="final">Zellenname und Spaltennummer festlegen</target>
      </trans-unit>
      <trans-unit id="grid_addColumn" resname="grid_addColumn" approved="yes">
        <source>Add column</source>
        <target state="final">Spalte hinzufügen</target>
      </trans-unit>
      <trans-unit id="grid_removeColumn" resname="grid_removeColumn" approved="yes">
        <source>Remove column</source>
        <target state="final">Spalte entfernen</target>
      </trans-unit>
      <trans-unit id="grid_addRow" resname="grid_addRow" approved="yes">
        <source>Add row</source>
        <target state="final">Zeile hinzufügen</target>
      </trans-unit>
      <trans-unit id="grid_removeRow" resname="grid_removeRow" approved="yes">
        <source>Remove row</source>
        <target state="final">Zeile entfernen</target>
      </trans-unit>
      <trans-unit id="grid_editCell" resname="grid_editCell" approved="yes">
        <source>Edit cell</source>
        <target state="final">Zelle bearbeiten</target>
      </trans-unit>
      <trans-unit id="grid_name" resname="grid_name" approved="yes">
        <source>Name</source>
        <target state="final">Name</target>
      </trans-unit>
      <trans-unit id="grid_column" resname="grid_column" approved="yes">
        <source>Column number</source>
        <target state="final">Spaltennummer</target>
      </trans-unit>
      <trans-unit id="grid_notSet" resname="grid_notSet" approved="yes">
        <source>not set</source>
        <target state="final">nicht gesetzt</target>
      </trans-unit>
      <trans-unit id="grid_nameHelp" resname="grid_nameHelp" approved="yes">
        <source>Enter a name for the cell.</source>
        <target state="final">Geben Sie einen Namen für die Zelle ein.</target>
      </trans-unit>
      <trans-unit id="grid_columnHelp" resname="grid_columnHelp" approved="yes">
        <source>The column position defines in which area the content is rendered in the frontend.</source>
        <target state="final">Die Spaltenposition setzt fest, in welchem Bereich der Inhalt im Frontend dargestellt wird.</target>
      </trans-unit>
      <trans-unit id="imwizard.open-editor" resname="imwizard.open-editor" approved="yes">
        <source>Open Editor</source>
        <target state="final">Editor öffnen</target>
      </trans-unit>
      <trans-unit id="imwizard.supported-types-message" resname="imwizard.supported-types-message" approved="yes">
        <source>Image manipulation is only available for supported types</source>
        <target state="final">Bildbearbeitung ist nur verfügbar für unterstützte Typen</target>
      </trans-unit>
      <trans-unit id="imwizard.no-image-dimensions" resname="imwizard.no-image-dimensions" approved="yes">
        <source>No image dimensions could be determined.</source>
        <target state="final">Bilddimensionen konnten nicht bestimmt werden.</target>
      </trans-unit>
      <trans-unit id="imwizard.no-image-dimensions-message" resname="imwizard.no-image-dimensions-message" approved="yes">
        <source>Unable to provide image manipulation, because original dimensions of the image are unknown.</source>
        <target state="final">Bildmanipulation ist nicht verfügbar, weil die Dimensionen des Originalbildes unbekannt sind.</target>
      </trans-unit>
      <trans-unit id="imwizard.image-manipulation" resname="imwizard.image-manipulation" approved="yes">
        <source>Image manipulation</source>
        <target state="final">Bildbearbeitung</target>
      </trans-unit>
      <trans-unit id="imwizard.reset" resname="imwizard.reset" approved="yes">
        <source>Reset</source>
        <target state="final">Zurücksetzen</target>
      </trans-unit>
      <trans-unit id="imwizard.accept" resname="imwizard.accept" approved="yes">
        <source>Accept</source>
        <target state="final">Akzeptieren</target>
      </trans-unit>
      <trans-unit id="imwizard.cancel" resname="imwizard.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbrechen</target>
      </trans-unit>
      <trans-unit id="imwizard.preview" resname="imwizard.preview" approved="yes">
        <source>Preview</source>
        <target state="final">Vorschau</target>
      </trans-unit>
      <trans-unit id="imwizard.aspect-ratio" resname="imwizard.aspect-ratio" approved="yes">
        <source>Aspect ratio</source>
        <target state="final">Seitenverhältnis</target>
      </trans-unit>
      <trans-unit id="imwizard.set-aspect-ratio" resname="imwizard.set-aspect-ratio" approved="yes">
        <source>Set Aspect Ratio</source>
        <target state="final">Seitenverhältnis festlegen</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.16_9" resname="imwizard.ratio.16_9" translate="no" approved="yes">
        <source>16:9</source>
        <target state="final">16:9</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.3_2" resname="imwizard.ratio.3_2" translate="no" approved="yes">
        <source>3:2</source>
        <target state="final">3:2</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.4_3" resname="imwizard.ratio.4_3" translate="no" approved="yes">
        <source>4:3</source>
        <target state="final">4:3</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.1_1" resname="imwizard.ratio.1_1" translate="no" approved="yes">
        <source>1:1</source>
        <target state="final">1:1</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.191_1" resname="imwizard.ratio.191_1" translate="no" approved="yes">
        <source>1.91:1</source>
        <target state="final">1.91:1</target>
      </trans-unit>
      <trans-unit id="imwizard.ratio.free" resname="imwizard.ratio.free" approved="yes">
        <source>Free</source>
        <target state="final">Frei</target>
      </trans-unit>
      <trans-unit id="imwizard.crop_variant.default" resname="imwizard.crop_variant.default" approved="yes">
        <source>Default</source>
        <target state="final">Standard</target>
      </trans-unit>
      <trans-unit id="imwizard.crop_variant.social" resname="imwizard.crop_variant.social" approved="yes">
        <source>Social media</source>
        <target state="final">Soziale Medien</target>
      </trans-unit>
      <trans-unit id="imwizard.selection" resname="imwizard.selection" approved="yes">
        <source>Selected Size</source>
        <target state="final">Ausgewählte Größe</target>
      </trans-unit>
      <trans-unit id="localizationStateSelector.header" resname="localizationStateSelector.header" approved="yes">
        <source>Translation behavior</source>
        <target state="final">Übersetzungsverhalten</target>
      </trans-unit>
      <trans-unit id="localizationStateSelector.customValue" resname="localizationStateSelector.customValue" approved="yes">
        <source>Custom value</source>
        <target state="final">Benutzerdefinierter Wert</target>
      </trans-unit>
      <trans-unit id="localizationStateSelector.defaultLanguageValue" resname="localizationStateSelector.defaultLanguageValue" approved="yes">
        <source>Value of default language</source>
        <target state="final">Wert der Standardsprache</target>
      </trans-unit>
      <trans-unit id="localizationStateSelector.sourceLanguageValue" resname="localizationStateSelector.sourceLanguageValue" approved="yes">
        <source>Value of %1s language</source>
        <target state="final">Wert von Sprache %1s</target>
      </trans-unit>
    </body>
  </file>
</xliff>
