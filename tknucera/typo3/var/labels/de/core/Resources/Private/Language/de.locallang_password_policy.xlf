<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/locallang_password_policy.xlf" date="2022-04-13T14:28:00Z" product-name="cms" target-language="de">
    <header/>
    <body>
      <trans-unit id="passwordRequirements.description" resname="passwordRequirements.description" approved="yes">
        <source>The password must match the following requirements:</source>
        <target state="final">Das Passwort muss den folgenden Anforderungen entsprechen:</target>
      </trans-unit>
      <trans-unit id="requirement.minimumLength" resname="requirement.minimumLength" approved="yes">
        <source>Minimum length: %d chars</source>
        <target state="final">Minimale Länge: %d Zeichen</target>
      </trans-unit>
      <trans-unit id="requirement.upperCaseCharacterRequired" resname="requirement.upperCaseCharacterRequired" approved="yes">
        <source>At least one upper case char</source>
        <target state="final">Mindestens ein Großbuchstabe</target>
      </trans-unit>
      <trans-unit id="requirement.lowerCaseCharacterRequired" resname="requirement.lowerCaseCharacterRequired" approved="yes">
        <source>At least one lower case char</source>
        <target state="final">Mindestens ein Kleinbuchstabe</target>
      </trans-unit>
      <trans-unit id="requirement.digitCharacterRequired" resname="requirement.digitCharacterRequired" approved="yes">
        <source>At least one digit</source>
        <target state="final">Mindestens eine Ziffer</target>
      </trans-unit>
      <trans-unit id="requirement.specialCharacterRequired" resname="requirement.specialCharacterRequired" approved="yes">
        <source>At least one special char</source>
        <target state="final">Mindestens ein Sonderzeichen</target>
      </trans-unit>
      <trans-unit id="requirement.notCurrentPassword" resname="requirement.notCurrentPassword" approved="yes">
        <source>Must be different from current password</source>
        <target state="final">Muss sich vom aktuellen Passwort unterscheiden</target>
      </trans-unit>
      <trans-unit id="error.minimumLength" resname="error.minimumLength" approved="yes">
        <source>The password is too short. Minimum length is %d chars</source>
        <target state="final">Das Passwort ist zu kurz. Die Mindestlänge beträgt %d Zeichen</target>
      </trans-unit>
      <trans-unit id="error.upperCaseCharacterRequired" resname="error.upperCaseCharacterRequired" approved="yes">
        <source>The password must at least contain one upper case char</source>
        <target state="final">Das Passwort muss mindestens einen Großbuchstaben enthalten</target>
      </trans-unit>
      <trans-unit id="error.lowerCaseCharacterRequired" resname="error.lowerCaseCharacterRequired" approved="yes">
        <source>The password must at least contain one lower case char</source>
        <target state="final">Das Passwort muss mindestens einen Kleinbuchstaben enthalten</target>
      </trans-unit>
      <trans-unit id="error.digitCharacterRequired" resname="error.digitCharacterRequired" approved="yes">
        <source>The password must at least contain one digit</source>
        <target state="final">Das Passwort muss mindestens eine Ziffer enthalten</target>
      </trans-unit>
      <trans-unit id="error.specialCharacterRequired" resname="error.specialCharacterRequired" approved="yes">
        <source>The password must at least contain one special char</source>
        <target state="final">Das Passwort muss mindestens ein Sonderzeichen enthalten</target>
      </trans-unit>
      <trans-unit id="error.notCurrentPassword" resname="error.notCurrentPassword" approved="yes">
        <source>The password must be different from the old password</source>
        <target state="final">Das Passwort muss sich vom alten Passwort unterscheiden</target>
      </trans-unit>
      <trans-unit id="dataHandler.passwordNotSaved" resname="dataHandler.passwordNotSaved" approved="yes">
        <source>Password not saved ({table}:{uid}): </source>
        <target state="final">Passwort wurde nicht gespeichert ({table}:{uid}): </target>
      </trans-unit>
    </body>
  </file>
</xliff>
