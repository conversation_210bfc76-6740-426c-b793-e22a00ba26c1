<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/locallang_mod_web_list.xlf" date="2011-10-17T20:22:34Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="mlang_labels_tablabel" resname="mlang_labels_tablabel" approved="yes">
        <source>List of database records</source>
        <target state="final">Liste der Datensätze</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tabdescr" resname="mlang_labels_tabdescr" approved="yes">
        <source>The Web&gt;List module provides low level access to records appearing on a page.</source>
        <target state="final">Das Modul Web &gt; Liste bietet den grundlegendsten Zugriff auf die Datensätze einer Seite.</target>
      </trans-unit>
      <trans-unit id="mlang_tabs_tab" resname="mlang_tabs_tab" approved="yes">
        <source>List</source>
        <target state="final">Liste</target>
      </trans-unit>
      <trans-unit id="clip_pasteAfter" resname="clip_pasteAfter" approved="yes">
        <source>Paste after: Clipboard content is inserted after this record</source>
        <target state="final">Einfügen nach: Inhalt der Zwischenablage wird nach diesem Datensatz eingefügt</target>
      </trans-unit>
      <trans-unit id="clip_pasteInto" resname="clip_pasteInto" approved="yes">
        <source>Paste into: Clipboard content is inserted on this page</source>
        <target state="final">Einfügen in: Inhalt der Zwischenablage wird in diese Seite eingefügt</target>
      </trans-unit>
      <trans-unit id="clip_paste" resname="clip_paste" approved="yes">
        <source>Paste in clipboard content</source>
        <target state="final">Zwischenablageninhalt einfügen</target>
      </trans-unit>
      <trans-unit id="clip_markRecords" resname="clip_markRecords" approved="yes">
        <source>Mark All/Mark none</source>
        <target state="final">Alles/nichts markieren</target>
      </trans-unit>
      <trans-unit id="clip_selectMarked" resname="clip_selectMarked" approved="yes">
        <source>Transfer the selection of records to clipboard</source>
        <target state="final">Ausgewählte Datensätze in die Zwischenablage übertragen</target>
      </trans-unit>
      <trans-unit id="clip_duplicates" resname="clip_duplicates" approved="yes">
        <source>Mark duplicates</source>
        <target state="final">Duplikate markieren</target>
      </trans-unit>
      <trans-unit id="clip_editMarked" resname="clip_editMarked" approved="yes">
        <source>Edit marked</source>
        <target state="final">Markierte bearbeiten</target>
      </trans-unit>
      <trans-unit id="clip_deleteMarked" resname="clip_deleteMarked" approved="yes">
        <source>Delete marked</source>
        <target state="final">Markierte löschen</target>
      </trans-unit>
      <trans-unit id="clip_deleteMarkedWarning" resname="clip_deleteMarkedWarning" approved="yes">
        <source>Are you sure you want to delete all marked records from the table '%s'?</source>
        <target state="final">Alle markieren Datensätze tatsächlich aus der Tabelle "%s" löschen?</target>
      </trans-unit>
      <trans-unit id="showInfo" resname="showInfo" approved="yes">
        <source>Display information</source>
        <target state="final">Informationen anzeigen</target>
      </trans-unit>
      <trans-unit id="edit" resname="edit" approved="yes">
        <source>Edit record</source>
        <target state="final">Datensatz bearbeiten</target>
      </trans-unit>
      <trans-unit id="editPage" resname="editPage" approved="yes">
        <source>Edit page properties</source>
        <target state="final">Seiteneigenschaften bearbeiten</target>
      </trans-unit>
      <trans-unit id="new" resname="new" approved="yes">
        <source>New record</source>
        <target state="final">Datensatz erstellen</target>
      </trans-unit>
      <trans-unit id="newPage" resname="newPage" approved="yes">
        <source>Create new page after this page</source>
        <target state="final">Neue Seite nach dieser Seite erstellen</target>
      </trans-unit>
      <trans-unit id="newRecord" resname="newRecord" approved="yes">
        <source>Create new record after this record</source>
        <target state="final">Neuen Datensatz nach diesem Datensatz erstellen</target>
      </trans-unit>
      <trans-unit id="newRecordGeneral" resname="newRecordGeneral" approved="yes">
        <source>Create new record</source>
        <target state="final">Neuen Datensatz erstellen</target>
      </trans-unit>
      <trans-unit id="moveUp" resname="moveUp" approved="yes">
        <source>Move up in list</source>
        <target state="final">Nach oben verschieben</target>
      </trans-unit>
      <trans-unit id="moveDown" resname="moveDown" approved="yes">
        <source>Move down in list</source>
        <target state="final">Nach unten verschieben</target>
      </trans-unit>
      <trans-unit id="move_record" resname="move_record" approved="yes">
        <source>Re-position content element</source>
        <target state="final">Datensatz verschieben</target>
      </trans-unit>
      <trans-unit id="move_page" resname="move_page" approved="yes">
        <source>Move page</source>
        <target state="final">Seite verschieben</target>
      </trans-unit>
      <trans-unit id="hide" resname="hide" approved="yes">
        <source>Hide record</source>
        <target state="final">Datensatz verbergen</target>
      </trans-unit>
      <trans-unit id="unHide" resname="unHide" approved="yes">
        <source>Un-hide record</source>
        <target state="final">Datensatz sichtbar machen</target>
      </trans-unit>
      <trans-unit id="hidePage" resname="hidePage" approved="yes">
        <source>Hide page</source>
        <target state="final">Seite verbergen</target>
      </trans-unit>
      <trans-unit id="unHidePage" resname="unHidePage" approved="yes">
        <source>Un-hide page</source>
        <target state="final">Seite sichtbar machen</target>
      </trans-unit>
      <trans-unit id="prevLevel" resname="prevLevel" approved="yes">
        <source>Move this page to the position after the current parent page (Inwards)</source>
        <target state="final">Diese Seite hinter die derzeit übergeordnete Seite verschieben (einwärts)</target>
      </trans-unit>
      <trans-unit id="nextLevel" resname="nextLevel" approved="yes">
        <source>Move this page to be a subpage of the page above (Outwards)</source>
        <target state="final">Diese Seite als Unterseite in die derzeit vorhergehende Seite verschieben (auswärts)</target>
      </trans-unit>
      <trans-unit id="delete" resname="delete" approved="yes">
        <source>Delete record (!)</source>
        <target state="final">Datensatz löschen(!)</target>
      </trans-unit>
      <trans-unit id="history" resname="history" approved="yes">
        <source>Display change history / Un-do</source>
        <target state="final">Änderungsverlauf anzeigen/Rückgängig</target>
      </trans-unit>
      <trans-unit id="displayVersions" resname="displayVersions" approved="yes">
        <source>Display versions</source>
        <target state="final">Versionen anzeigen</target>
      </trans-unit>
      <trans-unit id="permissions" resname="permissions" approved="yes">
        <source>Set permissions for page</source>
        <target state="final">Zugriffsrechte für Seite festlegen</target>
      </trans-unit>
      <trans-unit id="largeControl" resname="largeControl" approved="yes">
        <source>Extended view</source>
        <target state="final">Erweiterte Ansicht</target>
      </trans-unit>
      <trans-unit id="contractView" resname="contractView" approved="yes">
        <source>List all tables</source>
        <target state="final">Alle Tabellen anzeigen</target>
      </trans-unit>
      <trans-unit id="expandView" resname="expandView" approved="yes">
        <source>List only this table</source>
        <target state="final">Nur diese Tabelle anzeigen</target>
      </trans-unit>
      <trans-unit id="collapseExpandTable" resname="collapseExpandTable" approved="yes">
        <source>Collapse and expand records of table "%s"</source>
        <target state="final">Datensätze der Tabelle "%s" ein-/ausklappen</target>
      </trans-unit>
      <trans-unit id="showClipBoard" resname="showClipBoard" approved="yes">
        <source>Show clipboard</source>
        <target state="final">Zwischenablage anzeigen</target>
      </trans-unit>
      <trans-unit id="editShownColumns" resname="editShownColumns" approved="yes">
        <source>Edit all shown fields of the listed records</source>
        <target state="final">Alle angezeigten Felder der aufgeführten Datensätze bearbeiten</target>
      </trans-unit>
      <trans-unit id="editThisColumn" resname="editThisColumn" approved="yes">
        <source>Edit the '%s' field of the listed records</source>
        <target state="final">Das Feld "%s" der aufgeführten Datensätze bearbeiten</target>
      </trans-unit>
      <trans-unit id="deleteWarning" resname="deleteWarning" approved="yes">
        <source>Are you sure you want to delete the record '%s'?</source>
        <target state="final">Sind Sie sicher, dass Sie den Datensatz '%s' löschen möchten?</target>
      </trans-unit>
      <trans-unit id="localization" resname="localization" approved="yes">
        <source>Localization view</source>
        <target state="final">Lokalisierungsansicht</target>
      </trans-unit>
      <trans-unit id="defaultLanguage" resname="defaultLanguage" approved="yes">
        <source>Default</source>
        <target state="final">Standard</target>
      </trans-unit>
      <trans-unit id="multipleLanguages" resname="multipleLanguages" approved="yes">
        <source>All languages</source>
        <target state="final">Alle Sprachen</target>
      </trans-unit>
      <trans-unit id="Localize" resname="Localize" approved="yes">
        <source>Localize to</source>
        <target state="final">Lokalisieren</target>
      </trans-unit>
      <trans-unit id="viewRecords" resname="viewRecords" approved="yes">
        <source>Viewing records of</source>
        <target state="final">Anzeige der Datensätze von</target>
      </trans-unit>
      <trans-unit id="pageIndicator" resname="pageIndicator" approved="yes">
        <source>Page %1$s of %2$d</source>
        <target state="final">Seite %1$s von %2$d</target>
      </trans-unit>
      <trans-unit id="rangeIndicator" resname="rangeIndicator" approved="yes">
        <source>Records %1$d - %2$d</source>
        <target state="final">Datensätze %1$d - %2$d</target>
      </trans-unit>
      <trans-unit id="missingTcaColumnsMessageTitle" resname="missingTcaColumnsMessageTitle" approved="yes">
        <source>Missing TCA definition</source>
        <target state="final">TCA-Definition fehlt</target>
      </trans-unit>
      <trans-unit id="missingTcaColumnsMessage" resname="missingTcaColumnsMessage" approved="yes">
        <source>No fields for table "%s" have been found. $TCA['%s']['columns'] does not contain the necessary definitions.</source>
        <target state="final">Für die Tabelle "%s" wurden keine Felder gefunden. $TCA['%s']['columns'] enthält nicht die notwendigen Definitionen.</target>
      </trans-unit>
      <trans-unit id="noRecordsOnThisPage" resname="noRecordsOnThisPage" approved="yes">
        <source>There are no records on this page.</source>
        <target state="final">Es sind keine Datensätze auf dieser Seite.</target>
      </trans-unit>
      <trans-unit id="noRecordsOfTypeOnThisPage" resname="noRecordsOfTypeOnThisPage" approved="yes">
        <source>There are no records of type "%s" on this page.</source>
        <target state="final">Es gibt keine Datensätze vom Typ "%s" auf dieser Seite.</target>
      </trans-unit>
      <trans-unit id="button.cancel" resname="button.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbrechen</target>
      </trans-unit>
      <trans-unit id="button.close" resname="button.close" approved="yes">
        <source>Close</source>
        <target state="final">Schließen</target>
      </trans-unit>
      <trans-unit id="button.ok" resname="button.ok" approved="yes">
        <source>OK</source>
        <target state="final">OK</target>
      </trans-unit>
      <trans-unit id="button.delete" resname="button.delete" approved="yes">
        <source>Delete</source>
        <target state="final">Löschen</target>
      </trans-unit>
    </body>
  </file>
</xliff>
