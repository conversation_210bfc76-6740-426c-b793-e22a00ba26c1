<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:info/Resources/Private/Language/locallang_mod_web_info.xlf" date="2011-10-17T20:22:34Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="title" resname="title" approved="yes">
        <source>Page information</source>
        <target state="final">Seiteninformation</target>
      </trans-unit>
      <trans-unit id="message" resname="message" approved="yes">
        <source>There are no page information available.</source>
        <target state="final">Es sind keine Seiteninformationen verfügbar.</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tablabel" resname="mlang_labels_tablabel" approved="yes">
        <source>Page related information, eg. hit statistics, change log, record counts</source>
        <target state="final">Seitenbezogene Informationen, z.B. Zugriffsstatistik, Änderungsprotokollierung, Datensatzanzahl</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tabdescr" resname="mlang_labels_tabdescr" approved="yes">
        <source>The Web&gt;Info module is focused on statistic information about pages. This includes statistics of pagehits and a changelog allowing browsing of page history changes. An overview of the number of records on each page as well as page settings presented in a classic tree structure is provided.</source>
        <target state="final">Das Modul Web &gt; Info ist auf statistische Informationen über Web-Seiten spezialisiert. Dies beinhaltet Statistiken über Seitenzugriffe und eine Änderungsprotokollierung, über die man sich durch alle Änderungen der Seite bewegen kann. Es steht sowohl eine Übersicht der verwendeten Datensätze auf den einzelnen Seiten als auch der Seiteneinstellungen in Form einer Baumansicht zur Verfügung.</target>
      </trans-unit>
      <trans-unit id="mlang_tabs_tab" resname="mlang_tabs_tab" approved="yes">
        <source>Info</source>
        <target state="final">Info</target>
      </trans-unit>
    </body>
  </file>
</xliff>
