FROM alpine

RUN apk add -U\
    bash\
    binutils\
    ca-certificates\
    coreutils\
    curl\
    findutils\
    g++\
    gcc\
    grep\
    libgcc\
    libstdc++\
    libxml2-utils\
    linux-headers\
    make\
    ncurses\
    openssl\
    python3\
    util-linux\
    yarn

RUN touch $HOME/.profile\
    && echo 'export NVM_NODEJS_ORG_MIRROR=https://unofficial-builds.nodejs.org/download/release;' >> $HOME/.profile\
    && curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.7/install.sh | bash\
    && source $HOME/.profile\
    && nvm install v20.10.0\
    && npm install -g npm@10
